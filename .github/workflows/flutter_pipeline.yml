name: Flutter CI

on:
  pull_request:
    branches:
      - develop
      - master

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.29.3'
          cache: true

      - name: Install dependencies
        run: flutter pub get

      - name: Run Flutter analyze
        run: flutter analyze

      - name: Run Flutter tests
        run: flutter test
