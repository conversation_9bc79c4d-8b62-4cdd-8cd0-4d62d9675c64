{"BaseModel": {"scope": "dart", "prefix": "bmodel", "body": ["import 'package:equatable/equatable.dart';", "import 'package:json_annotation/json_annotation.dart';", "", "part '${TM_FILENAME/(.*)\\..+$/$1/}.g.dart';", "", "@JsonSerializable()", "class $1 extends Equatable {", "  const $1();", "", "  factory $1.fromJson(Map<String, dynamic> json) => _$$1FromJson(json);", "  Map<String, dynamic> toJson() => _$$1ToJson(this);", "", "  @override", "  List<Object?> get props => [];", "}"], "description": "Class model with Equatable and JsonSerializable"}, "SubModel": {"scope": "dart", "prefix": "smodel", "body": ["@JsonSerializable()", "class $1 extends Equatable {", "  const $1();", "", "  factory $1.fromJson(Map<String, dynamic> json) => _$$1FromJson(json);", "  Map<String, dynamic> toJson() => _$$1ToJson(this);", "", "  @override", "  List<Object?> get props => [];", "}"], "description": "Class model with Equatable and JsonSerializable"}, "PartFileG": {"scope": "dart", "prefix": "partg", "body": ["part '${TM_FILENAME/(.*)\\..+$/$1/}.g.dart';"], "description": "Part file"}}