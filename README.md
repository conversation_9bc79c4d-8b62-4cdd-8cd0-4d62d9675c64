# pic_sketch

A new Flutter project.


## Hướng dẫn setup Fastlane tích hợp Firebase App Distribution

run lệnh này để cài đặt fastlane (nếu máy tính chưa cài)
```
brew install fastlane
```

Cài đặt plugin Firebase App Distribution cho Fastlane.
cd vào thư mục `android` hoặc `ios`, sau đó run lệnh
```
fastlane add_plugin firebase_app_distribution
```

Cài đặt Firebase CLI (nếu máy tính chưa cài)
```
curl -sL https://firebase.tools | bash
```

Chạy và login vào Firebase để get refresh token, copy toàn bộ dòng token lưu nó vào `/android/.env`, hoặc `ios/.env` (Nếu có sẵn ở các project khác rồi thì copy qua cũng được)
```
firebase login:ci
```

Copy code trong Fastfile từ các project trước đó qua nếu đã có sẵn logic.


