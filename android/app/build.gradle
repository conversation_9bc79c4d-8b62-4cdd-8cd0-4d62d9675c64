plugins {
    id "com.android.application"
    id "kotlin-android"
    id 'com.google.gms.google-services'
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace = "vn.quanghuuxx.pic_sketch"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
        coreLibraryDesugaringEnabled true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "vn.quanghuuxx.pic_sketch"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        multiDexEnabled true
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.debug
        }
    }

    flavorDimensions "default"

    productFlavors {
        development {
            dimension "default"
            resValue "string", "app_name", "[DEV]Pic Sketch"
            // resValue "string", "app_ad_mob_id", "ca-app-pub-3940256099942544~3347511713"
            applicationIdSuffix ".dev" 
        }

        production {
            dimension "default"
            resValue "string", "app_name", "Pic Sketch"
            // resValue "string", "app_ad_mob_id", "ca-app-pub-4576350435800254~1897434476"
        }

        onepait {
            dimension "default"
            resValue "string", "app_name", "[ONEPAIT]Pic Sketch"
            // resValue "string", "app_ad_mob_id", "ca-app-pub-4576350435800254~1897434476"
            applicationId "vn.quanghuuxx.onepait"
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
  implementation platform('com.google.firebase:firebase-bom:33.12.0')
  implementation 'com.google.firebase:firebase-analytics-ktx'
  // for dependency 'flutter_local_notifications'
  coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'
}