# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

platform :android do
  desc "Build và upload lên Firebase App Distribution Dev"
  lane :distribution_dev do
    sh("flutter clean")
    sh("flutter build apk --flavor development -t lib/main_dev.dart")

    changelog = sh("git log --pretty=format:'%h - %s' --no-merges -n 5")
    
    firebase_app_distribution(
      app: ENV["FIREBASE_DEV_APP_ID"],
      firebase_cli_token: ENV["FIREBASE_TOKEN"],
      apk_path: "../build/app/outputs/flutter-apk/app-development-release.apk",
      groups: "testers",
      release_notes: changelog
    )
  end

  lane :distribution_prod do
    sh("flutter clean")
    sh("flutter build apk --flavor production -t lib/main.dart")

    changelog = sh("git log --pretty=format:'%h - %s' --no-merges -n 5")

    firebase_app_distribution(
      app: ENV["FIREBASE_PROD_APP_ID"],
      firebase_cli_token: ENV["FIREBASE_TOKEN"],
      apk_path: "../build/app/outputs/flutter-apk/app-production-release.apk",
      groups: "testers",
      release_notes: changelog
    )
  end

  lane :distribution_onepait do
    sh("flutter clean")
    sh("flutter build apk --flavor onepait -t lib/main_dev.dart")

    changelog = sh("git log --pretty=format:'%h - %s' --no-merges -n 5")
    
    firebase_app_distribution(
      app: ENV["FIREBASE_ONEPAIT_APP_ID"],
      firebase_cli_token: ENV["FIREBASE_TOKEN"],
      apk_path: "../build/app/outputs/flutter-apk/app-onepait-release.apk",
      groups: "testers",
      release_notes: changelog
    )
  end
end
