<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="fastlane.lanes">
    
    
    
      
      <testcase classname="fastlane.lanes" name="0: default_platform" time="0.000194">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="1: flutter clean" time="0.878313">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="2: flutter build apk --flavor production -t lib/main.dart" time="119.24107">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="3: git log --pretty=format:&apos;%h - %s&apos; --no-merges -n 5" time="0.017875">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="4: firebase_app_distribution" time="15.75941">
        
      </testcase>
    
  </testsuite>
</testsuites>
