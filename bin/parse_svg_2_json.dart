// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:convert';
import 'dart:io';

Map<String, dynamic> parseSvgTo<PERSON>son(final String key, final String svgContent) {
  // Khởi tạo kết quả JSON
  Map<String, dynamic> result = {
    'asset': key,
  };

  // Trích xuất width và height từ thẻ svg
  RegExp sizeExp = RegExp(r'width="(\d+)"\s+height="(\d+)"');
  Match? sizeMatch = sizeExp.firstMatch(svgContent);
  if (sizeMatch != null) {
    result['width'] = int.parse(sizeMatch.group(1)!);
    result['height'] = int.parse(sizeMatch.group(2)!);
  }

  // Trích xuất các path
  RegExp pathExp = RegExp(r'<path\s+d="([^"]+)"\s+(fill|stroke)="([^"]+)"\s*/>', multiLine: true);
  Iterable<Match> pathMatches = pathExp.allMatches(svgContent);

  List<Map<String, String>> paths = [];
  for (Match match in pathMatches) {
    String styleType = match.group(2)!; // "fill" hoặc "stroke"
    paths.add(
      {
        'style': styleType,
        'path': match.group(1)!,
        'color': match.group(3)!, // Giá trị của fill hoặc stroke
      },
    );
  }

  result['paths'] = paths;

  return result;
}

void main() {
  // SVG input của bạn
  String key = 'assets/shapes/hexagonal.png';
  String svgInput = '''
<svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M50 0L64.75 24.4523L93.3013 25L79.5 50L93.3013 75L64.75 75.5477L50 100L35.25 75.5477L6.69873 75L20.5 50L6.69873 25L35.25 24.4523L50 0Z" fill="black"/>
</svg>
''';

  Map<String, dynamic> jsonResult = parseSvgToJson(key, svgInput);
  if (jsonResult['paths'].length == 0) {
    stdout.write('No path found');
    return;
  }

  final file = File('assets/jsons/shapes.json');

  Map<String, dynamic> content = {};
  if (file.existsSync()) {
    content = json.decode(file.readAsStringSync()) as Map<String, dynamic>;
    final list = (content['shapes'] as List);
    final indexed = list.indexWhere((m) => m['asset'] == jsonResult['asset']);
    if (indexed == -1) {
      list.add(jsonResult);
    } else {
      list[indexed] = jsonResult;
    }
  } else {
    content = {
      'last_modified': DateTime.now().toIso8601String(),
      'shapes': [jsonResult],
    };

    file.createSync(recursive: true);
  }

  const encoder = JsonEncoder.withIndent('  ');
  String jsonString = encoder.convert(content);

  file.writeAsStringSync(jsonString);
}
