# Language code of target to translate
target_locales:
  - ja
  - fr
  - zh-cn
  - vi

# File name of dart class key localized 
dart_lkey_name: L<PERSON>ey

# Directory output json translated files
output_dir: assets/l10n

# Directory output LKey dart file
output_file_dart_lkey: lib/resource/localization/lkey.dart

sources:
  - 
    source: assets/l10n/en_lang.json # This is path link to file json with contents source for translate
    source_locale: en # Language code of source translate
    prefix_json_name: "" # (Optional) Prefix and suffix of json name, there was join with `-` 
    suffix_json_name: "lang"
  - 
    source: assets/l10n/en_error_lang.json
    source_locale: en
    prefix_json_name: ""
    suffix_json_name: "error_lang"