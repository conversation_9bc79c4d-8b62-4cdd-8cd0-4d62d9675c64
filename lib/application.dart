// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:it/it.dart';

import 'component/dependency_injector/di.dart';
import 'component/services/remote_config/remote_config_service.dart';
import 'component/services/remote_config/remote_configuration.dart';
import 'component/widget/inherited_consumer.dart';
import 'configurations/configurations.dart';
import 'resource/localization/app_localization.dart';
import 'resource/style/color/dark.dart';
import 'resource/style/color/light.dart';
import 'resource/style/decoration/app_theme_decoration.dart';
import 'resource/style/text/app_theme_text.dart';
import 'routes/routes.dart';

class Application {
  static void main(Env env, [String? name]) async {
    WidgetsFlutterBinding.ensureInitialized();

    _registerTypography();

    await Firebase.initializeApp();

    // load environment and configurations
    await env.load();
    configurations.load(env);

    await it.setupDependencies();
    await PathUtil.initial();

    // initialed for remote-config
    await RemoteConfigService.instance.fetchRemoteConfig(
      defaults: RemoteConfiguration.kDefaultConfigValues,
    );
    RemoteConfigService.instance.addListeneConfigUpdated();

    // initialed for list route app
    Routes.instance.init();
    // set enable log for debug
    Console.enable(configurations.devMode);

    _runApp(name);
  }

  static void _runApp([String? name]) {
    final theme = ThemeData(
      pageTransitionsTheme: const PageTransitionsTheme(
        builders: {
          TargetPlatform.android: CupertinoPageTransitionsBuilder(),
          TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        },
      ),
    );

    runApp(
      _Application(
        key: ValueKey(name),
        light: theme.withAppTheme(
          brightness: Brightness.light,
          themeColor: LightColor.instance,
          themeText: AppThemeText.withThemeColor(LightColor.instance),
          themeDecoration: AppThemeDecoration.withThemeColor(LightColor.instance),
        ),
        dark: theme.withAppTheme(
          brightness: Brightness.dark,
          themeColor: DarkColor.instance,
          themeText: AppThemeText.withThemeColor(DarkColor.instance),
          themeDecoration: AppThemeDecoration.withThemeColor(DarkColor.instance),
        ),
      ),
    );
  }

  static void _registerTypography() {
    AppTypography.registerFonts({
      'Inter': [
        const FontVariant(
          fontWeight: FontWeight.w100,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Inter/Inter-Thin.ttf',
        ),
        const FontVariant(
          fontWeight: FontWeight.w200,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Inter/Inter-ExtraLight.ttf',
        ),
        const FontVariant(
          fontWeight: FontWeight.w300,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Inter/Inter-Light.ttf',
        ),
        const FontVariant(
          fontWeight: FontWeight.w400,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Inter/Inter-Regular.ttf',
        ),
        const FontVariant(
          fontWeight: FontWeight.w500,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Inter/Inter-Medium.ttf',
        ),
        const FontVariant(
          fontWeight: FontWeight.w600,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Inter/Inter-SemiBold.ttf',
        ),
        const FontVariant(
          fontWeight: FontWeight.w700,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Inter/Inter-Bold.ttf',
        ),
        const FontVariant(
          fontWeight: FontWeight.w800,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Inter/Inter-ExtraBold.ttf',
        ),
        const FontVariant(
          fontWeight: FontWeight.w900,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Inter/Inter-Black.ttf',
        ),
      ],
      'Caveat': [
        const FontVariant(
          fontWeight: FontWeight.w400,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Caveat/Caveat-Regular.ttf',
        ),
      ],
      'Cookie': [
        const FontVariant(
          fontWeight: FontWeight.w400,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Cookie/Cookie-Regular.ttf',
        ),
      ],
      'DynaPuff': [
        const FontVariant(
          fontWeight: FontWeight.w400,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/DynaPuff/DynaPuff-Regular.ttf',
        ),
      ],
      'Gaegu': [
        const FontVariant(
          fontWeight: FontWeight.w400,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Gaegu/Gaegu-Regular.ttf',
        ),
      ],
      'Jua': [
        const FontVariant(
          fontWeight: FontWeight.w400,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Jua/Jua-Regular.ttf',
        ),
      ],
      'Moirai_One': [
        const FontVariant(
          fontWeight: FontWeight.w400,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Moirai_One/MoiraiOne-Regular.ttf',
        ),
      ],
      'Ole': [
        const FontVariant(
          fontWeight: FontWeight.w400,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Ole/Ole-Regular.ttf',
        ),
      ],
      'Outfit': [
        const FontVariant(
          fontWeight: FontWeight.w400,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Outfit/Outfit-Regular.ttf',
        ),
      ],
      'Puppies_Play': [
        const FontVariant(
          fontWeight: FontWeight.w400,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Puppies_Play/PuppiesPlay-Regular.ttf',
        ),
      ],
      'Rampart_One': [
        const FontVariant(
          fontWeight: FontWeight.w400,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Rampart_One/RampartOne-Regular.ttf',
        ),
      ],
      'Rouge_Script': [
        const FontVariant(
          fontWeight: FontWeight.w400,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Rouge_Script/RougeScript-Regular.ttf',
        ),
      ],
      'Silkscreen': [
        const FontVariant(
          fontWeight: FontWeight.w400,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Silkscreen/Silkscreen-Regular.ttf',
        ),
      ],
      'Mustachio_CF': [
        const FontVariant(
          fontWeight: FontWeight.w400,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Mustachio_CF/mustachio-cf.ttf',
        ),
      ],
      'Rainbow_Ribbon_CF': [
        const FontVariant(
          fontWeight: FontWeight.w400,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Rainbow_Ribbon_CF/rainbow-ribbon-cf.ttf',
        ),
      ],
      'Vintage_Grovee': [
        const FontVariant(
          fontWeight: FontWeight.w400,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Vintage_Grovee/vintage-grovee.ttf',
        ),
      ],
      'Blackout_2am': [
        const FontVariant(
          fontWeight: FontWeight.w400,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Blackout_2am/Blackout-2am.ttf',
        ),
      ],
      'Ice_Age': [
        const FontVariant(
          fontWeight: FontWeight.w400,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Ice_Age/ice-age.ttf',
        ),
      ],
      'Baloo2': [
        const FontVariant(
          fontWeight: FontWeight.w400,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Baloo2/Baloo2-Regular.ttf',
        ),
        const FontVariant(
          fontWeight: FontWeight.w500,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Baloo2/Baloo2-Medium.ttf',
        ),
        const FontVariant(
          fontWeight: FontWeight.w600,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Baloo2/Baloo2-SemiBold.ttf',
        ),
        const FontVariant(
          fontWeight: FontWeight.w700,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Baloo2/Baloo2-Bold.ttf',
        ),
        const FontVariant(
          fontWeight: FontWeight.w800,
          fontStyle: FontStyle.normal,
          uri: 'assets/fonts/Baloo2/Baloo2-ExtraBold.ttf',
        ),
      ],
    });
  }
}

class _Application extends StatelessWidget {
  const _Application({
    super.key,
    required this.light,
    required this.dark,
  });

  final ThemeData light;
  final ThemeData dark;

  @override
  Widget build(BuildContext context) {
    return ProviderScope(
      child: MaterialApp(
        debugShowCheckedModeBanner: false,
        theme: light,
        darkTheme: dark,
        scrollBehavior: ScrollConfiguration.of(context).copyWith(
          physics: const BouncingScrollPhysics(),
          overscroll: false,
        ),
        themeMode: ThemeMode.dark,
        initialRoute: Routes.splash,
        onGenerateRoute: Routes.instance.onGenerateRoute,
        navigatorKey: NavigatorObsService.key,
        navigatorObservers: [NavigatorObsService.instance],
        localeResolutionCallback: (locale, supports) {
          if (supports.any((e) => e.languageCode == locale?.languageCode)) {
            return locale;
          }
          return AppLocalizations.defaultLocale;
        },
        localizationsDelegates: [
          AppLocalizations.delegate,

          // defaults
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: AppLocalizations.delegate.supportedLocales,
        builder: (context, child) {
          // for riverpod provider with context
          return InheritedConsumer(
            // for listenable with context
            child: InheritedListenable(
              child: It(child: child!),
            ),
          );
        },
      ),
    );
  }
}
