// Flutter project by quanghu<PERSON><PERSON> (<EMAIL>)

import 'dart:async';

import 'package:core/core.dart';
import 'package:get_it/get_it.dart';

import '../../data/data.dart';
import '../../features/editor/interactor/editor_interactor.dart';
import '../../features/editor/providers/floating_bar_viewmodel.dart';
import '../../features/editor/repository/edit_tooltip_repository.dart';
import '../../features/editor/repository/editor_repository.dart';
import '../../features/image_picker/repository/image_picker_repository.dart';
import '../../features/image_picker/repository/impl/image_picker_repository.impl.dart';
import '../../features/preload/interactor/preload_interactor.dart';
import '../../features/preload/repository/preload_repository.dart';
import '../../features/shared/request_permision/service/request_permission_service.dart';
import '../../objectbox.g.dart';
import '../services/alert_dialog_service/alert_dialog_service.dart';

part 'di_boxs.dart';
part 'di_interactors.dart';
part 'di_repositories.dart';
part 'di_services.dart';
part 'di_viewmodel.dart';

GetIt it = GetIt.instance;

extension DependencyInjector on GetIt {
  FutureOr<void> setupDependencies() async {
    await _injectServices();
    await _injectBoxStores();
    await _injectRepositories();
    await _injectInteractors();
    await _injectViewModels();
  }
}
