// Flutter project by quanghuuxx (<EMAIL>)

part of 'di.dart';

FutureOr<void> _injectRepositories() {
  it
    ..registerFactory<ImagePickerRepository>(
      () => ImagePickerRepositoryImpl(),
    )
    ..registerFactory<EditTooltipRepository>(
      () => EditTooltipRepositoryImpl(
        preferences: it.get(),
      ),
    )
    ..registerFactory<PreloadRepository>(
      () => PreloadRepositoryImpl(
        picTemplateBox: it.get(),
      ),
    )
    ..registerFactory<EditorRepository>(
      () => EditorRepositoryImpl(
        picTemplateBox: it.get(),
        preferences: it.get(),
      ),
    );
}
