// Flutter project by quanghuuxx (<EMAIL>)

part of 'di.dart';

FutureOr<void> _injectServices() async {
  await SharedPreferencesService.instance.initialization();
  await PackageInfo.instance.initialization();

  it
    ..registerLazySingleton(() => PackageInfo.instance)
    ..registerLazySingleton<AppPreferences>(() => AppPreferencesImpl(SharedPreferencesService.instance))
    ..registerLazySingleton<RequestPermissionService>(
      () => RequestPermissionServiceImpl(
        preferences: it.get(),
      ),
    )
    ..registerLazySingleton<AlertDialogService>(() => AlertDialogService());
}
