// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/widgets.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

abstract class Refresher {
  void refreshCompleted([bool? noMore]);
}

mixin RefresherStateMixin<T extends StatefulWidget> on State<T> implements Refresher {
  final RefreshController refreshController = RefreshController();

  @override
  void refreshCompleted([bool? noMore]) {
    if (refreshController.isRefresh) {
      refreshController.refreshCompleted();
    } else if (refreshController.isLoading) {
      refreshController.loadComplete();
    }

    if (noMore == true) {
      refreshController.loadNoData();
    }
  }

  @override
  void dispose() {
    refreshController.dispose();
    super.dispose();
  }
}
