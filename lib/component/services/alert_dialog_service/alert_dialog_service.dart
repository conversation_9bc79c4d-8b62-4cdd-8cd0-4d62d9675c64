// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import 'model/loading_dialog.dart';
import 'widget/alert_dialog_widget.dart';

part 'model/base/alert_dialog_action.dart';
part 'model/base/alert_dialog_content.dart';
part 'model/base/alert_dialog_info.dart';

class AlertDialogService extends AlertService {
  AlertDialogService._() : super(builder: AlertDialogBuiler());

  static AlertDialogService instance = AlertDialogService._();

  factory AlertDialogService() {
    return instance;
  }
}

class AlertDialogBuiler extends AlertBuilder<AlertDialogInfo> {
  @override
  BuildContext get context => NavigatorObsService.instance.context;

  @override
  Future<T?> build<T>(AlertDialogInfo info) {
    return showGeneralDialog<T>(
      context: context,
      barrierDismissible: info.barrierDismissible,
      barrierLabel: 'barrierLabel',
      pageBuilder: (context, animation, secondAnimation) {
        if (info.viewGenerator != null) {
          return info.viewGenerator!.call(context);
        }

        if (info is LoadingDialog) {
          return const LoadingWidget();
        }

        return AlertDialogWidget(info: _wapperAlertDialogInfo(info));
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return PopScope(canPop: info.barrierDismissible, child: child);
      },
    );
  }

  AlertDialogInfo _wapperAlertDialogInfo(AlertDialogInfo info) {
    for (var i = 0; i < info.actions.length; i++) {
      var action = info.actions[i];
      if (action.action == null) {
        info.actions[i] = action = action.copyWith(action: (context) => context.pop());
      }
    }

    return info;
  }

  @override
  void closeCurrentAlert() {
    return context.pop();
  }
}
