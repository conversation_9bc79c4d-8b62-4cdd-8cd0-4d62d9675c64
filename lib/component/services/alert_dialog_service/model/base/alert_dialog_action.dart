// Sunday, 27th August 2023 08:30 PM
// quanghuuxx (<EMAIL>)
// -----
// Copyright 2023 quanghuuxx, Ltd. All rights reserved.

part of '../../alert_dialog_service.dart';

class AlertDialogAction extends Equatable {
  final String title;
  final ValueChanged<BuildContext>? action;
  AlertDialogAction({
    required this.title,
    this.action,
  });

  @override
  List<Object?> get props => [title, action];

  AlertDialogAction copyWith({
    String? title,
    ValueChanged<BuildContext>? action,
  }) {
    return AlertDialogAction(
      title: title ?? this.title,
      action: action ?? this.action,
    );
  }
}
