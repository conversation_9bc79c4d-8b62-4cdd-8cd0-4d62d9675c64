// Flutter project by quang<PERSON>uxx (<EMAIL>)

part of '../../alert_dialog_service.dart';

class AlertDialogContent<T extends Object> extends Equatable {
  final T content;
  final AlertDialogContentType type;

  /// [argument] hiện tại đang được dùng như:
  ///  [TextStyle] đối với [AlertDialogContentType.text]
  ///  [Color] đối với [AlertDialogContentType.image]
  final Object? argument;

  AlertDialogContent({
    required this.content,
    this.argument,
    required this.type,
  });

  @override
  List<Object?> get props => [content, argument, type];
}

enum AlertDialogContentType {
  widget,
  text,
  image,
}
