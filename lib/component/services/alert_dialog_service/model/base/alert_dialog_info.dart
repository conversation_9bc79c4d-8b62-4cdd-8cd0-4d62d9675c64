// Sunday, 27th August 2023 08:29 PM
// quanghuuxx (<EMAIL>)
// -----
// Copyright 2023 quanghuuxx, Ltd. All rights reserved.

part of '../../alert_dialog_service.dart';

class AlertDialogInfo extends AlertInfo {
  final List<AlertDialogContent> contents;
  final List<AlertDialogAction> actions;
  final bool barrierDismissible;

  AlertDialogInfo({
    super.viewGenerator,
    required this.contents,
    required this.actions,
    super.piority = AlertPiority.medium,
    this.barrierDismissible = false,
  });

  AlertDialogInfo copyWith({
    List<AlertDialogContent>? contents,
    List<AlertDialogAction>? actions,
    bool? barrierDismissible,
  }) {
    return AlertDialogInfo(
      contents: contents ?? this.contents,
      actions: actions ?? this.actions,
      barrierDismissible: barrierDismissible ?? this.barrierDismissible,
    );
  }
}
