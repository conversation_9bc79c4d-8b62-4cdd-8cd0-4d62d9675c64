// Flutter project by quanghuuxx (<EMAIL>)

// Wednesday, 6th September 2023 10:19 PM
// quanghuuxx (<EMAIL>)
// -----
// Copyright 2023 quanghuuxx, Ltd. All rights reserved.

import 'package:core/core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../../../resource/localization/lkey.dart';
import '../../../../resource/style/app_theme_ext.dart';
import '../alert_dialog_service.dart';

class ErrorDialogInfo extends AlertDialogInfo {
  ErrorDialogInfo.fromBaseException(
    BuildContext context, {
    required BaseException exception,
    List<AlertDialogContent>? contents,
    List<AlertDialogAction>? actions,
  }) : super(
          contents: [
            AlertDialogContent(
              content: context.tr(exception.code),
              type: AlertDialogContentType.text,
              argument: Theme.of(context).themeText.headline6,
            ),
            ...?contents,
            if (kDebugMode && exception.description != null)
              AlertDialogContent(
                content: exception.description!,
                type: AlertDialogContentType.text,
                argument: Theme.of(context).themeText.bodyText1.copyWith(
                      color: Theme.of(context).themeColor.neutral400,
                    ),
              ),
          ],
          actions: actions ??
              [
                AlertDialogAction(title: context.tr(LKey.common_ok)),
              ],
        );
}
