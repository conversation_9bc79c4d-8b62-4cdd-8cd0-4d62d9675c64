// Flutter project by quanghuuxx (<EMAIL>)

// Tuesday, 12th September 2023 10:17 PM
// quanghuuxx (<EMAIL>)
// -----
// Copyright 2023 quanghuuxx, Ltd. All rights reserved.

import 'package:core/core.dart';

import '../alert_dialog_service.dart';

class LoadingDialog extends AlertDialogInfo {
  LoadingDialog()
      : super(
          actions: [],
          contents: [],
          piority: AlertPiority.low,
        );
}
