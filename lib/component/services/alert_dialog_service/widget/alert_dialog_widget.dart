// Flutter project by quanghuuxx (<EMAIL>)

// Sunday, 27th August 2023 08:50 PM
// quanghuuxx (<EMAIL>)
// -----
// Copyright 2023 quanghuuxx, Ltd. All rights reserved.

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../../resource/style/app_theme_ext.dart';
import '../alert_dialog_service.dart';

class AlertDialogWidget extends StatelessWidget {
  const AlertDialogWidget({
    super.key,
    required this.info,
  });

  final AlertDialogInfo info;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      shape: const RoundedRectangleBorder(borderRadius: kBorderRadius12),
      elevation: kZero,
      child: Padding(
        padding: kPaddingAll16,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            ...info.contents.separated(
              (i, e) {
                if (e.type == AlertDialogContentType.text) {
                  return Text(
                    e.content as String,
                    overflow: TextOverflow.clip,
                    textAlign: TextAlign.center,
                    style: e.argument as TextStyle? ?? theme.themeText.bodyText0,
                  );
                } else if (e.type == AlertDialogContentType.image) {
                  return CoreImage(
                    e.content,
                    fit: BoxFit.contain,
                    height: kFifty,
                    color: e.argument as Color?,
                  );
                } else if (e.type == AlertDialogContentType.widget) {
                  return e.content as Widget;
                } else {
                  return const SizedBox.shrink();
                }
              },
              separated: (index) => const SizedBox(height: kTen),
            ),
            const SizedBox(height: kTwenty),
            _AlertButtons(actions: info.actions),
          ],
        ),
      ),
    );
  }
}

class _AlertButtons extends StatelessWidget {
  const _AlertButtons({required this.actions});

  final List<AlertDialogAction> actions;

  @override
  Widget build(BuildContext context) {
    if (actions.isEmpty) {
      return const SizedBox.shrink();
    }

    if (actions.length == 1) {
      return PrimaryButton(
        text: actions.first.title,
        onPressed: () => actions.first.action?.call(context),
      );
    }

    return LayoutBuilder(
      builder: (_, constraints) {
        final width = (constraints.maxWidth - kSixteen) / 2;
        final row = Row(
          children: [
            GhostButton(
              width: width,
              text: actions[0].title,
              onPressed: () => actions[0].action?.call(context),
            ),
            const SizedBox(width: kSixteen),
            PrimaryButton(
              width: width,
              text: actions[1].title,
              onPressed: () => actions[1].action?.call(context),
            ),
          ],
        );

        if (actions.length == 3) {
          return Column(
            children: [
              row,
              const SizedBox(height: kEight),
              SecondaryButton(
                width: width,
                text: actions[2].title,
                onPressed: () => actions[2].action?.call(context),
              ),
            ],
          );
        }
        return row;
      },
    );
  }
}
