// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:async';
import 'dart:convert';

import 'package:core/extentions/extentions.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:rxdart/rxdart.dart';

final class RemoteConfigService {
  RemoteConfigService._();
  static final RemoteConfigService instance = RemoteConfigService._();

  final FirebaseRemoteConfig _config = FirebaseRemoteConfig.instance;
  final BehaviorSubject<Map<String, dynamic>> _stream = BehaviorSubject();

  late Future<bool> _fetchAndActivate;

  Map<String, dynamic> _defaultValues = Map.identity();

  Future<bool> get ensureFetched => _fetchAndActivate;

  Stream<Map<String, dynamic>> get stream => _stream.stream;

  Future<void> fetchRemoteConfig({Map<String, dynamic> defaults = const {}}) async {
    _defaultValues = defaults;

    try {
      await _config.setConfigSettings(
        RemoteConfigSettings(
          fetchTimeout: const Duration(seconds: 10),
          minimumFetchInterval: const Duration(seconds: 15),
        ),
      );

      await (_fetchAndActivate = _config.fetchAndActivate());
    } catch (e) {
      _stream.addError(e..log(tag: 'RemoteConfigService'));
    } finally {
      _decodeConfig();
    }
  }

  StreamSubscription<RemoteConfigUpdate> addListeneConfigUpdated() {
    return _config.onConfigUpdated.listen((_) {
      _config.fetchAndActivate().then((_) => _decodeConfig());
    });
  }

  T get<T>(String key) {
    return _stream.value[key] as T;
  }

  T? maybeGet<T>(String key) {
    return _stream.value[key] as T?;
  }

  void _decodeConfig() {
    Iterable<String> keys = _config.getAll().keys;
    // merge keys + default keys
    keys = <String>{...keys, ..._defaultValues.keys};

    Map<String, dynamic> map = {};
    for (final key in keys) {
      map[key] = _decodeEntry(key, defaultValue: _defaultValues[key]);
    }

    return _stream.add(map);
  }

  T _decodeEntry<T>(String key, {required T defaultValue}) {
    dynamic value = _config.getString(key);

    if (defaultValue is String?) {
      if (value == RemoteConfigValue.defaultValueForString) {
        value = defaultValue;
      }
    } else if (defaultValue is bool?) {
      if (value != 'true' && value != 'false') {
        value = defaultValue;
      } else {
        value = value == 'true';
      }
    } else if (defaultValue is int?) {
      value = int.tryParse(value) ?? defaultValue;
    } else if (defaultValue is double?) {
      value = double.tryParse(value) ?? defaultValue;
    } else if (defaultValue is Map<String, dynamic>?) {
      final encoded = value as String;
      if (encoded.startsWith('{') && encoded.endsWith('}')) {
        value = jsonDecode(encoded) as T;
      } else {
        value = defaultValue;
      }
    } else if (defaultValue is List?) {
      final encoded = value as String;
      if (encoded.startsWith('[') && encoded.endsWith(']')) {
        value = jsonDecode(encoded) as T;
      } else {
        value = defaultValue;
      }
    }

    return value as T;
  }

  void close() {
    _stream.close();
  }
}
