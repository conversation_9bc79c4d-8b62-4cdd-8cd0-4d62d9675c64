// Flutter project by quanghuuxx (<EMAIL>)

class RemoteConfiguration {
  static final Map<String, dynamic> kDefaultConfigValues = {
    RemoteConfiguration.appVersion: '1.0.0',
    RemoteConfiguration.stickerCollections: List<Map<String, dynamic>>.empty(),
    RemoteConfiguration.numExportTemplate: 3,
  };

  static const String appVersion = 'app_version';
  static const String stickerCollections = 'sticker_collections';
  static const String ratingAppShowInterval = 'rating_app_show_interval';
  static const String numExportTemplate = 'num_export_template';

  // ad-mob
  static const String enableAd = 'enable_ad';
  static const String interAdShowInterval = 'inter_ad_show_interval';
  static const String maxFailedLoadAttempts = 'max_failed_load_attempts';
}
