// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:math';

import 'dart:ui';

/// 270 độ radian
const double angle270 = pi * 3 / 2;

/// 90 độ radian
const double angle90 = pi / 2;

/// 180 độ radian
const double angle180 = pi;

/// 360 độ radian
const double angle360 = 2 * pi;

/// Hàm tính toán và trả về Rect bao quanh chính xác của đường cung tròn
///
/// ![](https://flutter.github.io/assets-for-api-docs/assets/dart-ui/path_add_arc.png#gh-light-mode-only)
/// ![](https://flutter.github.io/assets-for-api-docs/assets/dart-ui/path_add_arc_dark.png#gh-dark-mode-only)
Rect getArcBounds(Rect arcRect, double startAngle, double sweepAngle) {
  final List<Offset> points = [];

  // Hàm tính điểm trên đường cung tại một góc cụ thể
  Offset pointAtAngle(double angle) {
    return Offset(
      arcRect.center.dx + arcRect.width / 2 * cos(angle),
      arcRect.center.dy + arcRect.height / 2 * sin(angle),
    );
  }

  // Thêm điểm bắt đầu và kết thúc
  points
    ..add(pointAtAngle(startAngle))
    ..add(pointAtAngle(startAngle + sweepAngle));

  // Kiểm tra các góc cực trị (0, pi/2, pi, 3pi/2)
  for (double extremeAngle in [0, angle90, angle180, angle270]) {
    if (isAngleInSweep(extremeAngle, startAngle, sweepAngle)) {
      points.add(pointAtAngle(extremeAngle));
    }
  }

  // Tính toán bounding box từ tất cả các điểm
  double minX = 999999;
  double minY = 999999;
  double maxX = -999999;
  double maxY = -999999;

  for (final p in points) {
    if (p.dx < minX) minX = p.dx;
    if (p.dy < minY) minY = p.dy;
    if (p.dx > maxX) maxX = p.dx;
    if (p.dy > maxY) maxY = p.dy;
  }

  return Rect.fromLTRB(minX, minY, maxX, maxY);
}

/// Hàm kiểm tra xem một góc có nằm trong khoảng góc quét [sweepAngle] không
bool isAngleInSweep(double angle, double startAngle, double sweepAngle) {
  while (startAngle < 0) {
    startAngle += 2 * pi; // Đảm bảo startAngle dương
  }
  angle = angle % angle360;
  startAngle = startAngle % angle360;

  double endAngle = startAngle + sweepAngle;
  if (sweepAngle >= 0) {
    return (angle >= startAngle && angle <= endAngle) || (endAngle > angle360 && angle <= endAngle % angle360);
  } else {
    return (angle <= startAngle && angle >= endAngle) || (endAngle < 0 && angle >= endAngle + angle360);
  }
}

/// Hàm tính ra bán kính của 1 hình tròn
/// dựa vào góc [radians] và chiều dài đường cung [arcLength] của góc đó
double radiusOf(double radians, double arcLength) {
  if (radians == 0) {
    return 0;
  }
  return arcLength / radians.abs();
}

Path getArcPath(Rect arcRect, double startAngle, double sweepAngle) {
  final path = Path()..addArc(arcRect, startAngle, sweepAngle);
  return path;
}

Rect computeArcRect({
  required double radians,
  required double arcLength,
  Offset focalPoint = Offset.zero,
}) {
  if (radians == 0.0) {
    return Rect.zero;
  }

  final radius = radiusOf(radians, arcLength);

  Offset center;
  if (radians.isNegative) {
    center = focalPoint - Offset(0, radius);
  } else {
    center = focalPoint + Offset(0, radius);
  }

  return Rect.fromCircle(center: center, radius: radius);
}
