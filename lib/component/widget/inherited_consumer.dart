// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:collection';

import 'package:collection/collection.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class _ProviderSubscriptionHolder<T> {
  final Object aspect;
  final ProviderSubscription<T> subscription;
  final bool Function(T?, T)? when;

  _ProviderSubscriptionHolder(
    this.aspect, {
    required this.subscription,
    this.when,
  });

  T? _value;

  T read() {
    final value = subscription.read();
    if (when?.call(_value, value) ?? true) {
      _value = value;
    }
    return _value ?? value;
  }

  bool get closed => subscription.closed;

  void close() {
    subscription.close();
  }

  @override
  bool operator ==(Object other) {
    if (other is _ProviderSubscriptionHolder) {
      return other.aspect == aspect;
    }
    return false;
  }

  @override
  int get hashCode => aspect.hashCode;
}

class InheritedConsumer extends InheritedWidget {
  const InheritedConsumer({super.key, required super.child});

  @override
  InheritedElement createElement() => _InheritProviderElement(this);

  @override
  bool updateShouldNotify(covariant InheritedConsumer oldWidget) => false;

  @protected
  bool isSupportedAspect(Object aspect) => aspect is _ProviderSubscriptionHolder;
}

class _InheritProviderElement extends InheritedElement {
  _InheritProviderElement(InheritedConsumer super.widget);

  final HashSet<Element> _marked = HashSet();

  @override
  void updateDependencies(Element dependent, Object? aspect) {
    assert(aspect != null && aspect is _ProviderSubscriptionHolder);

    if (!_marked.contains(dependent)) {
      _marked.add(dependent);

      final hash = getDependencies(dependent) as HashSet?;
      if (hash?.isNotEmpty == true) {
        for (final holder in hash!) {
          if (holder == aspect) {
            (aspect as _ProviderSubscriptionHolder)._value = holder._value;
          }
          holder.close();
        }
        hash.clear();
      }

      WidgetsBinding.instance.addPostFrameCallback((_) {
        _marked.remove(dependent);
      });
    }

    final hash = getDependencies(dependent) as HashSet?;
    if (hash == null) {
      setDependencies(dependent, HashSet()..add(aspect));
    } else {
      final holder = hash.firstWhereOrNull((e) => e == aspect) as _ProviderSubscriptionHolder?;
      if (holder != null) {
        (aspect as _ProviderSubscriptionHolder)._value = holder._value;
        hash.remove(holder..close());
      }
      hash.add(aspect);
    }
  }

  @override
  void removeDependent(Element dependent) {
    final hash = getDependencies(dependent) as HashSet?;
    if (hash?.isNotEmpty == true) {
      for (final holder in hash!) {
        holder.close();
      }
      hash.clear();
    }

    super.removeDependent(dependent);
  }

  _ProviderSubscriptionHolder<T> get<T>(
    final Element dependent, {
    required Object aspect,
  }) {
    final hash = getDependencies(dependent) as HashSet?;
    if (hash == null) {
      throw StateError('Can`t find holder in scope of $dependent');
    }

    return hash.firstWhere(
      (e) => e.aspect == aspect,
      orElse: () {
        throw StateError('Can`t find holder in scope of $dependent');
      },
    ) as _ProviderSubscriptionHolder<T>;
  }
}

extension ProviderContainerInheritExt on BuildContext {
  T read<T>(ProviderListenable<T> provider) {
    return ProviderScope.containerOf(this, listen: false).read(provider);
  }

  T refresh<T>(Refreshable<T> provider) {
    return ProviderScope.containerOf(this, listen: false).refresh(provider);
  }

  void invalidate(ProviderOrFamily provider) {
    ProviderScope.containerOf(this, listen: false).invalidate(provider);
  }

  void listen<T>(
    ProviderListenable<T> listenable,
    void Function(T? old, T state) listener, {
    bool fireImmediately = false,
    void Function(Object error, StackTrace stackTrace)? onError,
  }) {
    final element = getElementForInheritedWidgetOfExactType<InheritedConsumer>();
    if (element == null) {
      throw StateError('Can`t find InheritedListenable in scope of $this');
    }

    dependOnInheritedElement(
      element,
      aspect: _ProviderSubscriptionHolder<T>(
        listenable.hashCode ^ 'listen'.hashCode,
        subscription: ProviderScope.containerOf(this).listen(
          listenable,
          listener,
          fireImmediately: fireImmediately,
          onError: onError,
        ),
      ),
    );
  }

  T watch<T>(ProviderListenable<T> listenable, {bool Function(T? old, T state)? when}) {
    final element = getElementForInheritedWidgetOfExactType<InheritedConsumer>() as _InheritProviderElement?;
    if (element == null) {
      throw StateError('Can`t find InheritedListenable in scope of $this');
    }

    final holder = _ProviderSubscriptionHolder<T>(
      listenable.hashCode ^ 'watch'.hashCode,
      subscription: ProviderScope.containerOf(this).listen(listenable, (old, state) {
        if (when?.call(old, state) ?? true) {
          (this as Element).markNeedsBuild();
        }
      }),
      when: when,
    );

    dependOnInheritedElement(
      element,
      aspect: holder,
    );

    return holder.read();
  }
}
