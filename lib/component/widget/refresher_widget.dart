// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

import '../../resource/localization/lkey.dart';
import '../../resource/style/app_theme_ext.dart';

abstract class Refreshing {
  Refreshing refresh();

  Refreshing more();
}

class RefresherWidget<T extends Refreshing> extends StatefulWidget {
  const RefresherWidget({
    super.key,
    required this.initialPaging,
    required this.refreshController,
    this.axis,
    required this.child,
    this.onRefresh,
    this.onMore,
    this.scrollController,
    this.physics,
  });

  final T initialPaging;
  final RefreshController refreshController;
  final ScrollController? scrollController;
  final ScrollPhysics? physics;
  final Axis? axis;
  final Widget child;
  final ValueChanged<T>? onRefresh, onMore;

  @override
  State<RefresherWidget<T>> createState() => _RefresherWidgetState<T>();
}

class _RefresherWidgetState<T extends Refreshing> extends State<RefresherWidget<T>> {
  static const Widget footerLoading = Center(
    child: SizedBox.square(
      dimension: kThirtyTwo,
      child: CircularProgressIndicator.adaptive(
        strokeCap: StrokeCap.round,
      ),
    ),
  );

  late T paging;

  @override
  void initState() {
    super.initState();

    paging = widget.initialPaging;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return SmartRefresher(
      controller: widget.refreshController,
      scrollController: widget.scrollController,
      physics: widget.physics,
      scrollDirection: widget.axis,
      onRefresh: () {
        paging = paging.refresh() as T;
        widget.onRefresh?.call(paging);
      },
      onLoading: () {
        paging = paging.more() as T;
        widget.onMore?.call(paging);
      },
      enablePullUp: widget.onMore != null,
      enablePullDown: widget.onRefresh != null,
      header: const MaterialClassicHeader(),
      footer: CustomFooter(
        loadStyle: LoadStyle.ShowWhenLoading,
        builder: (context, mode) {
          if (mode == LoadStatus.noMore) {
            return Text(
              context.tr(LKey.no_more_data),
              textAlign: TextAlign.center,
              style: theme.themeText.bodyText0.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.themeColor.neutral300,
              ),
            );
          } else if (mode == LoadStatus.idle) {
            return kBox0;
          }
          return footerLoading;
        },
      ),
      child: widget.child,
    );
  }
}
