// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:math' as math;

import 'package:core/core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class SliderMarked extends StatefulWidget {
  SliderMarked({
    super.key,
    this.height = 36,
    this.min = 0,
    this.max = 100,
    this.speed = 1,
    required this.value,
    this.divisions = 10,
    this.valueStringAsFixed = 1,
    this.divisionStringAsFixed,
    required this.valueStyle,
    required this.divisionsStyle,
    this.padding = kPaddingHorizontal16,
    this.onStart,
    this.onUpdated,
    this.onEnd,
  }) : assert(valueStringAsFixed >= 1, 'toStringAsFixed must be >= 1');

  final double height;
  final double min, max, value;
  final double speed;

  final int divisions;
  final int valueStringAsFixed;
  final int? divisionStringAsFixed;
  final TextStyle valueStyle;
  final TextStyle divisionsStyle;
  final EdgeInsets padding;
  final ValueChanged<double>? onStart, onUpdated, onEnd;

  @override
  State<SliderMarked> createState() => _SliderMarkedState();
}

class _SliderMarkedState extends State<SliderMarked> {
  late final ValueNotifier<double> _valueNotifier = ValueNotifier<double>(widget.value);

  @override
  void didUpdateWidget(covariant SliderMarked oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (_valueNotifier.value != widget.value) {
      _valueNotifier.value = widget.value;
    }
  }

  @override
  Widget build(BuildContext context) {
    final sized = MediaQuery.sizeOf(context);
    final range = widget.max - widget.min;
    return GestureDetector(
      onHorizontalDragStart: (details) {
        widget.onStart?.call(_valueNotifier.value);
      },
      onHorizontalDragUpdate: (details) {
        double value = math.min(
          _valueNotifier.value - (-details.delta.dx * range / sized.width * widget.speed),
          widget.max,
        );
        value = math.max(value, widget.min);
        _valueNotifier.value = value;
        widget.onUpdated?.call(value);
      },
      onHorizontalDragEnd: (details) {
        widget.onEnd?.call(_valueNotifier.value);
      },
      onHorizontalDragCancel: () {
        widget.onEnd?.call(_valueNotifier.value);
      },
      child: Padding(
        padding: widget.padding,
        child: CustomPaint(
          size: Size.fromHeight(widget.height),
          painter: _Painter(
            _valueNotifier,
            min: widget.min,
            max: widget.max,
            valueStyle: widget.valueStyle,
            divisions: widget.divisions,
            valueStringAsFixed: widget.valueStringAsFixed,
            divisionStringAsFixed: widget.divisionStringAsFixed ?? (widget.valueStringAsFixed - 1),
            divisionsStyle: widget.divisionsStyle,
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _valueNotifier.dispose();
    super.dispose();
  }
}

class _Painter extends CustomPainter {
  final ValueListenable<double> listenable;
  final double min;
  final double max;
  final TextStyle valueStyle;

  final int divisions;
  final int valueStringAsFixed;
  final int divisionStringAsFixed;
  final TextStyle divisionsStyle;

  _Painter(
    this.listenable, {
    required this.min,
    required this.max,
    required this.valueStyle,
    required this.divisions,
    required this.valueStringAsFixed,
    required this.divisionStringAsFixed,
    required this.divisionsStyle,
  }) : super(repaint: listenable);

  final TextPainter _painter = TextPainter(textDirection: TextDirection.ltr);
  final Paint _paint = Paint();

  @override
  void paint(Canvas canvas, Size size) {
    final divisionSpace = size.width / (divisions);
    final divisionValue = (max - min) / divisions;

    // draw value position
    final p = size.width * (listenable.value - min) / (max - min);
    _painter
      ..text = TextSpan(
        text: listenable.value.toStringAsFixed(valueStringAsFixed),
        style: valueStyle,
      )
      ..layout()
      ..paint(canvas, Offset(p - _painter.width / 2, 0));

    // draw dash line
    double dashWidth = 2, dashSpace = 5, x = 0, y = _painter.height + 5;
    _paint
      ..color = divisionsStyle.color ?? Colors.grey.shade300
      ..strokeWidth = 1;
    while (x < size.width) {
      canvas.drawLine(Offset(x, y), Offset(x + dashWidth, y), _paint);
      x += dashWidth + dashSpace;
    }

    // draw value position
    final center = Offset(p, _painter.height + 5);

    _paint
      ..color = valueStyle.color ?? Colors.grey.shade700
      ..strokeWidth = 1;
    canvas.drawCircle(center, 2, _paint);

    for (int i = 0; i <= divisions; i++) {
      _painter
        ..text = TextSpan(
          text: (min + i * divisionValue).toStringAsFixed(divisionStringAsFixed),
          style: divisionsStyle,
        )
        ..maxLines = 1
        ..layout(maxWidth: divisionSpace);

      double paintX = divisionSpace * i;
      _painter.paint(canvas, Offset(paintX - _painter.width / 2, center.dy + 5));
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
