// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:ui';

import 'package:core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_image_gallery_saver/flutter_image_gallery_saver.dart';

class TakeshotWidget extends StatefulWidget {
  const TakeshotWidget({super.key, required this.child});

  final Widget child;

  @override
  State<TakeshotWidget> createState() => _TakeshotWidgetState();
}

class _TakeshotWidgetState extends State<TakeshotWidget> {
  final GlobalKey globalKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onLongPressEnd: (details) async {
        toast.show(context, message: 'Exporting');
        RenderRepaintBoundary boundary = globalKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
        final image = await boundary.toImage(pixelRatio: MediaQuery.devicePixelRatioOf(context));
        final bytes = await image.toByteData(format: ImageByteFormat.png);
        await FlutterImageGallerySaver.saveImage(bytes!.buffer.asUint8List());
        toast.show(context, message: 'Export success');
      },
      child: RepaintBoundary(
        key: globalKey,
        child: widget.child,
      ),
    );
  }
}
