// Flutter project by quanghuuxx (<EMAIL>)

enum AppError {
  unknown,
  noConnection,
  notFound,
  somethingWrong,
  forbidden;

  String get code {
    switch (this) {
      case AppError.unknown:
        return 'unknown';
      case AppError.noConnection:
        return 'no_connection';
      case AppError.notFound:
        return 'not_found';
      case AppError.somethingWrong:
        return 'something_wrong';
      case AppError.forbidden:
        return 'forbidden';
    }
  }
}
