// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:io';

import 'package:core/core.dart';
import 'package:flutter/foundation.dart';

final configurations = Configurations._();

class Configurations {
  Configurations._();

  static const String kDefaultPackageName = 'vn.quanghuuxx.pic_sketch';

  bool get devMode {
    if (kDebugMode) {
      return true;
    }

    final package = PackageInfo.instance;
    return Configurations.kDefaultPackageName != package.application.packageName;
  }

  late String _revenuecatApiKey;
  String get revenuecatApiKey => _revenuecatApiKey;

  void load(final Env env) {
    String key = 'revenuecat_api_key_android';
    if (Platform.isIOS) {
      key = 'revenuecat_api_key_ios';
    }
    _revenuecatApiKey = env.get(key, fallback: '');
  }
}
