// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:convert';

import 'package:core/extentions/extentions.dart';
import 'package:objectbox/objectbox.dart';

import '../data.dart';

@Entity()
class PicTemplateEntity {
  @Id(assignable: true)
  int id;
  int? saveAt;
  String? saveStatus;
  String originImg;
  double opacity;
  double? ratio;
  double rotation;
  String? nameFlipEnum;
  String? snapshot;
  String jsonImageBounds;
  String? jsonBlur;
  String jsonFilter;
  List<String> jsonElements;

  PicTemplateEntity({
    required this.id,
    this.saveAt,
    this.saveStatus,
    required this.originImg,
    required this.opacity,
    this.ratio,
    required this.rotation,
    this.nameFlipEnum,
    this.snapshot,
    required this.jsonImageBounds,
    this.jsonBlur,
    required this.jsonFilter,
    required this.jsonElements,
  });

  factory PicTemplateEntity.fromPicTemplate(PicTemplate template) {
    return PicTemplateEntity(
      id: template.id ?? 0,
      saveAt: template.savedAt,
      saveStatus: template.saveStatus?.name,
      originImg: template.originImg,
      opacity: template.opacity,
      ratio: template.ratio,
      rotation: template.rotation,
      nameFlipEnum: template.flipEnum?.name,
      snapshot: template.snapshot,
      jsonImageBounds: json.encode(template.imageBounds.toJson()),
      jsonBlur: template.blur.when((it) {
        if (it is PicBlur) {
          return json.encode(it.toJson());
        }
        return null;
      }),
      jsonFilter: json.encode(template.filter.toJson()),
      jsonElements: template.elements.map((e) => json.encode(e.toJson())).toList(),
    );
  }

  PicTemplate toPicTemplate() {
    return PicTemplate(
      id: id,
      savedAt: saveAt,
      saveStatus: SaveStatus.fromString(saveStatus ?? SaveStatus.draft.name),
      originImg: originImg,
      opacity: opacity,
      ratio: ratio,
      rotation: rotation,
      flipEnum: PicFlipEnum.fromString(nameFlipEnum),
      snapshot: snapshot,
      imageBounds: PicRect.fromJson(json.decode(jsonImageBounds)),
      blur: jsonBlur.when((it) {
        if (it is String) {
          return PicBlur.fromJson(json.decode(it));
        }
        return null;
      }),
      filter: PicImgFilter.fromJson(json.decode(jsonFilter)),
      elements: jsonElements.map((e) => PicElement.fromJson(json.decode(e))).toList(),
    );
  }
}
