// Flutter project by quanghuuxx (<EMAIL>)

import 'package:objectbox/objectbox.dart';

import '../../data.dart';

class PicTemplateBox {
  final Box<PicTemplateEntity> _box;

  PicTemplateBox(final Store store) : _box = store.box<PicTemplateEntity>();

  int put(PicTemplateEntity entity) {
    return _box.put(entity);
  }

  bool delete(int id) {
    return _box.remove(id);
  }

  Stream<List<PicTemplateEntity>> asStream([bool seeded = true]) {
    return _box.query().watch(triggerImmediately: seeded).map((event) => event.find());
  }

  int count() {
    return _box.count();
  }
}
