// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';

abstract class AppPreferences {
  Future<void> clear();

  Future<bool> setString(String key, String value);

  Future<bool> setInt(String key, int value);

  Future<bool> setBool(String key, bool value);

  Future<bool> setMap(String key, Map<String, dynamic> value);

  Set<String> getKeys();

  String? getString(String key);

  int? getInt(String key);

  bool? getBool(String key);

  Map<String, dynamic>? getMap(String key);

  static const String shownTooltipFeatures = 'shown_tooltip_features';
  static const String shouldShowPermissionRequestRationaleKey = 'should_show_permission_request_rationale';
  static const String exportSlotKey = 'export_slot_key';
}

class AppPreferencesImpl extends AppPreferences {
  final SharedPreferencesService _preferences;

  AppPreferencesImpl(this._preferences);

  @override
  Future<void> clear() {
    // TODO: implement clear
    throw UnimplementedError();
  }

  @override
  bool? getBool(String key) {
    return _preferences.getBool(key);
  }

  @override
  int? getInt(String key) {
    return _preferences.getInt(key);
  }

  @override
  Set<String> getKeys() {
    return _preferences.getKeys();
  }

  @override
  Map<String, dynamic>? getMap(String key) {
    return _preferences.getMap(key);
  }

  @override
  String? getString(String key) {
    return _preferences.getString(key);
  }

  @override
  Future<bool> setBool(String key, bool value) {
    return _preferences.setBool(key, value);
  }

  @override
  Future<bool> setInt(String key, int value) {
    return _preferences.setInt(key, value);
  }

  @override
  Future<bool> setMap(String key, Map<String, dynamic> value) {
    return _preferences.setMap(key, value);
  }

  @override
  Future<bool> setString(String key, String value) {
    return _preferences.setString(key, value);
  }
}
