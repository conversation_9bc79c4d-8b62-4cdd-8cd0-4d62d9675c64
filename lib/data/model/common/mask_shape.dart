// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:ui';

import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

import '../../data.dart';

part 'mask_shape.g.dart';

@JsonSerializable()
class MaskShape extends Equatable {
  static const MaskShape rect = MaskShape(
    asset: 'assets/shapes/rect.png',
    width: 100,
    height: 100,
    paths: [
      MaskPath(
        path: 'M0 0h100v100H0Z',
        style: PaintingStyle.fill,
      ),
    ],
  );

  const MaskShape({
    required this.asset,
    required this.width,
    required this.height,
    required this.paths,
    this.stroke,
  });

  final String asset;
  final double width;
  final double height;
  final List<MaskPath> paths;
  final MaskStroke? stroke;

  double get aspectRatio => width / height;

  factory MaskShape.fromJson(Map<String, dynamic> json) => _$MaskShapeFromJson(json);
  Map<String, dynamic> toJson() => _$MaskShapeToJson(this);

  factory MaskShape.fromRect({
    required String asset,
    required Rect rect,
  }) {
    return MaskShape(
      asset: asset,
      width: rect.width,
      height: rect.height,
      paths: [
        MaskPath(
          path: 'M${rect.left} ${rect.top}h${rect.width}v${rect.height}H${rect.left}Z',
          style: PaintingStyle.fill,
        ),
      ],
    );
  }

  @override
  List<Object?> get props => [
        asset,
        width,
        height,
        paths,
        stroke,
      ];

  MaskShape copyWith({
    String? asset,
    double? width,
    double? height,
    List<MaskPath>? paths,
    MaskStroke? stroke,
  }) {
    return MaskShape(
      asset: asset ?? this.asset,
      width: width ?? this.width,
      height: height ?? this.height,
      paths: paths ?? this.paths,
      stroke: stroke ?? this.stroke,
    );
  }
}

@JsonSerializable()
class MaskPath extends Equatable {
  const MaskPath({
    required this.path,
    required this.style,
  });

  final String path;
  final PaintingStyle style;

  factory MaskPath.fromJson(Map<String, dynamic> json) => _$MaskPathFromJson(json);
  Map<String, dynamic> toJson() => _$MaskPathToJson(this);

  @override
  List<Object?> get props => [
        path,
        style,
      ];
}

@JsonSerializable()
class MaskStroke extends Equatable {
  static const double kMaxTranslateRatio = 0.5, kMaxWidth = 0.05;

  const MaskStroke({
    this.width = 0,
    this.color = '#000000',
    this.translate = PicPosition.zero,
  });

  /// tỉ lệ dày của viền so với cạnh width hiện tại của element
  /// tối đa là [kMaxWidth]
  final double width;
  final String color;

  /// tỉ lệ dịch chuyển của stroke so với kích cỡ hiện tại của element
  /// tối đa là [kMaxTranslateRatio]
  final PicPosition translate;

  bool get hasStroke => width > 0;

  factory MaskStroke.fromJson(Map<String, dynamic> json) => _$MaskStrokeFromJson(json);
  Map<String, dynamic> toJson() => _$MaskStrokeToJson(this);

  @override
  List<Object?> get props => [
        width,
        color,
        translate,
      ];

  MaskStroke copyWith({
    double? width,
    String? color,
    PicPosition? translate,
  }) {
    return MaskStroke(
      width: width ?? this.width,
      color: color ?? this.color,
      translate: translate ?? this.translate,
    );
  }
}
