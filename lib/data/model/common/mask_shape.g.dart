// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mask_shape.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MaskShape _$MaskShapeFromJson(Map<String, dynamic> json) => MaskShape(
      asset: json['asset'] as String,
      width: (json['width'] as num).toDouble(),
      height: (json['height'] as num).toDouble(),
      paths: (json['paths'] as List<dynamic>)
          .map((e) => MaskPath.fromJson(e as Map<String, dynamic>))
          .toList(),
      stroke: json['stroke'] == null
          ? null
          : MaskStroke.fromJson(json['stroke'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$MaskShapeToJson(MaskShape instance) => <String, dynamic>{
      'asset': instance.asset,
      'width': instance.width,
      'height': instance.height,
      'paths': instance.paths.map((e) => e.toJson()).toList(),
      if (instance.stroke?.toJson() case final value?) 'stroke': value,
    };

MaskPath _$MaskPathFromJson(Map<String, dynamic> json) => MaskPath(
      path: json['path'] as String,
      style: $enumDecode(_$PaintingStyleEnumMap, json['style']),
    );

Map<String, dynamic> _$MaskPathToJson(MaskPath instance) => <String, dynamic>{
      'path': instance.path,
      'style': _$PaintingStyleEnumMap[instance.style]!,
    };

const _$PaintingStyleEnumMap = {
  PaintingStyle.fill: 'fill',
  PaintingStyle.stroke: 'stroke',
};

MaskStroke _$MaskStrokeFromJson(Map<String, dynamic> json) => MaskStroke(
      width: (json['width'] as num?)?.toDouble() ?? 0,
      color: json['color'] as String? ?? '#000000',
      translate: json['translate'] == null
          ? PicPosition.zero
          : PicPosition.fromJson(json['translate'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$MaskStrokeToJson(MaskStroke instance) =>
    <String, dynamic>{
      'width': instance.width,
      'color': instance.color,
      'translate': instance.translate.toJson(),
    };
