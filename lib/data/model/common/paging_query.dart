// Flutter project by quanghuuxx (<EMAIL>)

import '../../../component/widget/refresher_widget.dart';

class PagingQuery implements Refreshing {
  static PagingQuery initial = PagingQuery(limit: 20, start: 0);

  final int limit;
  final int start;

  PagingQuery({
    required this.limit,
    required this.start,
  });

  @override
  PagingQuery more() {
    return PagingQuery(
      limit: limit,
      start: start + limit,
    );
  }

  @override
  PagingQuery refresh() {
    return PagingQuery.initial;
  }
}
