// Flutter project by quanghuuxx (<EMAIL>)

enum PicFlipEnum {
  horizontal,
  vertical,
  both;

  PicFlipEnum? replace(PicFlipEnum? fliped) {
    switch (this) {
      case PicFlipEnum.horizontal:
        return switch (fliped) {
          PicFlipEnum.vertical => PicFlipEnum.both,
          _ => null,
        };
      case PicFlipEnum.vertical:
        return switch (fliped) {
          PicFlipEnum.horizontal => PicFlipEnum.both,
          _ => null,
        };
      case PicFlipEnum.both:
        return switch (fliped) {
          PicFlipEnum.horizontal => PicFlipEnum.vertical,
          PicFlipEnum.vertical => PicFlipEnum.horizontal,
          _ => null,
        };
    }
  }

  static PicFlipEnum? fromString(String? value) {
    switch (value) {
      case 'horizontal':
        return PicFlipEnum.horizontal;
      case 'vertical':
        return PicFlipEnum.vertical;
      case 'both':
        return PicFlipEnum.both;
      default:
        return null;
    }
  }
}
