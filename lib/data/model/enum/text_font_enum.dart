// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/utils/typography.dart';
import 'package:flutter/painting.dart';

import '../../../resource/style/text/app_theme_text.dart';

enum TextFontEnum {
  baloo2,
  inter,
  outfit,
  caveat,
  cookie,
  dynaPuff,
  jua,
  gaegu,
  ole,
  puppiesPlay,
  rougeScript,
  moiraiOne,
  rampartOne,
  silkscreen,
  mustachioCf,
  rainbowRibbonCf,
  vintageGrovee,
  blackout2am,
  iceAge;

  TextStyleBuilder get builder {
    return ({
      Color? color,
      Color? backgroundColor,
      double? fontSize,
      FontWeight? fontWeight,
      TextDecoration? decoration,
      double? letterSpacing,
      double? wordSpacing,
      double? height,
      Paint? foreground,
      Paint? background,
      List<Shadow>? shadows,
      TextBaseline? textBaseline,
    }) {
      return AppTypography.getTextStyle(
        fontFamily: family,
        color: color,
        fontSize: fontSize,
        fontWeight: fontWeight,
        decoration: decoration,
        letterSpacing: letterSpacing,
        height: height,
        backgroundColor: backgroundColor,
        wordSpacing: wordSpacing,
        foreground: foreground,
        background: background,
        shadows: shadows,
        textBaseline: textBaseline,
      );
    };
  }

  String get family => switch (this) {
        inter => 'Inter',
        caveat => 'Caveat',
        cookie => 'Cookie',
        dynaPuff => 'DynaPuff',
        gaegu => 'Gaegu',
        jua => 'Jua',
        moiraiOne => 'Moirai_One',
        ole => 'Ole',
        outfit => 'Outfit',
        puppiesPlay => 'Puppies_Play',
        rampartOne => 'Rampart_One',
        rougeScript => 'Rouge_Script',
        silkscreen => 'Silkscreen',
        mustachioCf => 'Mustachio_CF',
        rainbowRibbonCf => 'Rainbow_Ribbon_CF',
        vintageGrovee => 'Vintage_Grovee',
        blackout2am => 'Blackout_2am',
        iceAge => 'Ice_Age',
        baloo2 => 'Baloo2',
      };
}
