// Flutter project by quanghuuxx (<EMAIL>)

import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'sticker_collection.g.dart';

@JsonSerializable()
class StickerCollection extends Equatable {
  const StickerCollection({
    required this.stickers,
    required this.label,
  });

  @JsonKey(name: '3x')
  final List<String> stickers;
  final String label;

  factory StickerCollection.fromJson(Map<String, dynamic> json) => _$StickerCollectionFromJson(json);
  Map<String, dynamic> toJson() => _$StickerCollectionToJson(this);

  @override
  List<Object?> get props => [
        stickers,
        label,
      ];
}
