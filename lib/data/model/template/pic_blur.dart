// Flutter project by quanghuuxx (<EMAIL>)

import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'pic_blur.g.dart';

@JsonSerializable()
class PicBlur extends Equatable {
  static const none = PicBlur(sigmaX: 0, sigmaY: 0);

  final double sigmaX;
  final double sigmaY;

  const PicBlur({required this.sigmaX, required this.sigmaY});

  factory PicBlur.fromJson(Map<String, dynamic> json) => _$PicBlurFromJson(json);
  Map<String, dynamic> toJson() => _$PicBlurToJson(this);

  @override
  List<Object> get props => [sigmaX, sigmaY];

  PicBlur copyWith({
    double? sigmaX,
    double? sigmaY,
  }) {
    return PicBlur(
      sigmaX: sigmaX ?? this.sigmaX,
      sigmaY: sigmaY ?? this.sigmaY,
    );
  }
}
