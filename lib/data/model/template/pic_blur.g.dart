// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pic_blur.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PicBlur _$<PERSON>lur<PERSON>rom<PERSON>son(Map<String, dynamic> json) => PicBlur(
      sigmaX: (json['sigma_x'] as num).toDouble(),
      sigmaY: (json['sigma_y'] as num).toDouble(),
    );

Map<String, dynamic> _$PicBlurToJson(PicBlur instance) => <String, dynamic>{
      'sigma_x': instance.sigmaX,
      'sigma_y': instance.sigmaY,
    };
