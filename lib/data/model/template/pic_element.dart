// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

import '../../data.dart';

part 'pic_element.g.dart';

@JsonSerializable()
class PicElement extends Equatable {
  static final BaseException _invalidKeyException = throw BaseException(
    code: kDevErrorCode,
    description: 'Invalid element key',
  );

  static ({PicElementType type, int id}) decodeKey(String key) {
    final parts = key.split('_');
    if (parts.length < 3) {
      throw _invalidKeyException;
    }
    final type = PicElementType.values.firstWhereOrNull((element) => element.name == parts[1]);
    if (type == null) {
      throw _invalidKeyException;
    }
    final id = int.tryParse(parts[2]);
    if (id == null) {
      throw _invalidKeyException;
    }
    return (type: type, id: id);
  }

  static bool isElementType(String? key, PicElementType type) {
    if (key == null) return false;
    return key.contains('_${type.name}_');
  }

  const PicElement({
    required this.id,
    required this.type,
    required this.rect,
    this.rotation = 0.0,
    this.opacity = 1,
  });

  final int id;
  final PicElementType type;
  final PicRect rect;
  final double rotation;
  final double opacity;

  String get key => 'element_${type.name}_$id';

  factory PicElement.fromJson(Map<String, dynamic> json) {
    final type = json['type'];
    if (type == PicElementType.text.name) {
      return PicElementText.fromJson(json);
    } else if (type == PicElementType.mask.name) {
      return PicElementMask.fromJson(json);
    } else if (type == PicElementType.sticker.name) {
      return PicElementSticker.fromJson(json);
    }
    return _$PicElementFromJson(json);
  }
  Map<String, dynamic> toJson() => _$PicElementToJson(this);

  PicElement copyWith({
    int? id,
    PicElementType? type,
    PicRect? rect,
    double? rotation,
    double? opacity,
  }) {
    return PicElement(
      id: id ?? this.id,
      type: type ?? this.type,
      rect: rect ?? this.rect,
      rotation: rotation ?? this.rotation,
      opacity: opacity ?? this.opacity,
    );
  }

  @override
  List<Object?> get props => [
        id,
        type,
        rect,
        rotation,
        opacity,
      ];
}
