// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pic_element.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PicElement _$PicElementFromJson(Map<String, dynamic> json) => PicElement(
      id: (json['id'] as num).toInt(),
      type: $enumDecode(_$PicElementTypeEnumMap, json['type']),
      rect: PicRect.fromJson(json['rect'] as Map<String, dynamic>),
      rotation: (json['rotation'] as num?)?.toDouble() ?? 0.0,
      opacity: (json['opacity'] as num?)?.toDouble() ?? 1,
    );

Map<String, dynamic> _$PicElementToJson(PicElement instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$PicElementTypeEnumMap[instance.type]!,
      'rect': instance.rect.toJson(),
      'rotation': instance.rotation,
      'opacity': instance.opacity,
    };

const _$PicElementTypeEnumMap = {
  PicElementType.mask: 'mask',
  PicElementType.sticker: 'sticker',
  PicElementType.text: 'text',
};
