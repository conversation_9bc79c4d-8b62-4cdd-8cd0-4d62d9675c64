// Flutter project by quanghuuxx (<EMAIL>)

import 'package:equatable/equatable.dart';
import 'package:flutter/rendering.dart';
import 'package:json_annotation/json_annotation.dart';

import '../../data.dart';
import '../common/mask_shape.dart';

part 'pic_element_mask.g.dart';

@JsonSerializable()
class PicElementMask extends PicElement {
  PicElementMask({
    required super.id,
    required super.type,
    required super.rect,
    super.rotation,
    super.opacity,
    required this.source,
    this.shape = MaskShape.rect,
    this.filter,
    this.flipEnum,
    this.metadata,
  });

  final String source;
  final MaskShape shape;
  final PicImgFilter? filter;
  final PicFlipEnum? flipEnum;
  final MaskMetadata? metadata;

  factory PicElementMask.fromJson(Map<String, dynamic> json) => _$PicElementMaskFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$PicElementMaskToJson(this);

  @override
  List<Object?> get props => [
        ...super.props,
        source,
        shape,
        filter,
        flipEnum,
        metadata,
      ];

  @override
  PicElementMask copyWith({
    int? id,
    PicElementType? type,
    PicRect? rect,
    double? rotation,
    String? source,
    double? opacity,
    MaskShape? shape,
    PicImgFilter? filter,
    PicFlipEnum? flipEnum,
    MaskMetadata? metadata,
    bool metadataNullable = false,
  }) {
    return PicElementMask(
      source: source ?? this.source,
      id: id ?? this.id,
      type: type ?? this.type,
      rect: rect ?? this.rect,
      rotation: rotation ?? this.rotation,
      opacity: opacity ?? this.opacity,
      filter: filter ?? this.filter,
      shape: shape ?? this.shape,
      flipEnum: flipEnum ?? this.flipEnum,
      metadata: _includeNull(metadata, this.metadata, nullable: metadataNullable),
    );
  }

  T? _includeNull<T>(T? value, T? old, {bool nullable = false}) {
    if (nullable) {
      return value;
    }
    return value ?? old;
  }
}

@JsonSerializable()
class MaskMetadata extends Equatable {
  const MaskMetadata({
    required this.scale,
    required this.rotate,
    required this.translate,
  });

  final double scale;
  final double rotate;
  final PicPosition translate;

  factory MaskMetadata.fromJson(Map<String, dynamic> json) => _$MaskMetadataFromJson(json);
  Map<String, dynamic> toJson() => _$MaskMetadataToJson(this);

  Matrix4 toMatrix4([Offset center = Offset.zero]) {
    Matrix4 _matrix = Matrix4.identity();
    if (scale != 1.0) {
      _matrix
        ..translate(center.dx, center.dy)
        ..scale(scale, scale, 1.0)
        ..translate(-center.dx, -center.dy);
    }

    if (translate != PicPosition.zero) {
      _matrix.translate(
        translate.x,
        translate.y,
        1.0,
      );
    }

    if (rotate != 0.0) {
      _matrix
        ..translate(center.dx, center.dy)
        ..rotateZ(rotate)
        ..translate(-center.dx, -center.dy);
    }

    return _matrix;
  }

  @override
  List<Object?> get props => [
        scale,
        rotate,
        translate,
      ];
}
