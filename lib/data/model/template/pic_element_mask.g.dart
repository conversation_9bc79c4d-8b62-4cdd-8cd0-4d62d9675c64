// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pic_element_mask.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PicElementMask _$PicElementMaskFromJson(Map<String, dynamic> json) =>
    PicElementMask(
      id: (json['id'] as num).toInt(),
      type: $enumDecode(_$PicElementTypeEnumMap, json['type']),
      rect: PicRect.fromJson(json['rect'] as Map<String, dynamic>),
      rotation: (json['rotation'] as num?)?.toDouble() ?? 0.0,
      opacity: (json['opacity'] as num?)?.toDouble() ?? 1,
      source: json['source'] as String,
      shape: json['shape'] == null
          ? MaskShape.rect
          : MaskShape.fromJson(json['shape'] as Map<String, dynamic>),
      filter: json['filter'] == null
          ? null
          : PicImgFilter.fromJson(json['filter'] as Map<String, dynamic>),
      flipEnum: $enumDecodeNullable(_$PicFlipEnumEnumMap, json['flip_enum']),
      metadata: json['metadata'] == null
          ? null
          : MaskMetadata.fromJson(json['metadata'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PicElementMaskToJson(PicElementMask instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$PicElementTypeEnumMap[instance.type]!,
      'rect': instance.rect.toJson(),
      'rotation': instance.rotation,
      'opacity': instance.opacity,
      'source': instance.source,
      'shape': instance.shape.toJson(),
      if (instance.filter?.toJson() case final value?) 'filter': value,
      if (_$PicFlipEnumEnumMap[instance.flipEnum] case final value?)
        'flip_enum': value,
      if (instance.metadata?.toJson() case final value?) 'metadata': value,
    };

const _$PicElementTypeEnumMap = {
  PicElementType.mask: 'mask',
  PicElementType.sticker: 'sticker',
  PicElementType.text: 'text',
};

const _$PicFlipEnumEnumMap = {
  PicFlipEnum.horizontal: 'horizontal',
  PicFlipEnum.vertical: 'vertical',
  PicFlipEnum.both: 'both',
};

MaskMetadata _$MaskMetadataFromJson(Map<String, dynamic> json) => MaskMetadata(
      scale: (json['scale'] as num).toDouble(),
      rotate: (json['rotate'] as num).toDouble(),
      translate:
          PicPosition.fromJson(json['translate'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$MaskMetadataToJson(MaskMetadata instance) =>
    <String, dynamic>{
      'scale': instance.scale,
      'rotate': instance.rotate,
      'translate': instance.translate.toJson(),
    };
