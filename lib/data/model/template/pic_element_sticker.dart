// Flutter project by quanghuuxx (<EMAIL>)

import 'package:json_annotation/json_annotation.dart';

import '../../data.dart';

part 'pic_element_sticker.g.dart';

@JsonSerializable()
class PicElementSticker extends PicElement {
  final String source;
  final PicFlipEnum? flipEnum;

  const PicElementSticker({
    required super.id,
    required super.type,
    required super.rect,
    super.rotation,
    super.opacity,
    required this.source,
    this.flipEnum,
  });

  factory PicElementSticker.fromJson(Map<String, dynamic> json) => _$PicElementStickerFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$PicElementStickerToJson(this);

  @override
  PicElementSticker copyWith({
    int? id,
    PicElementType? type,
    PicRect? rect,
    double? rotation,
    double? opacity,
    String? source,
    PicFlipEnum? flipEnum,
    bool flipEnumNullable = false,
  }) {
    return PicElementSticker(
      id: id ?? this.id,
      type: type ?? this.type,
      rect: rect ?? this.rect,
      rotation: rotation ?? this.rotation,
      opacity: opacity ?? this.opacity,
      source: source ?? this.source,
      flipEnum: flipEnum ?? (flipEnumNullable ? null : this.flipEnum),
    );
  }

  @override
  List<Object?> get props => [
        id,
        type,
        rect,
        rotation,
        opacity,
        source,
        flipEnum,
      ];
}
