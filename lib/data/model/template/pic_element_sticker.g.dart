// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pic_element_sticker.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PicElementSticker _$PicElementStickerFromJson(Map<String, dynamic> json) =>
    PicElementSticker(
      id: (json['id'] as num).toInt(),
      type: $enumDecode(_$PicElementTypeEnumMap, json['type']),
      rect: PicRect.fromJson(json['rect'] as Map<String, dynamic>),
      rotation: (json['rotation'] as num?)?.toDouble() ?? 0.0,
      opacity: (json['opacity'] as num?)?.toDouble() ?? 1,
      source: json['source'] as String,
      flipEnum: $enumDecodeNullable(_$PicFlipEnumEnumMap, json['flip_enum']),
    );

Map<String, dynamic> _$PicElementStickerToJson(PicElementSticker instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$PicElementTypeEnumMap[instance.type]!,
      'rect': instance.rect.toJson(),
      'rotation': instance.rotation,
      'opacity': instance.opacity,
      'source': instance.source,
      if (_$PicFlipEnumEnumMap[instance.flipEnum] case final value?)
        'flip_enum': value,
    };

const _$PicElementTypeEnumMap = {
  PicElementType.mask: 'mask',
  PicElementType.sticker: 'sticker',
  PicElementType.text: 'text',
};

const _$PicFlipEnumEnumMap = {
  PicFlipEnum.horizontal: 'horizontal',
  PicFlipEnum.vertical: 'vertical',
  PicFlipEnum.both: 'both',
};
