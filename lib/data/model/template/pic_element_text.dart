// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:math' show max;

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart' show Shader, Matrix4, Size, Rect, Offset, ImageShader, TileMode, TextAlign;
import 'package:json_annotation/json_annotation.dart';

import '../../../features/editor/model/image_data.dart';
import '../../data.dart';

part 'pic_element_text.g.dart';

@JsonSerializable()
class PicElementText extends PicElement {
  static const TextMetadata kDefaultMetadata = TextMetadata(
    fontEnum: TextFontEnum.inter,
    fontSize: 14,
    color: '#eeeeee',
    textAlign: TextAlign.center,
  );

  const PicElementText({
    required super.id,
    required super.type,
    required super.rect,
    super.rotation,
    super.opacity,
    required this.content,
    this.metadata = PicElementText.kDefaultMetadata,
  });

  final String content;
  final TextMetadata metadata;

  factory PicElementText.fromJson(Map<String, dynamic> json) => _$PicElementTextFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$PicElementTextToJson(this);

  @override
  List<Object?> get props => [
        ...super.props,
        content,
        metadata,
      ];

  @override
  PicElementText copyWith({
    int? id,
    PicElementType? type,
    PicRect? rect,
    double? rotation,
    double? opacity,
    String? content,
    TextMetadata? metadata,
  }) {
    return PicElementText(
      id: id ?? this.id,
      type: type ?? this.type,
      rect: rect ?? this.rect,
      rotation: rotation ?? this.rotation,
      opacity: opacity ?? this.opacity,
      content: content ?? this.content,
      metadata: metadata ?? this.metadata,
    );
  }
}

@JsonSerializable()
class TextMetadata extends Equatable {
  const TextMetadata({
    required this.fontEnum,
    required this.fontSize,
    this.color,
    this.stroke,
    this.shadow,
    this.bend,
    this.shader,
    this.textAlign,
  });

  final TextFontEnum fontEnum;
  final double fontSize;
  final String? color;

  /// độ dày của viền bằng fontSize * PicStroke.thickness, tối đa là [PicStroke.maxThickness]
  final PicStroke? stroke;
  final TextShadow? shadow;
  final double? bend;
  final PicShader? shader;
  final TextAlign? textAlign;

  bool get hasBend => bend != null && bend != .0;

  bool get hasShader => shader != null;

  factory TextMetadata.fromJson(Map<String, dynamic> json) => _$TextMetadataFromJson(json);
  Map<String, dynamic> toJson() => _$TextMetadataToJson(this);

  @override
  List<Object?> get props => [
        fontEnum,
        fontSize,
        color,
        stroke,
        shadow,
        bend,
        shader,
        textAlign,
      ];

  TextMetadata copyWith({
    TextFontEnum? fontEnum,
    double? fontSize,
    String? color,
    PicStroke? stroke,
    TextShadow? shadow,
    double? bend,
    PicShader? shader,
    TextAlign? textAlign,
  }) {
    return TextMetadata(
      fontEnum: fontEnum ?? this.fontEnum,
      fontSize: fontSize ?? this.fontSize,
      color: shader != null ? null : color ?? this.color,
      stroke: stroke ?? this.stroke,
      shadow: shadow ?? this.shadow,
      bend: bend ?? this.bend,
      shader: (color != null && shader == null) ? null : shader ?? this.shader,
      textAlign: textAlign ?? this.textAlign,
    );
  }
}

@JsonSerializable()
class TextShadow extends Equatable {
  const TextShadow({
    required this.x,
    required this.y,
    required this.blur,
    required this.color,
    required this.opacity,
  });

  final double x;
  final double y;
  final double blur;
  final String color;
  final double opacity;

  factory TextShadow.fromJson(Map<String, dynamic> json) => _$TextShadowFromJson(json);
  Map<String, dynamic> toJson() => _$TextShadowToJson(this);

  TextShadow copyWith({
    double? x,
    double? y,
    double? blur,
    String? color,
    double? opacity,
  }) {
    return TextShadow(
      x: x ?? this.x,
      y: y ?? this.y,
      blur: blur ?? this.blur,
      color: color ?? this.color,
      opacity: opacity ?? this.opacity,
    );
  }

  @override
  List<Object?> get props => [
        x,
        y,
        blur,
        color,
        opacity,
      ];
}

@JsonSerializable()
class TextBackground extends PicShader {
  final String source;
  final double scale;
  final double rotate;
  final PicPosition translate;

  TextBackground({
    required this.source,
    required this.scale,
    required this.rotate,
    required this.translate,
    super.type = PicShaderType.background,
  }) : assert(type == PicShaderType.background);

  factory TextBackground.fromJson(Map<String, dynamic> json) => _$TextBackgroundFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$TextBackgroundToJson(this);

  @override
  List<Object?> get props => [
        ...super.props,
        source,
        scale,
        rotate,
        translate,
      ];

  @override
  Shader linear(Rect rect, {bool includeRotation = true}) {
    final data = PreloadImageObject.instance.get(source);
    return ImageShader(
      data.image,
      TileMode.decal,
      TileMode.decal,
      _caculateMatrix(data.size, rect.size).storage,
    );
  }

  @override
  Shader sweep(
    Rect rect, {
    required double startAngle,
    required double sweepAngle,
  }) {
    return linear(rect);
  }

  TextBackground copyWith({
    String? source,
    double? scale,
    double? rotate,
    PicPosition? translate,
  }) {
    return TextBackground(
      source: source ?? this.source,
      scale: scale ?? this.scale,
      rotate: rotate ?? this.rotate,
      translate: translate ?? this.translate,
    );
  }

  Matrix4 _caculateMatrix(Size inputSize, Size outputSize) {
    Matrix4 _matrix = Matrix4.identity();

    /// always scale cover to fill the [outputSize]
    double _scale = max(outputSize.width / inputSize.width, outputSize.height / inputSize.height);

    _matrix = Matrix4.identity()
      ..translate(outputSize.width / 2, outputSize.height / 2)
      ..scale(_scale, _scale, 1.0)
      ..translate(-inputSize.width / 2, -inputSize.height / 2);

    final center = inputSize.center(Offset.zero);
    if (scale != 1.0) {
      _matrix
        ..translate(center.dx, center.dy)
        ..scale(scale, scale, 1.0)
        ..translate(-center.dx, -center.dy);
    }

    if (translate != PicPosition.zero) {
      _matrix.translate(
        translate.x,
        translate.y,
        1.0,
      );
    }

    if (rotate != 0.0) {
      _matrix
        ..translate(center.dx, center.dy)
        ..rotateZ(rotate)
        ..translate(-center.dx, -center.dy);
    }

    return _matrix;
  }
}
