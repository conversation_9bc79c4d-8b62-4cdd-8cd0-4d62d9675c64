// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pic_element_text.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PicElementText _$PicElementTextFromJson(Map<String, dynamic> json) =>
    PicElementText(
      id: (json['id'] as num).toInt(),
      type: $enumDecode(_$PicElementTypeEnumMap, json['type']),
      rect: PicRect.fromJson(json['rect'] as Map<String, dynamic>),
      rotation: (json['rotation'] as num?)?.toDouble() ?? 0.0,
      opacity: (json['opacity'] as num?)?.toDouble() ?? 1,
      content: json['content'] as String,
      metadata: json['metadata'] == null
          ? PicElementText.kDefaultMetadata
          : TextMetadata.fromJson(json['metadata'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PicElementTextToJson(PicElementText instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$PicElementTypeEnumMap[instance.type]!,
      'rect': instance.rect.toJson(),
      'rotation': instance.rotation,
      'opacity': instance.opacity,
      'content': instance.content,
      'metadata': instance.metadata.toJson(),
    };

const _$PicElementTypeEnumMap = {
  PicElementType.mask: 'mask',
  PicElementType.sticker: 'sticker',
  PicElementType.text: 'text',
};

TextMetadata _$TextMetadataFromJson(Map<String, dynamic> json) => TextMetadata(
      fontEnum: $enumDecode(_$TextFontEnumEnumMap, json['font_enum']),
      fontSize: (json['font_size'] as num).toDouble(),
      color: json['color'] as String?,
      stroke: json['stroke'] == null
          ? null
          : PicStroke.fromJson(json['stroke'] as Map<String, dynamic>),
      shadow: json['shadow'] == null
          ? null
          : TextShadow.fromJson(json['shadow'] as Map<String, dynamic>),
      bend: (json['bend'] as num?)?.toDouble(),
      shader: json['shader'] == null
          ? null
          : PicShader.fromJson(json['shader'] as Map<String, dynamic>),
      textAlign: $enumDecodeNullable(_$TextAlignEnumMap, json['text_align']),
    );

Map<String, dynamic> _$TextMetadataToJson(TextMetadata instance) =>
    <String, dynamic>{
      'font_enum': _$TextFontEnumEnumMap[instance.fontEnum]!,
      'font_size': instance.fontSize,
      if (instance.color case final value?) 'color': value,
      if (instance.stroke?.toJson() case final value?) 'stroke': value,
      if (instance.shadow?.toJson() case final value?) 'shadow': value,
      if (instance.bend case final value?) 'bend': value,
      if (instance.shader?.toJson() case final value?) 'shader': value,
      if (_$TextAlignEnumMap[instance.textAlign] case final value?)
        'text_align': value,
    };

const _$TextFontEnumEnumMap = {
  TextFontEnum.baloo2: 'baloo2',
  TextFontEnum.inter: 'inter',
  TextFontEnum.outfit: 'outfit',
  TextFontEnum.caveat: 'caveat',
  TextFontEnum.cookie: 'cookie',
  TextFontEnum.dynaPuff: 'dynaPuff',
  TextFontEnum.jua: 'jua',
  TextFontEnum.gaegu: 'gaegu',
  TextFontEnum.ole: 'ole',
  TextFontEnum.puppiesPlay: 'puppiesPlay',
  TextFontEnum.rougeScript: 'rougeScript',
  TextFontEnum.moiraiOne: 'moiraiOne',
  TextFontEnum.rampartOne: 'rampartOne',
  TextFontEnum.silkscreen: 'silkscreen',
  TextFontEnum.mustachioCf: 'mustachioCf',
  TextFontEnum.rainbowRibbonCf: 'rainbowRibbonCf',
  TextFontEnum.vintageGrovee: 'vintageGrovee',
  TextFontEnum.blackout2am: 'blackout2am',
  TextFontEnum.iceAge: 'iceAge',
};

const _$TextAlignEnumMap = {
  TextAlign.left: 'left',
  TextAlign.right: 'right',
  TextAlign.center: 'center',
  TextAlign.justify: 'justify',
  TextAlign.start: 'start',
  TextAlign.end: 'end',
};

TextShadow _$TextShadowFromJson(Map<String, dynamic> json) => TextShadow(
      x: (json['x'] as num).toDouble(),
      y: (json['y'] as num).toDouble(),
      blur: (json['blur'] as num).toDouble(),
      color: json['color'] as String,
      opacity: (json['opacity'] as num).toDouble(),
    );

Map<String, dynamic> _$TextShadowToJson(TextShadow instance) =>
    <String, dynamic>{
      'x': instance.x,
      'y': instance.y,
      'blur': instance.blur,
      'color': instance.color,
      'opacity': instance.opacity,
    };

TextBackground _$TextBackgroundFromJson(Map<String, dynamic> json) =>
    TextBackground(
      source: json['source'] as String,
      scale: (json['scale'] as num).toDouble(),
      rotate: (json['rotate'] as num).toDouble(),
      translate:
          PicPosition.fromJson(json['translate'] as Map<String, dynamic>),
      type: $enumDecodeNullable(_$PicShaderTypeEnumMap, json['type']) ??
          PicShaderType.background,
    );

Map<String, dynamic> _$TextBackgroundToJson(TextBackground instance) =>
    <String, dynamic>{
      'type': _$PicShaderTypeEnumMap[instance.type]!,
      'source': instance.source,
      'scale': instance.scale,
      'rotate': instance.rotate,
      'translate': instance.translate.toJson(),
    };

const _$PicShaderTypeEnumMap = {
  PicShaderType.gradient: 'gradient',
  PicShaderType.background: 'background',
};
