// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

import 'pic_shader.dart';

part 'pic_gradient.g.dart';

@JsonSerializable()
class PicGradient extends PicShader {
  static final blackAndWhite = PicGradient(
    colors: List.of(['#000000', '#ffffff']),
    stops: List.of([.0, 1.0]),
    rotation: .0,
  );

  PicGradient({
    required this.colors,
    required this.stops,
    required this.rotation,
    super.type = PicShaderType.gradient,
  }) : assert(type == PicShaderType.gradient);

  factory PicGradient.fromLinearGradient(LinearGradient gradient) {
    return PicGradient(
      colors: gradient.colors.map((e) => e.toHex()).toList(),
      stops: gradient.stops ?? List.of([.0, 1.0]),
      rotation: (gradient.transform as GradientRotation?)?.radians ?? .0,
    );
  }

  LinearGradient toLinearGradient([bool includeRotation = true]) =>
      LinearGradient(
        colors: colors.map((e) => e.toColor()).toList(),
        stops: stops,
        transform: includeRotation ? GradientRotation(rotation) : null,
      );

  @override
  Shader linear(final Rect rect, {bool includeRotation = true}) {
    return toLinearGradient(includeRotation).createShader(rect);
  }

  @override
  Shader sweep(
    final Rect rect, {
    required double startAngle,
    required double sweepAngle,
  }) {
    return SweepGradient(
      startAngle: 0,
      endAngle: sweepAngle.abs(),
      colors: colors.map((e) => e.toColor()).toList(),
      stops: stops,
      tileMode: TileMode.decal,
      transform: GradientRotation(
        startAngle.when((it) {
          if (sweepAngle.isNegative) {
            return it + sweepAngle;
          } else {
            return it;
          }
        }),
      ),
    ).createShader(rect);
  }

  final List<String> colors;
  final List<double> stops;
  final double rotation;

  factory PicGradient.fromJson(Map<String, dynamic> json) =>
      _$PicGradientFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$PicGradientToJson(this);

  @override
  List<Object?> get props => [
        ...super.props,
        colors,
        stops,
        rotation,
      ];

  PicGradient copyWith({
    List<String>? colors,
    List<double>? stops,
    double? rotation,
  }) {
    return PicGradient(
      colors: colors ?? this.colors,
      stops: stops ?? this.stops,
      rotation: rotation ?? this.rotation,
    );
  }
}
