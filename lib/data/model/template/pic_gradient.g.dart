// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pic_gradient.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PicGradient _$PicGradientFromJson(Map<String, dynamic> json) => PicGradient(
      colors:
          (json['colors'] as List<dynamic>).map((e) => e as String).toList(),
      stops: (json['stops'] as List<dynamic>)
          .map((e) => (e as num).toDouble())
          .toList(),
      rotation: (json['rotation'] as num).toDouble(),
      type: $enumDecodeNullable(_$PicShaderTypeEnumMap, json['type']) ??
          PicShaderType.gradient,
    );

Map<String, dynamic> _$PicGradientToJson(PicGradient instance) =>
    <String, dynamic>{
      'type': _$PicShaderTypeEnumMap[instance.type]!,
      'colors': instance.colors,
      'stops': instance.stops,
      'rotation': instance.rotation,
    };

const _$PicShaderTypeEnumMap = {
  PicShaderType.gradient: 'gradient',
  PicShaderType.background: 'background',
};
