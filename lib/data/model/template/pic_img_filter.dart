// Flutter project by quanghuuxx (<EMAIL>)

import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

import '../../../features/editor/model/bottom_bar_enum.dart';
import '../../../features/editor/model/image_filter_fragment.dart';

part 'pic_img_filter.g.dart';

@JsonSerializable()
class PicImgFilter extends Equatable with UniForms {
  static const PicImgFilter none = PicImgFilter();

  final double brightness;
  final double contrast;
  final double saturation;
  final double vibrance;
  final double exposure;
  // white-balance
  final double temperature;
  final double tint;
  // highlight and shadow
  final double shadow;
  final double highlight;

  const PicImgFilter({
    this.brightness = .0,
    this.contrast = 1.0,
    this.saturation = 1.0,
    this.vibrance = .0,
    this.exposure = 1,
    this.temperature = .0,
    this.tint = .0,
    this.shadow = 1.0,
    this.highlight = 1.0,
  });

  factory PicImgFilter.from<PERSON>son(Map<String, dynamic> json) => _$PicImgFilterFromJson(json);

  Map<String, dynamic> toJson() => _$PicImgFilterToJson(this);

  /// thứ tự phải được sắp xếp đúng với các layout params trong 'shaders/image_filter_fragment.frag'
  @override
  List<double> get uniForms => [
        doubleFixed(brightness, fractionDigits: FilterBottomBarItem.brightness.fractionDigits),
        doubleFixed(exposure, fractionDigits: FilterBottomBarItem.exposure.fractionDigits),
        doubleFixed(temperature, fractionDigits: FilterBottomBarItem.temperature.fractionDigits),
        doubleFixed(shadow, fractionDigits: FilterBottomBarItem.shadow.fractionDigits),
        doubleFixed(contrast, fractionDigits: FilterBottomBarItem.contrast.fractionDigits),
        doubleFixed(saturation, fractionDigits: FilterBottomBarItem.saturation.fractionDigits),
        doubleFixed(highlight, fractionDigits: FilterBottomBarItem.highlight.fractionDigits),
        doubleFixed(vibrance, fractionDigits: FilterBottomBarItem.vibrance.fractionDigits),
        doubleFixed(tint, fractionDigits: FilterBottomBarItem.tint.fractionDigits),
      ];

  @override
  List<Object?> get props {
    return [
      brightness,
      contrast,
      saturation,
      vibrance,
      exposure,
      temperature,
      tint,
      shadow,
      highlight,
    ];
  }

  PicImgFilter copyWith({
    double? brightness,
    double? contrast,
    double? saturation,
    double? vibrance,
    double? exposure,
    double? temperature,
    double? tint,
    double? shadow,
    double? highlight,
  }) {
    return PicImgFilter(
      brightness: brightness ?? this.brightness,
      contrast: contrast ?? this.contrast,
      saturation: saturation ?? this.saturation,
      vibrance: vibrance ?? this.vibrance,
      exposure: exposure ?? this.exposure,
      temperature: temperature ?? this.temperature,
      tint: tint ?? this.tint,
      shadow: shadow ?? this.shadow,
      highlight: highlight ?? this.highlight,
    );
  }
}
