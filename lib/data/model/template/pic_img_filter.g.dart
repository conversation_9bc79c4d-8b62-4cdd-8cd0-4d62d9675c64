// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pic_img_filter.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PicImgFilter _$PicImgFilterFromJson(Map<String, dynamic> json) => PicImgFilter(
      brightness: (json['brightness'] as num?)?.toDouble() ?? 0.0,
      contrast: (json['contrast'] as num?)?.toDouble() ?? 1.0,
      saturation: (json['saturation'] as num?)?.toDouble() ?? 1.0,
      vibrance: (json['vibrance'] as num?)?.toDouble() ?? 0.0,
      exposure: (json['exposure'] as num?)?.toDouble() ?? 1,
      temperature: (json['temperature'] as num?)?.toDouble() ?? 0.0,
      tint: (json['tint'] as num?)?.toDouble() ?? 0.0,
      shadow: (json['shadow'] as num?)?.toDouble() ?? 1.0,
      highlight: (json['highlight'] as num?)?.toDouble() ?? 1.0,
    );

Map<String, dynamic> _$PicImgFilterToJson(PicImgFilter instance) =>
    <String, dynamic>{
      'brightness': instance.brightness,
      'contrast': instance.contrast,
      'saturation': instance.saturation,
      'vibrance': instance.vibrance,
      'exposure': instance.exposure,
      'temperature': instance.temperature,
      'tint': instance.tint,
      'shadow': instance.shadow,
      'highlight': instance.highlight,
    };
