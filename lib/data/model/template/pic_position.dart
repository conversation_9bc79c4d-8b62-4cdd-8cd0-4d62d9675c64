// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:ui' show Offset;

import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'pic_position.g.dart';

@JsonSerializable()
class PicPosition extends Equatable {
  static const PicPosition zero = PicPosition(x: 0, y: 0);

  const PicPosition({
    required this.x,
    required this.y,
  });

  final double x;
  final double y;

  factory PicPosition.fromOffset(Offset offset) => PicPosition(x: offset.dx, y: offset.dy);

  factory PicPosition.fromJson(Map<String, dynamic> json) => _$PicPositionFromJson(json);
  Map<String, dynamic> toJson() => _$PicPositionToJson(this);

  @override
  List<Object?> get props => [
        x,
        y,
      ];

  PicPosition copyWith({
    double? x,
    double? y,
  }) {
    return PicPosition(
      x: x ?? this.x,
      y: y ?? this.y,
    );
  }
}
