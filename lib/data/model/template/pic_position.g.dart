// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pic_position.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PicPosition _$PicPositionFromJson(Map<String, dynamic> json) => PicPosition(
      x: (json['x'] as num).toDouble(),
      y: (json['y'] as num).toDouble(),
    );

Map<String, dynamic> _$PicPositionToJson(PicPosition instance) =>
    <String, dynamic>{
      'x': instance.x,
      'y': instance.y,
    };
