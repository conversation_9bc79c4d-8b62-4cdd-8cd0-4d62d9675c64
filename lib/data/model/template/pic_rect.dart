// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:ui';

import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'pic_rect.g.dart';

@JsonSerializable()
class PicRect extends Equatable {
  final double x;
  final double y;
  final double w;
  final double h;

  PicRect({
    required this.x,
    required this.y,
    required this.w,
    required this.h,
  });

  factory PicRect.fromSize(double w, double h) => PicRect(x: 0, y: 0, w: w, h: h);

  factory PicRect.fromCenter(Offset center, Size size) =>
      PicRect(x: center.dx - size.width / 2, y: center.dy - size.height / 2, w: size.width, h: size.height);

  factory PicRect.fromLTRB(double left, double top, double right, double bottom) =>
      PicRect(x: left, y: top, w: right - left, h: bottom - top);

  factory PicRect.fromRect(Rect rect) => PicRect(x: rect.left, y: rect.top, w: rect.width, h: rect.height);

  factory PicRect.fromPoints(Offset min, Offset max) {
    return PicRect(
      x: min.dx,
      y: min.dy,
      w: max.dx - min.dx,
      h: max.dy - min.dy,
    );
  }

  Rect toRect() => Rect.fromLTWH(x, y, w, h);

  factory PicRect.fromJson(Map<String, dynamic> json) => _$PicRectFromJson(json);

  Map<String, dynamic> toJson() => _$PicRectToJson(this);

  Offset get center => Offset(x + w / 2, y + h / 2);

  Offset get topCenter => Offset(x + w / 2, y);

  Offset get bottomCenter => Offset(x + w / 2, y + h);

  Offset get leftCenter => Offset(x, y + h / 2);

  Offset get rightCenter => Offset(x + w, y + h / 2);

  Offset get topLeft => Offset(x, y);

  Offset get topRight => Offset(x + w, y);

  Offset get bottomLeft => Offset(x, y + h);

  Offset get bottomRight => Offset(x + w, y + h);

  Size get size => Size(w, h);

  @override
  List<Object> get props => [x, y, w, h];

  PicRect shift(Offset offset) {
    return PicRect(x: x + offset.dx, y: y + offset.dy, w: w, h: h);
  }
}
