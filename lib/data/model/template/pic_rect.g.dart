// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pic_rect.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PicRect _$PicRectFromJson(Map<String, dynamic> json) => PicRect(
      x: (json['x'] as num).toDouble(),
      y: (json['y'] as num).toDouble(),
      w: (json['w'] as num).toDouble(),
      h: (json['h'] as num).toDouble(),
    );

Map<String, dynamic> _$PicRectToJson(PicRect instance) => <String, dynamic>{
      'x': instance.x,
      'y': instance.y,
      'w': instance.w,
      'h': instance.h,
    };
