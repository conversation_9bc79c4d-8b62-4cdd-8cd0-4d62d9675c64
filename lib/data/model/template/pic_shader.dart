import 'dart:ui';

import 'package:equatable/equatable.dart';

import 'pic_element_text.dart';
import 'pic_gradient.dart';

abstract class <PERSON>c<PERSON>had<PERSON> extends Equatable {
  final PicShaderType type;

  PicShader({required this.type});

  factory PicShader.fromJson(Map<String, dynamic> json) {
    switch (json['type']) {
      case 'gradient':
        return PicGradient.fromJson(json);
      case 'background':
        return TextBackground.fromJson(json);
      default:
        throw ArgumentError('Unknown shader type: ${json['type']}');
    }
  }

  Map<String, dynamic> toJson();

  Shader linear(final Rect rect, {bool includeRotation = true});

  Shader sweep(
    final Rect rect, {
    required double startAngle,
    required double sweepAngle,
  });

  @override
  List<Object?> get props => [type];
}

enum PicShaderType {
  gradient,
  background,
}
