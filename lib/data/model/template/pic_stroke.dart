// Flutter project by quanghuuxx (<EMAIL>)

import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'pic_stroke.g.dart';

@JsonSerializable()
class PicStroke extends Equatable {
  static const double maxThickness = 0.3;

  const PicStroke({
    required this.thickness,
    required this.color,
    this.opacity = 1,
  });

  final double thickness;
  final String color;
  final double opacity;

  factory PicStroke.fromJson(Map<String, dynamic> json) => _$PicStrokeFromJson(json);
  Map<String, dynamic> toJson() => _$PicStrokeToJson(this);

  @override
  List<Object?> get props => [
        thickness,
        color,
      ];

  PicStroke copyWith({
    double? thickness,
    String? color,
    double? opacity,
  }) {
    return PicStroke(
      thickness: thickness ?? this.thickness,
      color: color ?? this.color,
      opacity: opacity ?? this.opacity,
    );
  }
}
