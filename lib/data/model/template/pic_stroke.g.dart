// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pic_stroke.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PicStroke _$PicStrokeFromJson(Map<String, dynamic> json) => PicStroke(
      thickness: (json['thickness'] as num).toDouble(),
      color: json['color'] as String,
      opacity: (json['opacity'] as num?)?.toDouble() ?? 1,
    );

Map<String, dynamic> _$PicStrokeToJson(PicStroke instance) => <String, dynamic>{
      'thickness': instance.thickness,
      'color': instance.color,
      'opacity': instance.opacity,
    };
