// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:core/core.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

import '../../../features/editor/model/image_data.dart';
import '../../data.dart';

part 'pic_template.g.dart';

@JsonSerializable()
class PicTemplate extends Equatable {
  final int? id;
  final int? savedAt;
  final SaveStatus? saveStatus;
  final String originImg;
  final double opacity;
  final double? ratio;
  final double rotation;
  final PicFlipEnum? flipEnum;
  final String? snapshot;

  /// phạm vi ranh giới hiển thị của hình ảnh gốc,
  /// nếu background template k được crop thì imageBounds = PicRect(0, 0, img.width, img.height),
  /// còn nếu được cắt thì imageBounds sẽ nằm trong phạm vi PicRect(0, 0, img.width, img.height)
  /// dùng để crop ảnh khi load template từ cache và gán cho [PreloadImageObject]
  final PicRect imageBounds;
  final PicBlur? blur;
  final PicImgFilter filter;

  final List<PicElement> elements;

  PicTemplate({
    this.id,
    this.savedAt,
    this.saveStatus,
    required this.originImg,
    this.opacity = 1,
    this.ratio,
    this.rotation = .0,
    this.flipEnum,
    this.snapshot,
    required this.imageBounds,
    this.blur,
    this.filter = PicImgFilter.none,
    required this.elements,
  });

  factory PicTemplate.fromJson(Map<String, dynamic> json) => _$PicTemplateFromJson(json);

  Map<String, dynamic> toJson() => _$PicTemplateToJson(this);

  @override
  List<Object?> get props {
    return [
      id,
      savedAt,
      saveStatus,
      originImg,
      opacity,
      ratio,
      rotation,
      flipEnum,
      snapshot,
      imageBounds,
      blur,
      filter,
      elements,
    ];
  }

  PicTemplate copyWith({
    int? id,
    int? savedAt,
    SaveStatus? saveStatus,
    String? originImg,
    double? opacity,
    double? ratio,
    double? rotation,
    PicFlipEnum? flipEnum,
    String? snapshot,
    PicRect? imageBounds,
    PicBlur? blur,
    PicImgFilter? filter,
    List<PicElement>? elements,
    bool ratioNullable = false,
    bool flipEnumNullable = false,
    bool blurNullable = false,
  }) {
    return PicTemplate(
      id: id ?? this.id,
      savedAt: savedAt ?? this.savedAt,
      saveStatus: saveStatus ?? this.saveStatus,
      originImg: originImg ?? this.originImg,
      opacity: opacity ?? this.opacity,
      ratio: _includeNull(ratio, this.ratio, nullable: ratioNullable),
      rotation: rotation ?? this.rotation,
      flipEnum: _includeNull(flipEnum, this.flipEnum, nullable: flipEnumNullable),
      snapshot: snapshot ?? this.snapshot,
      imageBounds: imageBounds ?? this.imageBounds,
      blur: _includeNull(blur, this.blur, nullable: blurNullable),
      filter: filter ?? this.filter,
      elements: elements ?? this.elements,
    );
  }

  T? _includeNull<T>(T? value, T? old, {bool nullable = false}) {
    if (nullable) {
      return value;
    }
    return value ?? old;
  }

  String buildSnapshot() {
    assert(snapshot != null, 'Snapshot is null');
    return '${PathUtil.documentsPath}/$snapshot';
  }
}

class PicEditingTemplate extends PicTemplate {
  PicEditingTemplate({
    super.id,
    super.savedAt,
    super.saveStatus,
    required super.originImg,
    super.opacity = 1,
    super.ratio,
    super.rotation = .0,
    super.flipEnum,
    super.snapshot,
    required super.imageBounds,
    super.blur,
    super.filter = PicImgFilter.none,
    required super.elements,
  });

  @override
  PicEditingTemplate copyWith({
    int? id,
    int? savedAt,
    SaveStatus? saveStatus,
    String? originImg,
    double? opacity,
    double? ratio,
    double? rotation,
    PicFlipEnum? flipEnum,
    String? snapshot,
    PicRect? imageBounds,
    PicBlur? blur,
    PicImgFilter? filter,
    List<PicElement>? elements,
    bool ratioNullable = false,
    bool flipEnumNullable = false,
    bool blurNullable = false,
  }) {
    return PicEditingTemplate(
      id: id ?? this.id,
      savedAt: savedAt ?? this.savedAt,
      saveStatus: saveStatus ?? this.saveStatus,
      originImg: originImg ?? this.originImg,
      opacity: opacity ?? this.opacity,
      ratio: _includeNull(ratio, this.ratio, nullable: ratioNullable),
      rotation: rotation ?? this.rotation,
      flipEnum: _includeNull(flipEnum, this.flipEnum, nullable: flipEnumNullable),
      snapshot: snapshot ?? this.snapshot,
      imageBounds: imageBounds ?? this.imageBounds,
      blur: _includeNull(blur, this.blur, nullable: blurNullable),
      filter: filter ?? this.filter,
      elements: elements ?? this.elements,
    );
  }

  factory PicEditingTemplate.fromPicTemplate(PicTemplate template) {
    return PicEditingTemplate(
      id: template.id,
      savedAt: template.savedAt,
      saveStatus: template.saveStatus,
      originImg: template.originImg,
      opacity: template.opacity,
      ratio: template.ratio,
      rotation: template.rotation,
      flipEnum: template.flipEnum,
      snapshot: template.snapshot,
      imageBounds: template.imageBounds,
      blur: template.blur,
      filter: template.filter,
      elements: template.elements,
    );
  }

  // một dạng key định danh cho template, được dùng để lưu cache các data
  // của template, ví dụ như lưu [ImageData] của image trong [PreloadImageObject]
  String get identifier {
    return 'pic_editing_template_${id ?? -1}';
  }

  ui.Image get image {
    return PreloadImageObject.instance.get(identifier).image;
  }

  Uint8List get uint8list {
    return PreloadImageObject.instance.get(identifier).uint8list;
  }
}
