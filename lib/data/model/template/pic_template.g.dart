// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pic_template.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PicTemplate _$PicTemplateFromJson(Map<String, dynamic> json) => PicTemplate(
      id: (json['id'] as num?)?.toInt(),
      savedAt: (json['saved_at'] as num?)?.toInt(),
      saveStatus: $enumDecodeNullable(_$SaveStatusEnumMap, json['save_status']),
      originImg: json['origin_img'] as String,
      opacity: (json['opacity'] as num?)?.toDouble() ?? 1,
      ratio: (json['ratio'] as num?)?.toDouble(),
      rotation: (json['rotation'] as num?)?.toDouble() ?? .0,
      flipEnum: $enumDecodeNullable(_$PicFlipEnumEnumMap, json['flip_enum']),
      snapshot: json['snapshot'] as String?,
      imageBounds:
          PicRect.fromJson(json['image_bounds'] as Map<String, dynamic>),
      blur: json['blur'] == null
          ? null
          : PicBlur.fromJson(json['blur'] as Map<String, dynamic>),
      filter: json['filter'] == null
          ? PicImgFilter.none
          : PicImgFilter.fromJson(json['filter'] as Map<String, dynamic>),
      elements: (json['elements'] as List<dynamic>)
          .map((e) => PicElement.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$PicTemplateToJson(PicTemplate instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.savedAt case final value?) 'saved_at': value,
      if (_$SaveStatusEnumMap[instance.saveStatus] case final value?)
        'save_status': value,
      'origin_img': instance.originImg,
      'opacity': instance.opacity,
      if (instance.ratio case final value?) 'ratio': value,
      'rotation': instance.rotation,
      if (_$PicFlipEnumEnumMap[instance.flipEnum] case final value?)
        'flip_enum': value,
      if (instance.snapshot case final value?) 'snapshot': value,
      'image_bounds': instance.imageBounds.toJson(),
      if (instance.blur?.toJson() case final value?) 'blur': value,
      'filter': instance.filter.toJson(),
      'elements': instance.elements.map((e) => e.toJson()).toList(),
    };

const _$SaveStatusEnumMap = {
  SaveStatus.draft: 'draft',
  SaveStatus.export: 'export',
};

const _$PicFlipEnumEnumMap = {
  PicFlipEnum.horizontal: 'horizontal',
  PicFlipEnum.vertical: 'vertical',
  PicFlipEnum.both: 'both',
};
