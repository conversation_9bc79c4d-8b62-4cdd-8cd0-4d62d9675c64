// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../resource/style/app_theme_ext.dart';
import 'page/arc_path.dart';
import 'page/component.dart';
import 'page/it_test.dart';

class DevScreen extends StatefulWidget {
  const DevScreen({super.key});

  @override
  State<DevScreen> createState() => _DevScreenState();
}

class _DevScreenState extends AppScreenState<DevScreen> {
  final PageController pageController = PageController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 10,
        title: const Text('Dev'),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(40),
          child: Pagebar(
            controller: pageController,
            itemCount: PageDev.values.length,
            itemBuilder: (context, index, selected) {
              final page = PageDev.values[index];
              return ActionChip(
                backgroundColor: selected ? theme.themeColor.primary : null,
                label: Text(page.title),
                onPressed: () {
                  pageController.jumpToPage(index);
                },
              );
            },
          ),
        ),
      ),
      body: PageView.builder(
        itemCount: PageDev.values.length,
        controller: pageController,
        itemBuilder: (context, index) {
          final page = PageDev.values[index];
          return switch (page) {
            PageDev.component => const Component(),
            PageDev.arc_path => const ArcPath(),
            PageDev.it_test => const ItTest(),
          };
        },
      ),
    );
  }

  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }
}

enum PageDev {
  component,
  arc_path,
  it_test;

  String get title => switch (this) {
        component => 'Component',
        arc_path => 'Arc Path',
        it_test => 'It Test',
      };
}
