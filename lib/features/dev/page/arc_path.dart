// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:math';

import 'package:flutter/material.dart';

import '../../../component/util/arc_util.dart';

class ArcPath extends StatefulWidget {
  const ArcPath({super.key});

  @override
  State<ArcPath> createState() => _ArcPathState();
}

class _ArcPathState extends State<ArcPath> {
  final TextPainter _painter = TextPainter(textDirection: TextDirection.ltr);

  String content = 'quanghuu.xx\nflutter.dev';

  double _value = 0;

  Size originSize = Size.zero;

  @override
  void initState() {
    super.initState();

    _painter
      ..text = TextSpan(text: content)
      ..layout();

    originSize = _painter.size;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Expanded(
          child: CustomPaint(
            size: MediaQuery.sizeOf(context),
            painter: _<PERSON>(
              factor: double.parse(_value.toStringAsFixed(2)),
              content: content,
              painter: _painter,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 48),
          child: Slider(
            min: -1.0,
            max: 1.0,
            value: _value,
            label: _value.toStringAsFixed(2),
            onChanged: (v) {
              setState(() {
                _value = v;
              });
            },
          ),
        ),
      ],
    );
  }
}

class _Painter extends CustomPainter {
  final double factor;
  final String content;
  final TextPainter painter;

  _Painter({
    required this.factor,
    required this.content,
    required this.painter,
  });

  @override
  void paint(Canvas canvas, Size size) {
    Rect rect = Offset.zero & size;
    Rect bounds = rect;
    double sweepAngle = 0.0, startAngle = 0.0;
    final radians = angle360 * factor;

    if (radians != 0.0) {
      painter
        ..text = TextSpan(text: content.replaceAll('\n', ' '))
        ..layout();

      final focalPoint = size.center(Offset.zero);
      rect = computeArcRect(
        focalPoint: focalPoint,
        radians: radians,
        arcLength: painter.width,
      );

      sweepAngle = _getSweepAngle(rect.width / 2);
      if (radians.isNegative) {
        startAngle = sweepAngle / 2;
        startAngle = pi / 2 - startAngle;
      } else {
        startAngle = -sweepAngle / 2;
        startAngle = startAngle - pi / 2;
      }

      bounds = getArcBounds(rect, startAngle, sweepAngle);

      final shift = focalPoint - bounds.center;
      rect = rect.shift(shift);
      bounds = bounds.shift(shift);
    }

    canvas
      ..drawRect(rect, Paint()..color = Colors.black12)
      ..drawArc(
        rect,
        startAngle,
        sweepAngle,
        true,
        Paint()..color = Colors.blueAccent,
      )
      ..drawRect(
        bounds,
        Paint()
          ..color = Colors.red
          ..strokeWidth = 1.5
          ..style = PaintingStyle.stroke,
      )
      ..drawCircle(size.center(Offset.zero), 3, Paint()..color = Colors.yellow)
      ..drawCircle(bounds.center, 2, Paint()..color = Colors.red);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }

  /// tính toán tổng góc quét dùng để vẽ được hết nội dung của [text]
  double _getSweepAngle(double effectiveRadius) {
    double total = 0;
    for (final char in content.characters) {
      painter
        ..text = TextSpan(text: char)
        ..layout(minWidth: 0, maxWidth: double.maxFinite);

      total += 2 * asin(painter.width / (2 * effectiveRadius));
    }

    return total;
  }
}
