// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../component/dependency_injector/di.dart';
import '../../../component/services/alert_dialog_service/alert_dialog_service.dart';
import '../../../resource/localization/lkey.dart';
import '../../../resource/lottie_constants.dart';
import '../../../resource/style/app_theme_ext.dart';

class Component extends StatefulWidget {
  const Component({super.key});

  @override
  State<Component> createState() => _ComponentState();
}

class _ComponentState extends State<Component> {
  double value = 0;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Material(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Slider(
            value: value,
            max: 10,
            min: 0,
            label: value.toStringAsFixed(1),
            onChanged: (value) {
              setState(() {
                this.value = value;
              });
            },
          ),
          TextButton(
            onPressed: () {
              toast.show(context, message: 'Hello ${value.toStringAsFixed(1)}');
            },
            child: const Text('Test toast'),
          ),
          TextButton(
            onPressed: () {
              it.get<AlertDialogService>().add(
                    AlertDialogInfo(
                      contents: [
                        AlertDialogContent(
                          content: LottieWidget(
                            LottiesConstants.crown,
                            width: 100,
                            height: 100,
                          ),
                          type: AlertDialogContentType.widget,
                        ),
                        AlertDialogContent(
                          content: context.tr(LKey.iap_upgraded_to_premium),
                          type: AlertDialogContentType.text,
                          argument: theme.themeText.headline6.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        AlertDialogContent(
                          content: context.tr(LKey.iap_upgraded_to_premium_sub_1),
                          type: AlertDialogContentType.text,
                        ),
                        AlertDialogContent(
                          content: context.tr(LKey.iap_upgraded_to_premium_sub_2),
                          type: AlertDialogContentType.text,
                        ),
                      ],
                      actions: [
                        AlertDialogAction(
                          title: context.tr(LKey.common_ok),
                          action: (context) {
                            Navigator.pop(context);
                          },
                        ),
                      ],
                    ),
                  );
            },
            child: const Text('Test snackbar'),
          ),
        ],
      ),
    );
  }
}
