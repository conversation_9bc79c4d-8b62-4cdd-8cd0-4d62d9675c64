// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';
import 'package:it/it.dart';

class ItTest extends StatefulWidget {
  const ItTest({super.key});

  @override
  State<ItTest> createState() => _ItTestState();
}

class _ItTestState extends State<ItTest> {
  int counter = 0;

  @override
  Widget build(BuildContext context) {
    return It(
      child: ViewModel.create(
        create: () => _ViewModel(),
        child: Builder(
          builder: (context) {
            context.observe<int>((_, v) => counter = v);

            return Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const _Text(),
                _Text(transform: (stream) => stream.map((e) => e * 2)),
                const _Text2(),
                kBox24,
                TextButton(
                  onPressed: () {
                    ViewModel.of<_ViewModel>(context).increment(counter);
                  },
                  child: const Text('Increment'),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}

class _Text extends StatelessWidget {
  const _Text({
    this.transform,
  });

  final ItTransformer<int>? transform;

  @override
  Widget build(BuildContext context) {
    final value = context.on<int>(transform: transform) ?? 0;

    return Text(value.toString());
  }
}

class _Text2 extends StatelessWidget {
  const _Text2();

  @override
  Widget build(BuildContext context) {
    final value = context.on<String>() ?? '_';

    return Text(value);
  }
}

class _ViewModel extends ViewModel {
  _ViewModel() {
    listen<int>((old, v) => fire('$old -> $v'));
  }

  void increment(int value) {
    fire(value + 1);
  }
}
