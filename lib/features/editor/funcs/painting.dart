// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/widgets.dart';

void paintLineGridLayout(
  final Canvas canvas, {
  required final Rect rect,
  Paint? paint,
}) {
  final w = rect.width / 3;
  final h = rect.height / 3;

  (paint ??= Paint())
    ..style = PaintingStyle.stroke
    ..strokeWidth = 1
    ..blendMode = BlendMode.srcOver;

  canvas.drawRect(rect, paint);

  for (int i = 1; i < 3; i++) {
    final x = rect.left + w * i;
    final y = rect.top + h * i;
    canvas
      ..drawLine(Offset(x, rect.top), Offset(x, rect.bottom), paint)
      ..drawLine(Offset(rect.left, y), Offset(rect.right, y), paint);
  }
}
