// Flutter project by quang<PERSON>uxx (<EMAIL>)

import 'dart:io';
import 'dart:math';
import 'dart:typed_data';

import 'package:core/core.dart';
import 'package:flutter/widgets.dart';
import 'package:image/image.dart' as image;

import '../../../configurations/app_error.dart';
import '../../../data/model/enum/pic_flip_enum.dart';

Size caculateFittedSizeOf(
  BuildContext context,
  final Size inputSize, {
  final BoxFit fit = BoxFit.contain,
}) {
  final outputSize = MediaQuery.sizeOf(context);
  return caculateFittedSize(inputSize, outputSize, fit: fit);
}

Size caculateFittedSize(
  final Size inputSize,
  final Size outputSize, {
  final BoxFit fit = BoxFit.contain,
}) {
  return applyBoxFit(fit, inputSize, outputSize).destination;
}

Matrix4 caculateImageMatrix(
  final Size inputSize,
  final Size outputSize, {
  double? rotation,
  PicFlipEnum? flipEnum,
}) {
  // scale lại theo kích cỡ [outputSize]
  Matrix4 _matrix = Matrix4.diagonal3Values(
    outputSize.width / inputSize.width,
    outputSize.height / inputSize.height,
    1.0,
  );

  // Lưu ý: dùng tâm của [inputSize] để translate _matrix vì
  // ban đầu _matrix đã được scale theo kích thức [outputSize],
  // nên khi translate thông số [center] sẽ được scale theo thông số đang có của _matrix
  final center = inputSize.center(Offset.zero);

  if (rotation != null && rotation != 0.0) {
    final double sinAngle = sin(rotation).abs();
    final double cosAngle = cos(rotation).abs();

    // Tính kích thước bounding box mới sau xoay
    final double newWidth = outputSize.width * cosAngle + outputSize.height * sinAngle;
    final double newHeight = outputSize.width * sinAngle + outputSize.height * cosAngle;

    // Tính tỉ lệ scale để fill được kích thước [outputSize] sau khi xoay
    final scale = max(newWidth / outputSize.width, newHeight / outputSize.height);

    _matrix *= Matrix4.identity()
      ..translate(center.dx, center.dy)
      ..rotateZ(rotation)
      ..scale(scale)
      ..translate(-center.dx, -center.dy);
  }

  if (flipEnum != null) {
    if (flipEnum == PicFlipEnum.horizontal || flipEnum == PicFlipEnum.both) {
      _matrix *= Matrix4.identity()
        ..translate(center.dx, .0)
        ..rotateY(pi)
        ..translate(-center.dx, .0);
    }

    if (flipEnum == PicFlipEnum.vertical || flipEnum == PicFlipEnum.both) {
      _matrix *= Matrix4.identity()
        ..translate(.0, center.dy)
        ..rotateX(pi)
        ..translate(.0, -center.dy);
    }
  }

  return _matrix;
}

Matrix4 caculateRotationAndFittedMatrix(
  final double rotation,
  final Size fittedSize,
) {
  if (rotation == 0.0) {
    return Matrix4.identity();
  }

  final Offset center = fittedSize.center(Offset.zero);
  final double sinAngle = sin(rotation).abs();
  final double cosAngle = cos(rotation).abs();

  // Tính kích thước bounding box mới sau xoay
  final double newWidth = fittedSize.width * cosAngle + fittedSize.height * sinAngle;
  final double newHeight = fittedSize.width * sinAngle + fittedSize.height * cosAngle;

  // Tính tỉ lệ scale để ảnh fill được kích thước view,
  // Chọn tỉ lệ lớn nhất để đảm bảo fill đủ vùng hiển thị [fittedSize]
  final double scale = max(newWidth / fittedSize.width, newHeight / fittedSize.height);

  return Matrix4.identity()
    ..translate(center.dx, center.dy)
    ..rotateZ(rotation)
    ..scale(scale)
    ..translate(-center.dx, -center.dy);
}

Future<({Uint8List bytes, Rect rect})> cropImage(
  Rect rect,
  String originImg,
) async {
  final file = File(originImg);
  Uint8List bytes = await file.readAsBytes();
  final decode = image.decodeImage(bytes);
  if (decode == null) {
    throw BaseException(
      code: AppError.somethingWrong.code,
      description: 'Can`t decode image by path: $originImg',
    );
  }

  rect = (Offset.zero & Size(decode.width.toDouble(), decode.height.toDouble())).intersect(rect);

  final croped = image.copyCrop(
    decode,
    x: rect.left.toInt(),
    y: rect.top.toInt(),
    width: rect.width.toInt(),
    height: rect.height.toInt(),
  );

  final recognizer = PathUtil.recognizer(originImg);
  switch (recognizer) {
    case 'jpg':
    case 'jpeg':
      bytes = image.encodeJpg(croped);
      break;
    case 'png':
      bytes = image.encodePng(croped);
      break;
    case 'gif':
      bytes = image.encodeGif(croped);
      break;
    case 'ico':
      bytes = image.encodeIco(croped);
      break;
  }

  return (bytes: bytes, rect: rect);
}
