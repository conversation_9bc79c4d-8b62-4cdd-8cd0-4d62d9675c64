// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:io';
import 'dart:isolate';
import 'dart:typed_data';
import 'dart:ui';

import 'package:core/core.dart';
import 'package:flutter/rendering.dart';

import '../../../configurations/app_error.dart';
import '../../../data/data.dart';
import '../funcs/rendering.dart';
import '../repository/editor_repository.dart';

abstract class EditorInteractor {
  Future<({Uint8List bytes, Rect rect})> cropBackground(
    Rect rect,
    String originImg,
  );

  Future<PicTemplate> saveTemplate(
    PicTemplate template, {
    required RenderRepaintBoundary render,
    required double pixelRatio,
  });

  Future<bool> saveExportSlot(int slot);
  int getExportSlot();
}

class EditorInteractorImpl implements EditorInteractor {
  final EditorRepository repository;

  EditorInteractorImpl({
    required this.repository,
  });

  @override
  Future<({Uint8List bytes, Rect rect})> cropBackground(
    Rect rect,
    String originImg,
  ) {
    return Isolate.run(() => cropImage(rect, originImg));
  }

  @override
  Future<PicTemplate> saveTemplate(
    PicTemplate template, {
    required RenderRepaintBoundary render,
    required double pixelRatio,
  }) async {
    final image = await render.toImage(pixelRatio: pixelRatio);
    final bytes = await image.toByteData(format: ImageByteFormat.png);
    if (bytes == null) {
      throw BaseException(
        code: AppError.somethingWrong.code,
        description: 'Can`t get bytes from image',
      );
    }

    String snapshot = '$uuid.png';
    if (template.snapshot != null) {
      snapshot = PathUtil.basename(template.snapshot!);
    }
    final p = File('${PathUtil.documentsPath}/$snapshot');
    if (await p.exists()) {
      await p.delete();
    }
    await p.writeAsBytes(bytes.buffer.asUint8List(), flush: true);
    final id = repository.saveTemplate(
      template.copyWith(
        snapshot: snapshot,
        savedAt: DateUtil.timestamp,
      ),
    );

    return template.copyWith(id: id, snapshot: snapshot);
  }

  @override
  int getExportSlot() {
    return repository.getExportSlot();
  }

  @override
  Future<bool> saveExportSlot(int slot) {
    return repository.saveExportSlot(slot);
  }
}
