// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:async';

import 'package:core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

import '../../../component/widget/inherited_consumer.dart';
import '../providers/bottom_bar_provider.dart';
import '../providers/editor_providers.dart';
import '../view/editor_screen.dart';

mixin StateBottomActionMixin<T extends StatefulWidget> on State<T> implements IBottomAction {
  late final PicTemplateEditor templateEditor = context.read(picTemplateEditorProvider.notifier);
  late final BottomBarEditor bottomBarEditor = context.read(bottomBarEditorProvider.notifier);

  Timer? _scheduleCaptureTimer;

  @override
  void initState() {
    super.initState();
    templateEditor; // trigger instance
    bottomBarEditor; // trigger instance

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      bottomBarEditor.pushAction(this);
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      bottomBarEditor.popAction();
    });
    super.dispose();
  }

  @override
  void onPopState() {
    templateEditor.pop();
    _scheduleCapture();
    bottomBarEditor.popBottom();
  }

  @override
  void onSubmitState() {
    _scheduleCapture();
    bottomBarEditor.popBottom();
  }

  void _scheduleCapture() {
    final render = EditorScreen.area(context).currentContext.findRenderObject() as RenderRepaintBoundary;
    final pixelRatio = MediaQuery.devicePixelRatioOf(context);

    _scheduleCaptureTimer?.cancel();
    _scheduleCaptureTimer = Timer(const Duration(milliseconds: 500), () {
      runImmediatelyOrAfterFrame(() {
        final bottomEnum = bottomBarEditor.currentBottom;
        if (bottomEnum?.canCapture == false) return;
        templateEditor.submit(
          render,
          pixelRatio: pixelRatio,
        );
      });
    });
  }
}

abstract class IBottomAction {
  void onPopState();

  void onSubmitState();
}
