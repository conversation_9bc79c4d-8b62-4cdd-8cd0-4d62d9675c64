// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/painting.dart';
import '../../../component/util/arc_util.dart';
import '../../../data/data.dart';
import '../model/arc_text_computer.dart';

mixin ElementTextLayoutMixin {
  static final TextPainter _painter =
      TextPainter(textDirection: TextDirection.ltr);
  static final ArcTextComputer _arcComputer =
      ArcTextComputer(TextDirection.ltr);

  TextStyle getTextStyle(final TextMetadata metadata) {
    return metadata.fontEnum.builder(
      fontSize: metadata.fontSize,
      color: metadata.stroke != null ? null : metadata.color?.toColor(),
      foreground: metadata.stroke.when(
        (it) {
          if (it != null && it.opacity > 0 && it.thickness > 0) {
            return Paint()
              ..style = PaintingStyle.stroke
              ..color = it.color.toColor().withValues(alpha: it.opacity)
              ..strokeWidth = metadata.fontSize * it.thickness;
          }
          return null;
        },
      ),
      shadows: metadata.shadow.when(
        (it) {
          if (it != null && it.opacity > 0) {
            return <Shadow>[
              Shadow(
                color: it.color.toColor().withValues(alpha: it.opacity),
                offset: Offset(it.x, it.y),
                blurRadius: it.blur,
              ),
            ];
          }
          return null;
        },
      ),
    );
  }

  Size layoutSized(
    final String content, {
    final TextStyle? style,
    double minWidth = 0.0,
    double maxWidth = double.infinity,
  }) {
    _painter.text = TextSpan(text: content, style: style);
    _painter.layout(
      minWidth: minWidth,
      maxWidth: maxWidth,
    );
    return _painter.size;
  }

  Rect layoutWithBend(
    final String content, {
    final Offset center = Offset.zero,
    final TextStyle? style,
    required double bend,
  }) {
    _arcComputer.layout(
      content,
      style: style,
      radians: angle360 * bend,
      center: center,
    );
    return _arcComputer.bounds;
  }

  ArcComputerResult getArcResult(
    final String content, {
    final Offset center = Offset.zero,
    final TextStyle? style,
    required double bend,
  }) {
    _arcComputer.layout(
      content,
      style: style,
      radians: angle360 * bend,
      center: center,
    );
    return _arcComputer.getResult();
  }
}
