// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:math';

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../../component/util/arc_util.dart';
import '../../../component/util/str_util.dart';

class ArcTextComputer {
  ArcTextComputer(final TextDirection direction) : _painter = TextPainter(textDirection: direction);

  final TextPainter _painter;

  double get sweepAngle => _sweepAngle;
  double _sweepAngle = 0.0;

  double get startAngle => _startAngle;
  double _startAngle = 0.0;

  Rect _rect = Rect.zero;

  Size _contentSized = Size.zero;
  Size get contentSized => _contentSized;

  Rect get bounds => _bounds.inflate(_contentSized.height / 2);

  Rect _bounds = Rect.zero;

  void layout(
    final String content, {
    final TextStyle? style,
    required double radians,
    Offset center = Offset.zero,
  }) {
    final str = justLine(content);
    _painter
      ..text = TextSpan(text: str, style: style)
      ..layout();
    _contentSized = _painter.size;

    if (radians == 0) {
      _rect = Offset.zero & _contentSized;
      _bounds = _rect;
      return;
    }

    _rect = computeArcRect(
      focalPoint: center,
      radians: radians,
      arcLength: _contentSized.width,
    );

    _sweepAngle = radians;
    if (radians.isNegative) {
      _startAngle = pi / 2;
    } else {
      _startAngle = -pi / 2;
    }
    _startAngle -= sweepAngle / 2;

    _bounds = getArcBounds(_rect, startAngle, sweepAngle);

    center -= bounds.center;
    _rect = _rect.shift(center);
    _bounds = _bounds.shift(center);
  }

  // /// tính toán tổng góc quét dùng để vẽ được hết nội dung của [text]
  // double _getSweepAngle(String content, double effectiveRadius) {
  //   double total = 0;
  //   for (final char in content.characters) {
  //     _painter
  //       ..text = TextSpan(text: char)
  //       ..layout(minWidth: 0, maxWidth: double.maxFinite);

  //     total += 2 * asin(_painter.width / (2 * effectiveRadius));
  //   }
  //   return total;
  // }

  void dispose() {
    _painter.dispose();
  }

  ArcComputerResult getResult() => ArcComputerResult(
        sweepAngle: _sweepAngle,
        startAngle: _startAngle,
        arcRect: _rect,
        bounds: _bounds,
      );
}

class ArcComputerResult extends Equatable {
  final double sweepAngle;
  final double startAngle;
  final Rect arcRect;
  final Rect bounds;

  const ArcComputerResult({
    required this.sweepAngle,
    required this.startAngle,
    required this.arcRect,
    required this.bounds,
  });

  Path getPath() {
    final path = Path()..addArc(arcRect, startAngle, sweepAngle);
    return path;
  }

  @override
  List<Object> get props => [sweepAngle, startAngle, arcRect, bounds];
}
