// Flutter project by quanghuuxx (<EMAIL>)

import '../../../resource/icon_constants.dart';
import '../../../resource/localization/lkey.dart';

mixin BottomItemEnum {
  String get lkey;

  String? get icon;

  bool get canCapture;
}

enum MainBottomBarItem implements BottomItemEnum {
  // sketch,
  mask,
  sticker,
  text,
  transform;

  @override
  String get lkey {
    return switch (this) {
      // sketch => LKey.edit_sketch_bottom,
      transform => LKey.edit_transform_bottom,
      text => LKey.edit_text_bottom,
      sticker => LKey.edit_sticker_bottom,
      mask => LKey.edit_mask_bottom,
    };
  }

  @override
  String get icon {
    return switch (this) {
      // sketch => IconConstants.ic_sketch,
      transform => IconConstants.ic_transform,
      text => IconConstants.ic_text,
      sticker => IconConstants.ic_sticker,
      mask => IconConstants.ic_gallery,
    };
  }

  @override
  bool get canCapture {
    return switch (this) {
      // sketch => false,
      transform => false,
      text => true,
      sticker => true,
      mask => true,
    };
  }
}

mixin SubBottomBarItem implements BottomItemEnum {
  bool get supportBottomBar;

  @override
  bool get canCapture => false;
}

enum FilterBottomBarItem with SubBottomBarItem {
  brightness,
  contrast,
  saturation,
  exposure,
  vibrance,
  temperature,
  tint,
  shadow,
  highlight;

  @override
  String get lkey => switch (this) {
        brightness => LKey.filter_opt_brightness,
        contrast => LKey.filter_opt_contrast,
        saturation => LKey.filter_opt_saturation,
        exposure => LKey.filter_opt_exposure,
        vibrance => LKey.filter_opt_vibrance,
        temperature => LKey.filter_opt_temperature,
        tint => LKey.filter_opt_tint,
        shadow => LKey.filter_opt_shadow,
        highlight => LKey.filter_opt_highlight,
      };

  @override
  String get icon => switch (this) {
        brightness => IconConstants.ic_brightness,
        contrast => IconConstants.ic_contrast,
        saturation => IconConstants.ic_saturation,
        exposure => IconConstants.ic_exposure,
        vibrance => IconConstants.ic_vibrance,
        temperature => IconConstants.ic_temperature,
        tint => IconConstants.ic_tint,
        shadow => IconConstants.ic_shadow,
        highlight => IconConstants.ic_highlight,
      };

  @override
  bool get supportBottomBar => switch (this) {
        _ => false,
      };

  double get max {
    return switch (this) {
      brightness => 1.0,
      contrast => 2.0,
      saturation => 2.0,
      exposure => 2.0,
      vibrance => 1.0,
      temperature =>1.0,
      tint => 1.0,
      shadow => 2.0,
      highlight => 2.0,
    };
  }

  double get min {
    return switch (this) {
      brightness => -1.0,
      contrast => 0.0,
      saturation => 0.0,
      exposure => 0.0,
      vibrance => -1.0,
      temperature => -1.0,
      tint => -1.0,
      shadow => 0.0,
      highlight => 0.0,
    };
  }

  int get fractionDigits {
    return switch (this) {
      brightness => 2,
      contrast => 2,
      saturation => 2,
      exposure => 2,
      vibrance => 1,
      temperature => 1,
      tint => 2,
      shadow => 2,
      highlight => 2,
    };
  }
}

enum TransformBottomBarItem with SubBottomBarItem {
  crop,
  rotate,
  filter,
  blur,
  opacity,
  flip_horizontal,
  flip_vertical;

  @override
  String get lkey => switch (this) {
        crop => LKey.common_crop,
        rotate => LKey.common_rotate,
        filter => LKey.edit_filter_bottom,
        blur => LKey.common_blur,
        opacity => LKey.common_opacity,
        flip_horizontal => LKey.common_flip_horizontal,
        flip_vertical => LKey.common_flip_vertical
      };

  @override
  String get icon => switch (this) {
        crop => IconConstants.ic_crop,
        rotate => IconConstants.ic_rotate,
        filter => IconConstants.ic_filter,
        blur => IconConstants.ic_blur,
        opacity => IconConstants.ic_opacity,
        flip_horizontal => IconConstants.ic_flip_horizontal,
        flip_vertical => IconConstants.ic_flip_vertical
      };

  @override
  bool get supportBottomBar => switch (this) {
        crop => true,
        rotate => true,
        filter => true,
        blur => true,
        opacity => true,
        flip_horizontal => false,
        flip_vertical => false
      };
}

enum RatioBottomBarItem with SubBottomBarItem {
  origin,
  ratio_1_1,
  ratio_4_3,
  ratio_3_4,
  ratio_5_4,
  ratio_3_2,
  ratio_16_9;

  double? get ratio {
    return switch (this) {
      origin => null,
      ratio_1_1 => 1.0,
      ratio_4_3 => 4.0 / 3.0,
      ratio_3_4 => 3.0 / 4.0,
      ratio_5_4 => 5.0 / 4.0,
      ratio_3_2 => 3.0 / 2.0,
      ratio_16_9 => 16.0 / 9.0
    };
  }

  @override
  String get lkey {
    return switch (this) {
      origin => LKey.common_origin,
      ratio_1_1 => '1:1',
      ratio_4_3 => '4:3',
      ratio_3_4 => '3:4',
      ratio_5_4 => '5:4',
      ratio_3_2 => '3:2',
      ratio_16_9 => '16:9'
    };
  }

  @override
  String? get icon => null;

  @override
  bool get supportBottomBar => false;
}

enum TextBottomBarItem with SubBottomBarItem {
  add,
  font,
  color,
  gradient,
  // adjustment,
  background,
  opacity,
  bend,
  shadow,
  stroke;

  @override
  String? get icon => switch (this) {
        add => IconConstants.ic_pencil,
        font => IconConstants.ic_font,
        color => IconConstants.ic_color,
        gradient => IconConstants.ic_gradient,
        // adjustment => IconConstants.ic_adjustment,
        background => IconConstants.ic_sticker_circle,
        opacity => IconConstants.ic_opacity,
        bend => IconConstants.ic_bend,
        shadow => IconConstants.ic_shadow,
        stroke => IconConstants.ic_stroke
      };

  @override
  String get lkey => switch (this) {
        add => LKey.text_opt_add,
        font => LKey.text_opt_font,
        color => LKey.text_opt_color,
        gradient => LKey.text_opt_gradient,
        // adjustment => LKey.text_opt_adjustment,
        background => LKey.edit_transform_bottom,
        opacity => LKey.common_opacity,
        bend => LKey.common_bend,
        shadow => LKey.common_shadow,
        stroke => LKey.common_stroke
      };

  @override
  bool get supportBottomBar => switch (this) {
        add => false,
        background => false,
        _ => true,
      };
}

enum TextAdjustmentBottomBarItem with SubBottomBarItem {
  underline,
  bold,
  italic,
  strike;

  @override
  // TODO: implement icon
  String? get icon => throw UnimplementedError();

  @override
  // TODO: implement lkey
  String get lkey => throw UnimplementedError();

  @override
  // TODO: implement supportBottomBar
  bool get supportBottomBar => throw UnimplementedError();
}

enum MaskBottomBarItem with SubBottomBarItem {
  add,
  replace,
  shape,
  filter,
  stroke;

  @override
  String? get icon => switch (this) {
        add => IconConstants.ic_gallery_add,
        replace => IconConstants.replace_img,
        shape => IconConstants.ic_shapes,
        filter => IconConstants.ic_filter,
        stroke => IconConstants.ic_stroke
      };

  @override
  String get lkey => switch (this) {
        add => LKey.common_add_new,
        replace => LKey.common_replace,
        shape => LKey.common_shape,
        filter => LKey.edit_filter_bottom,
        stroke => LKey.common_stroke
      };

  @override
  bool get supportBottomBar => switch (this) {
        add => false,
        replace => false,
        _ => true,
      };
}

enum StickerBottomBarItem with SubBottomBarItem {
  collection,
  opacity,
  flip_horizontal,
  flip_vertical;

  @override
  String? get icon => switch (this) {
        collection => IconConstants.ic_sticker,
        opacity => IconConstants.ic_opacity,
        flip_horizontal => IconConstants.ic_flip_horizontal,
        flip_vertical => IconConstants.ic_flip_vertical
      };

  @override
  String get lkey => switch (this) {
        collection => LKey.common_collection,
        opacity => LKey.common_opacity,
        flip_horizontal => LKey.common_flip_horizontal,
        flip_vertical => LKey.common_flip_vertical
      };

  @override
  bool get supportBottomBar => switch (this) {
        collection => true,
        opacity => true,
        _ => false,
      };
}
