// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:math';

import 'package:equatable/equatable.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

class CropRectangle extends Equatable {
  static bool hitTest(final Rect rect, final Offset point) {
    final delta = rect.center - point;
    return delta.distance <= 48;
  }

  static final CropRectangle zero = CropRectangle(Rect.zero);

  final double cornerSize;
  final Rect rect;
  final Rect topLeft;
  final Rect topRight;
  final Rect bottomLeft;
  final Rect bottomRight;
  final Rect centerLeft;
  final Rect centerRight;
  final Rect centerTop;
  final Rect centerBottom;

  CropRectangle(this.rect, {this.cornerSize = 4})
      : topLeft = Rect.fromCenter(center: rect.topLeft, width: cornerSize, height: cornerSize),
        topRight = Rect.fromCenter(center: rect.topRight, width: cornerSize, height: cornerSize),
        bottomLeft = Rect.fromCenter(center: rect.bottomLeft, width: cornerSize, height: cornerSize),
        bottomRight = Rect.fromCenter(center: rect.bottomRight, width: cornerSize, height: cornerSize),
        centerLeft = Rect.fromCenter(center: rect.centerLeft, width: cornerSize, height: cornerSize * 4),
        centerRight = Rect.fromCenter(center: rect.centerRight, width: cornerSize, height: cornerSize * 4),
        centerTop = Rect.fromCenter(center: rect.topCenter, width: cornerSize * 4, height: cornerSize),
        centerBottom = Rect.fromCenter(center: rect.bottomCenter, width: cornerSize * 4, height: cornerSize);

  CropRectangle inflate(double delta) {
    return CropRectangle(rect.inflate(delta), cornerSize: cornerSize);
  }

  CropRectangle deflate(double delta) {
    return CropRectangle(rect.deflate(delta), cornerSize: cornerSize);
  }

  CropRectangle shift(Offset offset, {Rect boundary = Rect.largest}) {
    double translateX = offset.dx, translateY = offset.dy;
    if (rect.left + offset.dx < boundary.left || rect.right + offset.dx > boundary.right) {
      translateX = 0;
    }

    if (rect.top + offset.dy < boundary.top || rect.bottom + offset.dy > boundary.bottom) {
      translateY = 0;
    }

    return CropRectangle(rect.translate(translateX, translateY), cornerSize: cornerSize);
  }

  CropRectangle translale({
    double left = 0,
    double top = 0,
    double right = 0,
    double bottom = 0,
    Rect boundary = Rect.largest,
  }) {
    return CropRectangle(
      Rect.fromLTRB(
        max(boundary.left, min(rect.left + left, topRight.left - centerTop.width - topLeft.width)),
        max(boundary.top, min(rect.top + top, bottomLeft.top - centerLeft.height - topLeft.height)),
        min(boundary.right, max(rect.right + right, topLeft.right + centerTop.width + topRight.width)),
        min(boundary.bottom, max(rect.bottom + bottom, topLeft.bottom + centerLeft.height + bottomLeft.height)),
      ),
      cornerSize: cornerSize,
    );
  }

  void paintCorners(Canvas canvas, Paint paint) {
    canvas
      ..drawRect(topLeft, paint)
      ..drawRect(topRight, paint)
      ..drawRect(bottomLeft, paint)
      ..drawRect(bottomRight, paint)
      ..drawRect(centerLeft, paint)
      ..drawRect(centerRight, paint)
      ..drawRect(centerTop, paint)
      ..drawRect(centerBottom, paint);
  }

  @override
  List<Object?> get props => [
        cornerSize,
        rect,
      ];
}

class CropRectangleDragHandler {
  late final PanGestureRecognizer onTopLeft;
  late final PanGestureRecognizer onTopRight;
  late final PanGestureRecognizer onBottomLeft;
  late final PanGestureRecognizer onBottomRight;

  late final PanGestureRecognizer centerLeft;
  late final PanGestureRecognizer centerTop;
  late final PanGestureRecognizer center;
  late final PanGestureRecognizer centerRight;
  late final PanGestureRecognizer centerBottom;

  PanGestureRecognizer? _gesture;
  void set(PanGestureRecognizer? other) {
    _gesture = other;
  }

  void onDragStart(DragStartDetails details) {
    _gesture?.onStart?.call(details);
  }

  void onDragUpdate(DragUpdateDetails details) {
    _gesture?.onUpdate?.call(details);
  }

  void onDragEnd(DragEndDetails details) {
    _gesture?.onEnd?.call(details);
  }

  void onDragCancel() {
    _gesture?.onCancel?.call();
  }
}
