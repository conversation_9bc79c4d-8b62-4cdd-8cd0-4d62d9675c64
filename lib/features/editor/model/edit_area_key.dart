// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

class EditAreaKey<T extends State> extends GlobalKey<T> {
  EditAreaKey() : super.constructor();

  @override
  BuildContext get currentContext {
    if (super.currentContext == null) {
      throw Exception('No EditAreaState found in context');
    }
    return super.currentContext!;
  }

  Offset localToGlobal(BuildContext context, Offset offset) {
    final renderBox = context.findRenderObject() as RenderBox;
    return renderBox.localToGlobal(offset, ancestor: currentContext.findRenderObject());
  }

  Size get size => currentContext.size!;

  Future<ui.Image> capture({double pixelRatio = 1.0}) {
    final renderBox = currentContext.findRenderObject() as RenderRepaintBoundary;
    return renderBox.toImage(pixelRatio: pixelRatio);
  }
}
