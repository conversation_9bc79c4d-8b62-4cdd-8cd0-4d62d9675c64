// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:ui';

import 'package:core/core.dart';
import 'package:equatable/equatable.dart';

import 'floating_bar_enum.dart';

class CurrentMainFloatingBarEvent extends Equatable {
  final MainFloatingBarEnum? value;

  CurrentMainFloatingBarEvent({this.value});

  @override
  List<Object?> get props => [value];
}

class CurrentSubFloatingBarEvent extends Equatable {
  final FloatingBarEnum? value;

  CurrentSubFloatingBarEvent({this.value});

  @override
  List<Object?> get props => [
        if (value?.selectable == false) forcedBuild,
        value,
      ];
}

class ReplaceBackActionEvent extends Equatable {
  final VoidCallback? onBack;

  ReplaceBackActionEvent({required this.onBack});

  @override
  List<Object?> get props => [onBack];
}
