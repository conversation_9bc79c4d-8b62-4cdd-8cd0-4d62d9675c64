// Flutter project by quanghuuxx (<EMAIL>)

import '../../../resource/icon_constants.dart';
import '../../../resource/localization/lkey.dart';

mixin FloatingBarEnum {
  String get lkey;

  String get icon;

  bool get selectable;

  bool get maintain;
}

enum MainFloatingBarEnum implements FloatingBarEnum {
  text,
  sticker,
  mask,
  transform,
  layer;

  List<FloatingBarEnum> get features => switch (this) {
        text => TextFloatingBarEnum.values,
        sticker => StickerFloatingBarEnum.values,
        mask => MaskFloatingBarEnum.values,
        transform => TransformFloatingBarEnum.values,
        layer => throw UnimplementedError(),
      };

  @override
  String get lkey {
    return switch (this) {
      // sketch => LKey.edit_sketch_bottom,
      transform => LKey.edit_transform_bottom,
      text => LKey.edit_text_bottom,
      sticker => LKey.edit_sticker_bottom,
      mask => LKey.edit_mask_bottom,
      layer => 'Layers',
    };
  }

  @override
  String get icon {
    return switch (this) {
      // sketch => IconConstants.ic_sketch,
      transform => IconConstants.ic_transform,
      text => IconConstants.ic_text,
      sticker => IconConstants.ic_sticker,
      mask => IconConstants.ic_gallery,
      layer => IconConstants.ic_layers,
    };
  }

  @override
  bool get selectable => true;

  @override
  bool get maintain => this == layer;
}

enum TransformFloatingBarEnum implements FloatingBarEnum {
  crop,
  rotate,
  filter,
  blur,
  opacity,
  flip_horizontal,
  flip_vertical;

  @override
  String get icon => switch (this) {
        crop => IconConstants.ic_crop,
        rotate => IconConstants.ic_rotate,
        filter => IconConstants.ic_filter,
        blur => IconConstants.ic_blur,
        opacity => IconConstants.ic_opacity,
        flip_horizontal => IconConstants.ic_flip_horizontal,
        flip_vertical => IconConstants.ic_flip_vertical
      };

  @override
  String get lkey => switch (this) {
        crop => LKey.common_crop,
        rotate => LKey.common_rotate,
        filter => LKey.edit_filter_bottom,
        blur => LKey.common_blur,
        opacity => LKey.common_opacity,
        flip_horizontal => LKey.common_flip_horizontal,
        flip_vertical => LKey.common_flip_vertical
      };

  @override
  bool get selectable => switch (this) {
        flip_horizontal => false,
        flip_vertical => false,
        _ => true,
      };

  @override
  bool get maintain => switch (this) {
        crop => true,
        rotate => true,
        _ => false,
      };
}

enum TextFloatingBarEnum implements FloatingBarEnum {
  add,
  background,
  bend,
  opacity,
  gradient,
  shadow,
  stroke;

  @override
  String get icon => switch (this) {
        add => IconConstants.ic_pencil,
        background => IconConstants.ic_sticker_circle,
        opacity => IconConstants.ic_opacity,
        bend => IconConstants.ic_bend,
        shadow => IconConstants.ic_shadow,
        stroke => IconConstants.ic_stroke,
        gradient => IconConstants.ic_gradient
      };

  @override
  String get lkey => switch (this) {
        add => LKey.text_opt_add,
        background => LKey.edit_transform_bottom,
        opacity => LKey.common_opacity,
        bend => LKey.common_bend,
        shadow => LKey.common_shadow,
        stroke => LKey.common_stroke,
        gradient => LKey.text_opt_gradient
      };

  @override
  bool get selectable => true;

  @override
  bool get maintain => false;
}

enum StickerFloatingBarEnum implements FloatingBarEnum {
  collection,
  opacity,
  flip_horizontal,
  flip_vertical;

  @override
  String get icon => switch (this) {
        collection => IconConstants.ic_sticker,
        opacity => IconConstants.ic_opacity,
        flip_horizontal => IconConstants.ic_flip_horizontal,
        flip_vertical => IconConstants.ic_flip_vertical
      };

  @override
  String get lkey => switch (this) {
        collection => LKey.common_collection,
        opacity => LKey.common_opacity,
        flip_horizontal => LKey.common_flip_horizontal,
        flip_vertical => LKey.common_flip_vertical
      };

  @override
  bool get selectable => switch (this) {
        flip_horizontal => false,
        flip_vertical => false,
        _ => true,
      };

  @override
  bool get maintain => false;
}

enum MaskFloatingBarEnum implements FloatingBarEnum {
  add,
  replace,
  shape,
  filter,
  stroke;

  @override
  String get icon => switch (this) {
        add => IconConstants.ic_gallery_add,
        replace => IconConstants.replace_img,
        shape => IconConstants.ic_shapes,
        filter => IconConstants.ic_filter,
        stroke => IconConstants.ic_stroke
      };

  @override
  String get lkey => switch (this) {
        add => LKey.common_add_new,
        replace => LKey.common_replace,
        shape => LKey.common_shape,
        filter => LKey.edit_filter_bottom,
        stroke => LKey.common_stroke
      };

  @override
  bool get selectable => true;

  @override
  bool get maintain => false;
}
