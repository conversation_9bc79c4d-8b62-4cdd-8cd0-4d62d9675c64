// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:async';
import 'dart:typed_data';
import 'dart:ui' show Image, ImageByteFormat, Size;

import 'package:core/core.dart';
import 'package:equatable/equatable.dart';

import '../../../configurations/app_error.dart';

final class ImageData extends Equatable {
  final Uint8List uint8list;
  final Image image;

  ImageData({required this.uint8list, required this.image});

  Size get size => image.size;

  @override
  List<Object?> get props => [
        uint8list,
        image,
      ];
}

final class PreloadImageObject {
  final Map<int, ImageData> _cache = {};

  PreloadImageObject._();

  static final PreloadImageObject instance = PreloadImageObject._();

  bool exist(final String source) => _cache.containsKey(source.hashCode);

  FutureOr<ImageData> load(final String source) async {
    if (_cache.containsKey(source.hashCode)) {
      return _cache[source.hashCode]!;
    }

    final image = await HelperUtil.getUIImage(HelperUtil.getImageProvider(source));
    final bytes = await image.toByteData(format: ImageByteFormat.png);
    if (bytes == null) {
      throw BaseException(
        code: AppError.somethingWrong.code,
        description: 'Can`t get bytes from image by path: $source',
      );
    }

    return _cache[source.hashCode] = ImageData(uint8list: bytes.buffer.asUint8List(), image: image);
  }

  FutureOr<ImageData> loadByBytes({
    required String source,
    required Uint8List uint8list,
    bool replace = false,
  }) async {
    if (!replace && _cache.containsKey(source.hashCode)) {
      return _cache[source.hashCode]!;
    }

    final image = await HelperUtil.decodeUIImage(uint8list);
    return _cache[source.hashCode] = ImageData(uint8list: uint8list, image: image);
  }

  FutureOr<ImageData> loadByUIImage(final String source, final Image image) async {
    final bytes = await image.toByteData(format: ImageByteFormat.png);
    if (bytes == null) {
      throw BaseException(
        code: AppError.somethingWrong.code,
        description: 'Can`t get bytes from image',
      );
    }

    return _cache[source.hashCode] = ImageData(uint8list: bytes.buffer.asUint8List(), image: image);
  }

  ImageData get(final String source) {
    final image = _cache[source.hashCode];
    if (image == null) {
      throw BaseException(
        code: AppError.notFound.code,
        description: 'Can`t find image by path: $source',
      );
    }
    return image;
  }

  ImageData? remove(final String source) {
    return _cache.remove(source.hashCode);
  }

  ImageData replace(final String source, final ImageData data) {
    return _cache[source.hashCode] = data;
  }
}
