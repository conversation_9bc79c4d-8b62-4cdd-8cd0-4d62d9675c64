// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:async';
import 'dart:ui';

import 'package:core/core.dart';

class ImageFilterFragment {
  FragmentProgram? _fragmentProgram;
  FragmentShader? _fragmentShader;

  bool get hasFragment => _fragmentProgram != null && _fragmentShader != null;

  FutureOr<void> initFragment() async {
    if (hasFragment) return;

    _fragmentProgram ??= await FragmentProgram.fromAsset('shaders/image_filter_fragment.frag');
    _fragmentShader ??= _fragmentProgram!.fragmentShader();
  }

  Shader createShader(
    Image image,
    UniForms uniForms,
  ) {
    assert(hasFragment, 'must be call initFragment to create fragment before');

    <double>[
      ...uniForms.uniForms,
      image.width.toDouble(),
      image.height.toDouble(),
    ].forEachIndexed((index, value) {
      _fragmentShader!.setFloat(index, value);
    });

    _fragmentShader!.setImageSampler(0, image);

    return _fragmentShader!;
  }

  void dispose() {
    _fragmentShader?.dispose();
    _fragmentShader = null;
    _fragmentProgram = null;
  }
}

mixin UniForms {
  List<double> get uniForms;

  double doubleFixed(
    double value, {
    int fractionDigits = 1,
  }) {
    return double.parse(value.toStringAsFixed(fractionDigits));
  }
}
