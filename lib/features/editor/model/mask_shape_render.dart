// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/material.dart';

class MaskShapeRender {
  final String key;
  final double width;
  final double height;
  final List<MaskPathRender> paths;

  MaskShapeRender({
    required this.key,
    required this.width,
    required this.height,
    required this.paths,
  });
}

final class MaskPathRender {
  final PaintingStyle style;
  final Path path;

  MaskPathRender({
    required this.style,
    required this.path,
  });
}
