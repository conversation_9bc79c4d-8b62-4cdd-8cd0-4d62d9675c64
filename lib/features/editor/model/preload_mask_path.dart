// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:async';
import 'dart:ui';

import 'package:core/core.dart';
import 'package:equatable/equatable.dart';
import 'package:svg_path_parser/svg_path_parser.dart';

import '../../../configurations/app_error.dart';
import '../../../data/model/common/mask_shape.dart';

class MaskPathRender extends Equatable {
  final Path path;
  final PaintingStyle style;

  MaskPathRender({required this.path, required this.style});

  @override
  List<Object> get props => [path, style];
}

class PreloadPathsMaskShape {
  PreloadPathsMaskShape._();

  static final PreloadPathsMaskShape instance = PreloadPathsMaskShape._();

  final Map<String, List<MaskPathRender>> _cache = {};
  final Map<String, Path> _cacheCombine = {};

  List<MaskPathRender> get(final String source) {
    final paths = _cache[source];
    if (paths == null) {
      throw BaseException(
        code: AppError.notFound.code,
        description: 'Can`t find mask path by path: $source',
      );
    }
    return paths;
  }

  Path getCombine(final String source) {
    final combine = _cacheCombine[source];
    if (combine == null) {
      throw BaseException(
        code: AppError.notFound.code,
        description: 'Can`t find mask path by path: $source',
      );
    }
    return combine;
  }

  bool exist(final String source) => _cache.containsKey(source);

  FutureOr<List<MaskPathRender>> load(final MaskShape shape) async {
    if (_cache.containsKey(shape.asset)) {
      return _cache[shape.asset]!;
    }

    final paths = await Future.wait(
      shape.paths.map(
        (e) async => MaskPathRender(
          path: parseSvgPath(e.path),
          style: e.style,
        ),
      ),
    );

    _cacheCombine[shape.asset] = Path();
    for (final m in paths) {
      _cacheCombine[shape.asset] = Path.combine(PathOperation.union, _cacheCombine[shape.asset]!, m.path);
    }

    return _cache[shape.asset] = paths;
  }

  void cachePath(Path path, String key) {
    _cacheCombine[key] = path;
  }
}
