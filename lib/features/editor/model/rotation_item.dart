// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:math';

// https://t0.gstatic.com/licensed-image?q=tbn:ANd9GcQkwOrvGEI2HbfG-L4B48TNbfTrm7S7pmhOmvESVEftNpelok9Z3FF0mLPh1c7LgGB-
class RotationItem {
  static List<RotationItem> values = [
    RotationItem(title: '0°', radians: 0),
    RotationItem(title: '45°', radians: pi / 4),
    RotationItem(title: '90°', radians: pi / 2),
    RotationItem(title: '135°', radians: 3 * pi / 4),
    RotationItem(title: '180°', radians: pi),
    RotationItem(title: '225°', radians: 5 * pi / 4),
    RotationItem(title: '270°', radians: 3 * pi / 2),
    RotationItem(title: '315°', radians: 7 * pi / 4),
  ];

  final String title;
  final double radians;

  RotationItem({
    required this.title,
    required this.radians,
  });
}
