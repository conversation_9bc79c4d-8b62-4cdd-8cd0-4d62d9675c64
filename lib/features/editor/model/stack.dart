// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/material.dart';

class StackField<T> {
  StackField([T? value]) {
    if (value != null) _iterable.add(value);
  }

  final List<T> _iterable = <T>[];

  void push(T value, {bool forceNotify = false}) {
    _iterable.add(value);
  }

  T? pop() {
    if (_iterable.isEmpty) return null;
    return _iterable.removeLast();
  }

  // T operator [](int index) => _iterable[index];

  T? peek() => _iterable.lastOrNull;

  bool get isEmpty => _iterable.isEmpty;

  int get length => _iterable.length;

  void replace(T re) => _iterable.last = re;

  void clear() => _iterable.clear();

  Iterable<T> get iterable => _iterable.reversed;
}

class StackFieldNotifier<T> extends StackField<T> with ChangeNotifier {
  StackFieldNotifier([super.value]);

  @override
  void push(T value, {bool forceNotify = false}) {
    final current = peek();
    super.push(value, forceNotify: forceNotify);
    if (current != value) {
      notifyListeners();
    }
  }

  @override
  T? pop() {
    final poped = super.pop();
    notifyListeners();
    return poped;
  }

  @override
  void replace(T re) {
    super.replace(re);
    notifyListeners();
  }

  @override
  void clear() {
    super.clear();
    notifyListeners();
  }
}
