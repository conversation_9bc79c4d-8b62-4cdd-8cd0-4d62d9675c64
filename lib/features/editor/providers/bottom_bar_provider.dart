// Flutter project by quanghuuxx (<EMAIL>)

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../mixin/bottom_action_mixin.dart';
import '../model/bottom_bar_enum.dart';
import 'bottom_bar_state.dart';

part 'bottom_bar_provider.g.dart';

@riverpod
class BottomBarEditor extends _$BottomBarEditor {
  @override
  BottomBarSelection build() {
    return BottomBarSelection();
  }

  BottomItemEnum? get currentBottom => state.bottom?.value;
  SubBottomBarItem? get currentSubBottom => state.subBottomEnum;

  void popBottom() {
    state = state.popBottom();
  }

  void selectSubBottom(SubBottomBarItem item) {
    assert(state.bottom != null);
    state = state.selectSubBottom(item);
  }

  void selectBottom(
    BottomItemEnum item, {
    SubBottomBarItem? subBottom,
  }) {
    state = state.pushBottom(item);
  }

  void pushAction(IBottomAction action) {
    state = state.pushAction(action);
  }

  void popAction() {
    state = state.popAction();
  }
}
