// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bottom_bar_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$bottomBarEditorHash() => r'1ad7ac45dc81dfdcffea384fe0a35d06934af0c0';

/// See also [BottomBarEditor].
@ProviderFor(BottomBarEditor)
final bottomBarEditorProvider =
    AutoDisposeNotifierProvider<BottomBarEditor, BottomBarSelection>.internal(
  BottomBarEditor.new,
  name: r'bottomBarEditorProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$bottomBarEditorHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BottomBarEditor = AutoDisposeNotifier<BottomBarSelection>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
