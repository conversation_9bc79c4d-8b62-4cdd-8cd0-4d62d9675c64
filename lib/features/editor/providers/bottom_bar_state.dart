// Flutter project by quanghuuxx (<EMAIL>)

import 'package:equatable/equatable.dart';

import '../mixin/bottom_action_mixin.dart';
import '../model/bottom_bar_enum.dart';

class BottomBarSelection extends Equatable {
  final History<BottomItemEnum>? bottom;
  final SubBottomBarItem? subBottomEnum;
  final History<IBottomAction>? action;

  BottomBarSelection({
    this.bottom,
    this.subBottomEnum,
    this.action,
  });

  BottomBarSelection pushBottom(BottomItemEnum item) {
    return BottomBarSelection(
      bottom: bottom?.push(item) ?? History(value: item),
      action: action,
    );
  }

  BottomBarSelection selectSubBottom(SubBottomBarItem item) {
    return BottomBarSelection(
      bottom: bottom,
      subBottomEnum: item,
      action: action,
    );
  }

  BottomBarSelection popBottom() {
    return BottomBarSelection(
      bottom: bottom?.pop(),
      action: action,
    );
  }

  BottomBarSelection pushAction(IBottomAction action) {
    return BottomBarSelection(
      bottom: bottom,
      action: this.action?.push(action) ?? History(value: action),
    );
  }

  BottomBarSelection popAction() {
    return BottomBarSelection(
      bottom: bottom,
      action: action?.pop(),
    );
  }

  @override
  List<Object?> get props => [
        bottom,
        action,
      ];
}

class History<T> {
  final T value;
  final History<T>? previous;

  History({
    required this.value,
    this.previous,
  });

  bool get canPop => previous != null;

  History<T>? pop() {
    return previous;
  }

  History<T> push(T value) {
    return History(
      value: value,
      previous: this,
    );
  }

  History<T> replace(T value) {
    return History(
      value: value,
      previous: previous,
    );
  }
}
