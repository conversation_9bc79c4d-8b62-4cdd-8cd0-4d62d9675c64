// Flutter project by quanghuuxx (<EMAIL>)

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../component/dependency_injector/di.dart';
import '../repository/edit_tooltip_repository.dart';

part 'edit_tooltip_provider.g.dart';

@riverpod
class EditTooltipState extends _$EditTooltipState {
  late final EditTooltipRepository _repository;

  @override
  Set<EditTooltipFeature> build() {
    _repository = it.get<EditTooltipRepository>();
    return _fetch();
  }

  Set<EditTooltipFeature> _fetch() {
    final showns = _repository.fetchListEditTooltip();

    return EditTooltipFeature.values.fold(<EditTooltipFeature>{}, (set, e) {
      if (!showns.contains(e)) {
        set.add(e);
      }
      return set;
    }).toSet();
  }

  void saveShownTooltip(EditTooltipFeature feature) {
    _repository.saveShownTooltip(feature);
    state = _fetch();
  }
}
