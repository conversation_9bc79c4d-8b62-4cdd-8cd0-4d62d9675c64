// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'edit_tooltip_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$editTooltipStateHash() => r'29e477bca20eed0d6ccf4c0c4e52cf668f91cbcd';

/// See also [EditTooltipState].
@ProviderFor(EditTooltipState)
final editTooltipStateProvider = AutoDisposeNotifierProvider<EditTooltipState,
    Set<EditTooltipFeature>>.internal(
  EditTooltipState.new,
  name: r'editTooltipStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$editTooltipStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$EditTooltipState = AutoDisposeNotifier<Set<EditTooltipFeature>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
