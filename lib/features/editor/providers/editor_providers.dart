// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_image_gallery_saver/flutter_image_gallery_saver.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../component/dependency_injector/di.dart';
import '../../../configurations/app_error.dart';
import '../../../data/data.dart';
import '../../../data/model/common/mask_shape.dart';
import '../../shared/customer_purchased/customer_purchased_provider.dart';
import '../interactor/editor_interactor.dart';
import '../mixin/text_layout_mixin.dart';
import '../model/image_data.dart';
import '../model/preload_mask_path.dart';
import 'bottom_bar_state.dart';
import 'pic_element_provider.dart';

part 'editor_providers.g.dart';

@riverpod
class PicTemplateEditor extends _$PicTemplateEditor with ElementTextLayoutMixin {
  late final EditorInteractor _interactor;

  @override
  History<PicEditingTemplate> build() {
    _interactor = it.get<EditorInteractor>();

    final template = NavigatorObsService.instance.currentRoute.arguments as PicEditingTemplate;
    return History<PicEditingTemplate>(value: template);
  }

  PicEditingTemplate get template => state.value;

  void push() {
    state = state.push(
      template.copyWith(
        elements: List.from(template.elements),
      ),
    );
  }

  void pop() {
    assert(state.canPop, 'Can`t pop');
    state = state.pop()!;
  }

  Future<void> submit(
    final RenderRepaintBoundary render, {
    double pixelRatio = 1.0,
    SaveStatus saveStatus = SaveStatus.draft,
    bool force = false,
  }) async {
    final saved = await _interactor.saveTemplate(
      template.copyWith(saveStatus: saveStatus),
      render: render,
      pixelRatio: pixelRatio,
    );

    if (saveStatus == SaveStatus.export && saved.snapshot?.isNotEmpty == true) {
      if (!force && !ref.read(customerPurchasedProvider).isPremium) {
        final slot = _interactor.getExportSlot();
        if (slot <= 0) {
          throw BaseException(
            code: AppError.forbidden.code,
            description: 'No more export slot',
          );
        }
        await _interactor.saveExportSlot(slot - 1);
      }
      try {
        await FlutterImageGallerySaver.saveFile(saved.buildSnapshot());
      } catch (e) {
        throw BaseException(
          code: AppError.somethingWrong.code,
          description: e.toString(),
        );
      }
    }

    state = state.replace(saved as PicEditingTemplate);
  }

  Future<void> cropBackground(final Rect rect) async {
    final record = await _interactor.cropBackground(
      rect,
      template.originImg,
    );

    await PreloadImageObject.instance.loadByBytes(
      source: template.identifier,
      uint8list: record.bytes,
      replace: true,
    );

    state = state.replace(
      template.copyWith(imageBounds: PicRect.fromRect(record.rect)),
    );
  }

  void updateAttributes({
    double? opacity,
    double? ratio,
    double? rotation,
    PicFlipEnum? flipEnum,
    String? snapshot,
    PicRect? imageBounds,
    PicBlur? blur,
    PicImgFilter? filter,
    bool ratioNullable = false,
    bool flipEnumNullable = false,
    bool blurNullable = false,
  }) {
    state = state.replace(
      template.copyWith(
        opacity: opacity,
        ratio: ratio,
        rotation: rotation,
        flipEnum: flipEnum,
        snapshot: snapshot,
        imageBounds: imageBounds,
        blur: blur,
        filter: filter,
        ratioNullable: ratioNullable,
        flipEnumNullable: flipEnumNullable,
        blurNullable: blurNullable,
      ),
    );
  }

  void addElementText(
    String content, {
    TextMetadata metadata = PicElementText.kDefaultMetadata,
    required Size boundary,
  }) {
    final sized = layoutSized(
      content,
      style: getTextStyle(metadata),
      maxWidth: boundary.width * 0.85,
    );

    final rect = PicRect.fromCenter(boundary.center(Offset.zero), sized);
    final element = PicElementText(
      id: template.elements.fold(0, (value, e) => e is PicElementText ? e.id + 1 : value),
      type: PicElementType.text,
      rect: rect,
      rotation: 0,
      content: content,
      metadata: metadata,
    );

    ref.read(picElementSelectionProvider.notifier).key = element.key;
    state = state.replace(
      template.copyWith(
        elements: [...template.elements, element],
      ),
    );
  }

  void addElementMask(
    String source, {
    MaskShape? shape,
    required Size boundary,
  }) async {
    if (!PreloadImageObject.instance.exist(source)) {
      await PreloadImageObject.instance.load(source);
    }

    if (shape != null) {
      await PreloadPathsMaskShape.instance.load(shape);
    } else {
      final data = PreloadImageObject.instance.get(source);
      shape = MaskShape.fromRect(asset: source, rect: Offset.zero & Size(100, data.size.aspectRatio * 100));
      await PreloadPathsMaskShape.instance.load(shape);
    }

    final dimension = boundary.width * 0.3;
    final rect = PicRect.fromCenter(
      boundary.center(Offset.zero),
      Size(
        dimension,
        dimension * shape.aspectRatio,
      ),
    );
    final element = PicElementMask(
      id: template.elements.fold(0, (value, e) => e is PicElementMask ? e.id + 1 : value),
      type: PicElementType.mask,
      rect: rect,
      source: source,
      shape: shape,
    );
    ref.read(picElementSelectionProvider.notifier).key = element.key;
    state = state.replace(
      template.copyWith(
        elements: [...template.elements, element],
      ),
    );
  }

  void addElementSticker(
    String source, {
    required Size boundary,
  }) async {
    final data = await PreloadImageObject.instance.load(source);

    final dimension = boundary.width * 0.3;
    final rect = PicRect.fromCenter(
      boundary.center(Offset.zero),
      Size(
        dimension,
        dimension / data.size.aspectRatio,
      ),
    );
    final element = PicElementSticker(
      id: template.elements.fold(0, (value, e) => e is PicElementSticker ? e.id + 1 : value),
      type: PicElementType.sticker,
      rect: rect,
      source: source,
    );
    ref.read(picElementSelectionProvider.notifier).key = element.key;
    state = state.replace(
      template.copyWith(
        elements: [...template.elements, element],
      ),
    );
  }

  void duplicateElement(String key) {
    PicElement? element = template.elements.firstWhereOrNull((e) => e.key == key);
    if (element == null) return;

    final newElement = element.copyWith(
      id: template.elements.fold(0, (value, e) {
        switch (e.type) {
          case PicElementType.mask:
            return e is PicElementMask ? e.id + 1 : value;
          case PicElementType.text:
            return e is PicElementText ? e.id + 1 : value;
          case PicElementType.sticker:
            return e is PicElementSticker ? e.id + 1 : value;
        }
      }),
      rect: element.rect.shift(const Offset(15, 15)),
    );
    state = state.replace(
      template.copyWith(elements: [...template.elements, newElement]),
    );
    ref.read(picElementSelectionProvider.notifier).key = newElement.key;
  }

  void deleteElement(String key) {
    final listFrom = List<PicElement>.from(template.elements)..removeWhere((e) => e.key == key);
    if (listEquals(template.elements, listFrom)) return;
    ref.read(picElementSelectionProvider.notifier).key = null;
    state = state.replace(
      template.copyWith(elements: listFrom),
    );
  }

  void reorderElement(int oldIndex, int newIndex) {
    final listFrom = List<PicElement>.of(template.elements.reversed);
    final element = listFrom.removeAt(oldIndex);
    listFrom.insert(newIndex, element);
    state = state.replace(
      template.copyWith(elements: List.of(listFrom.reversed)),
    );
  }
}
