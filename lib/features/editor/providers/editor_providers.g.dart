// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'editor_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$picTemplateEditorHash() => r'064d3cab72a2566bb8b9440019ae1cd254ba7e6c';

/// See also [PicTemplateEditor].
@ProviderFor(PicTemplateEditor)
final picTemplateEditorProvider = AutoDisposeNotifierProvider<PicTemplateEditor,
    History<PicEditingTemplate>>.internal(
  PicTemplateEditor.new,
  name: r'picTemplateEditorProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$picTemplateEditorHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PicTemplateEditor = AutoDisposeNotifier<History<PicEditingTemplate>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
