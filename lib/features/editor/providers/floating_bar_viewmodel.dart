// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/foundation.dart';
import 'package:it/it.dart';

import '../model/events.dart';
import '../model/floating_bar_enum.dart';

class FloatingBarViewModel extends ViewModel {
  MainFloatingBarEnum? get main => get<CurrentMainFloatingBarEvent>()?.value;
  set main(MainFloatingBarEnum? value) {
    fire(CurrentSubFloatingBarEvent(value: null));
    fire(CurrentMainFloatingBarEvent(value: value));
  }

  FloatingBarEnum? get sub => get<CurrentSubFloatingBarEvent>()?.value;
  set sub(FloatingBarEnum? value) {
    fire(CurrentSubFloatingBarEvent(value: value));
  }

  bool pop() {
    bool result = false;
    if (sub == null) {
      result = main != null;
      main = null;
    } else {
      result = sub != null;
      sub = null;
    }

    return result;
  }

  void replaceBackAction(VoidCallback? onBack) {
    fire(ReplaceBackActionEvent(onBack: onBack));
  }
}
