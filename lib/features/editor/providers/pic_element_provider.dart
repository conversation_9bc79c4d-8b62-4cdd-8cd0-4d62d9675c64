// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:ui';

import 'package:core/core.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../data/data.dart';
import '../../../data/model/common/mask_shape.dart';
import '../mixin/text_layout_mixin.dart';
import '../model/image_data.dart';
import '../model/preload_mask_path.dart';
import 'editor_providers.dart';

part 'pic_element_provider.g.dart';

@riverpod
class PicElementSelection extends _$PicElementSelection {
  @override
  String? build() {
    return null;
  }

  set key(String? other) {
    state = other;
  }

  String? get key => state;
}

@riverpod
class PicElementEditor extends _$PicElementEditor with ElementTextLayoutMixin {
  @override
  PicElement build(final String key) {
    final template = ref.watch(picTemplateEditorProvider).value;
    final element = template.elements.firstWhereOrNull((e) => e.key == key) ?? stateOrNull;
    if (element == null) {
      throw StateError('PicElement with key:: $key not found');
    }
    return element;
  }

  void updateElementTextAttributes({
    PicRect? rect,
    double? rotation,
    double? opacity,
    TextMetadata? metadata,
  }) {
    assert(state is PicElementText, 'Element is not PicElementText');
    PicElementText element = state as PicElementText;
    state = element.copyWith(
      rect: rect,
      rotation: rotation,
      opacity: opacity,
      metadata: metadata,
    );
  }

  void changeElementTextBackground({
    String? source,
    double? scale,
    double? rotate,
    PicPosition? translate,
  }) async {
    assert(state is PicElementText, 'Element is not PicElementText');
    PicElementText element = state as PicElementText;
    if (source != null) {
      await PreloadImageObject.instance.load(source);
    }

    TextBackground? background;
    if (element.metadata.shader is TextBackground) {
      background = (element.metadata.shader as TextBackground).copyWith(
        source: source,
        scale: scale,
        rotate: rotate,
        translate: translate,
      );
    } else {
      assert(source != null, 'Can`t create text background with source is null');
      background = TextBackground(
        source: source!,
        scale: scale ?? 1,
        rotate: rotate ?? 0,
        translate: translate ?? PicPosition.zero,
      );
    }

    state = element.copyWith(
      metadata: element.metadata.copyWith(
        shader: background,
      ),
    );
  }

  void updateElementMaskAttributes({
    PicRect? rect,
    double? rotation,
    PicImgFilter? filter,
    PicFlipEnum? flipEnum,
    MaskStroke? stroke,
    MaskMetadata? metadata,
    bool metadataNullable = false,
  }) {
    assert(state is PicElementMask, 'Element is not PicElementMask');
    PicElementMask element = state as PicElementMask;
    state = element.copyWith(
      rect: rect,
      rotation: rotation,
      filter: filter,
      flipEnum: flipEnum,
      metadata: metadata,
      metadataNullable: metadataNullable,
      shape: element.shape.copyWith(
        stroke: stroke,
      ),
    );
  }

  void updateElementStickerAttributes({
    PicRect? rect,
    double? rotation,
    double? opacity,
    PicFlipEnum? flipEnum,
    bool flipEnumNullable = false,
  }) {
    assert(state is PicElementSticker, 'Element is not PicElementSticker');
    PicElementSticker element = state as PicElementSticker;
    state = element.copyWith(
      rect: rect,
      rotation: rotation,
      opacity: opacity,
      flipEnum: flipEnum,
      flipEnumNullable: flipEnumNullable,
    );
  }

  void resizeElementText({
    String? content,
    double? fontSize,
    double? bend,
    TextFontEnum? fontEnum,
    Color? color,
    TextAlign? textAlign,
  }) {
    assert(state is PicElementText, 'Element is not PicElementText');
    PicElementText element = state as PicElementText;
    element = element.copyWith(
      content: content,
      metadata: element.metadata.copyWith(
        fontEnum: fontEnum,
        fontSize: fontSize,
        bend: bend,
        color: color?.toHex(),
        textAlign: textAlign,
      ),
    );

    if (element == state) return;

    PicRect rect = element.rect;
    if (element.metadata.hasBend) {
      final bounds = layoutWithBend(
        element.content,
        style: getTextStyle(element.metadata),
        bend: element.metadata.bend!,
        center: rect.center,
      );
      rect = PicRect.fromRect(bounds);
    } else {
      final sized = layoutSized(
        element.content,
        style: getTextStyle(element.metadata),
      );
      rect = PicRect.fromCenter(rect.center, sized);
    }

    state = element.copyWith(rect: rect);
  }

  void changeElementMaskShape(MaskShape shape) async {
    assert(state is PicElementMask, 'Element is not PicElementMask');
    final element = state as PicElementMask;
    if (element.shape == shape) return;

    await PreloadPathsMaskShape.instance.load(shape);

    state = element.copyWith(
      shape: shape.copyWith(stroke: element.shape.stroke),
      rect: PicRect.fromCenter(
        element.rect.center,
        Size(
          element.rect.w,
          element.rect.w * shape.aspectRatio,
        ),
      ),
    );
  }

  void replaceElementMaskSource(String source) async {
    assert(state is PicElementMask, 'Element is not PicElementMask');
    final element = state as PicElementMask;
    if (element.source == source) return;
    await PreloadImageObject.instance.load(source);
    state = element.copyWith(
      source: source,
      // reset scale, rotation, translate info
      metadata: null,
      metadataNullable: true,
    );
  }

  @override
  set state(PicElement value) {
    final template = ref.read(picTemplateEditorProvider).value;
    final index = template.elements.indexWhere((e) => e.key == value.key);
    if (index != -1) {
      template.elements[index] = value;
    }

    runImmediatelyOrAfterFrame(() {
      super.state = value;
    });
  }
}
