// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pic_element_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$picElementSelectionHash() =>
    r'39b0c5837d44593b1676c2862051e6c96152ac20';

/// See also [PicElementSelection].
@ProviderFor(PicElementSelection)
final picElementSelectionProvider =
    AutoDisposeNotifierProvider<PicElementSelection, String?>.internal(
  PicElementSelection.new,
  name: r'picElementSelectionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$picElementSelectionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PicElementSelection = AutoDisposeNotifier<String?>;
String _$picElementEditorHash() => r'8c410f56343b757b0a987d650e728111ebede476';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$PicElementEditor
    extends BuildlessAutoDisposeNotifier<PicElement> {
  late final String key;

  PicElement build(
    String key,
  );
}

/// See also [PicElementEditor].
@ProviderFor(PicElementEditor)
const picElementEditorProvider = PicElementEditorFamily();

/// See also [PicElementEditor].
class PicElementEditorFamily extends Family<PicElement> {
  /// See also [PicElementEditor].
  const PicElementEditorFamily();

  /// See also [PicElementEditor].
  PicElementEditorProvider call(
    String key,
  ) {
    return PicElementEditorProvider(
      key,
    );
  }

  @override
  PicElementEditorProvider getProviderOverride(
    covariant PicElementEditorProvider provider,
  ) {
    return call(
      provider.key,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'picElementEditorProvider';
}

/// See also [PicElementEditor].
class PicElementEditorProvider
    extends AutoDisposeNotifierProviderImpl<PicElementEditor, PicElement> {
  /// See also [PicElementEditor].
  PicElementEditorProvider(
    String key,
  ) : this._internal(
          () => PicElementEditor()..key = key,
          from: picElementEditorProvider,
          name: r'picElementEditorProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$picElementEditorHash,
          dependencies: PicElementEditorFamily._dependencies,
          allTransitiveDependencies:
              PicElementEditorFamily._allTransitiveDependencies,
          key: key,
        );

  PicElementEditorProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.key,
  }) : super.internal();

  final String key;

  @override
  PicElement runNotifierBuild(
    covariant PicElementEditor notifier,
  ) {
    return notifier.build(
      key,
    );
  }

  @override
  Override overrideWith(PicElementEditor Function() create) {
    return ProviderOverride(
      origin: this,
      override: PicElementEditorProvider._internal(
        () => create()..key = key,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        key: key,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<PicElementEditor, PicElement>
      createElement() {
    return _PicElementEditorProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is PicElementEditorProvider && other.key == key;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, key.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin PicElementEditorRef on AutoDisposeNotifierProviderRef<PicElement> {
  /// The parameter `key` of this provider.
  String get key;
}

class _PicElementEditorProviderElement
    extends AutoDisposeNotifierProviderElement<PicElementEditor, PicElement>
    with PicElementEditorRef {
  _PicElementEditorProviderElement(super.provider);

  @override
  String get key => (origin as PicElementEditorProvider).key;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
