// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:convert';

import '../../../data/data.dart';

enum EditTooltipFeature {
  rotate,
}

abstract class EditTooltipRepository {
  Set<EditTooltipFeature> fetchListEditTooltip();

  Future<bool> saveShownTooltip(EditTooltipFeature feature);
}

class EditTooltipRepositoryImpl implements EditTooltipRepository {
  final AppPreferences preferences;

  EditTooltipRepositoryImpl({required this.preferences});

  @override
  Set<EditTooltipFeature> fetchListEditTooltip() {
    final encode = preferences.getString(AppPreferences.shownTooltipFeatures);
    if (encode == null) {
      return <EditTooltipFeature>{};
    }

    final decode = jsonDecode(encode) as List<dynamic>;
    if (decode.isEmpty) {
      return <EditTooltipFeature>{};
    }

    return decode.map<EditTooltipFeature>((e) => EditTooltipFeature.values[e]).toSet();
  }

  @override
  Future<bool> saveShownTooltip(EditTooltipFeature feature) {
    final encode = preferences.getString(AppPreferences.shownTooltipFeatures);
    List<int> list = [];
    if (encode != null) {
      list = jsonDecode(encode) as List<int>;
      if (list.contains(feature.index)) return Future.value(false);
    }
    list.add(feature.index);
    return preferences.setString(AppPreferences.shownTooltipFeatures, jsonEncode(list));
  }
}
