// Flutter project by quanghuuxx (<EMAIL>)

import '../../../component/services/remote_config/remote_config_service.dart';
import '../../../component/services/remote_config/remote_configuration.dart';
import '../../../data/data.dart';

abstract class EditorRepository {
  int saveTemplate(PicTemplate template);

  Future<bool> saveExportSlot(int slot);
  int getExportSlot();
}

class EditorRepositoryImpl implements EditorRepository {
  final PicTemplateBox picTemplateBox;
  final AppPreferences preferences;

  EditorRepositoryImpl({
    required this.picTemplateBox,
    required this.preferences,
  });

  @override
  int saveTemplate(PicTemplate template) {
    return picTemplateBox.put(PicTemplateEntity.fromPicTemplate(template));
  }

  @override
  int getExportSlot() {
    final map = preferences.getMap(AppPreferences.exportSlotKey);
    if (map == null) {
      return RemoteConfigService.instance.get<int>(RemoteConfiguration.numExportTemplate);
    }

    final date = DateTime.fromMillisecondsSinceEpoch(map['date'] as int);
    final now = DateTime.now();
    if (now.day == date.day && now.month == date.month && now.year == date.year) {
      return map['slot'] as int;
    } else {
      return RemoteConfigService.instance.get<int>(RemoteConfiguration.numExportTemplate);
    }
  }

  @override
  Future<bool> saveExportSlot(int slot) {
    Map<String, dynamic>? map = preferences.getMap(AppPreferences.exportSlotKey);

    final now = DateTime.now();
    if (map == null) {
      return preferences.setMap(
        AppPreferences.exportSlotKey,
        {
          'date': now.millisecondsSinceEpoch,
          'slot': slot,
        },
      );
    }

    final date = DateTime.fromMillisecondsSinceEpoch(map['date'] as int);
    if (now.day == date.day && now.month == date.month && now.year == date.year) {
      map['slot'] = slot;
    } else {
      map = {
        'date': now.millisecondsSinceEpoch,
        'slot': slot,
      };
    }
    return preferences.setMap(AppPreferences.exportSlotKey, map);
  }
}
