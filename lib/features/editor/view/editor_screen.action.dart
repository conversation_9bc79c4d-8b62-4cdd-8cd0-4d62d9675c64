// Flutter project by quanghuuxx (<EMAIL>)

part of 'editor_screen.dart';

extension on _EditorScreenState {
  Future<void> initial() async {
    // await FlutterWindowManagerPlus.addFlags(FlutterWindowManagerPlus.FLAG_SECURE);
    // load fragment cho phần xử lý filter ảnh
    await imageFilterFragment.initFragment();
  }

  void _onElementSelectionChanged(String? old, String? value) {
    if (value == null) {
      final bottomEnum = context.read(bottomBarEditorProvider).bottom?.value;
      if (bottomEnum is SubBottomBarItem) {
        context.read(bottomBarEditorProvider.notifier).popBottom();
      }

      floatingBarViewModel.main = null;
    } else {
      final records = PicElement.decodeKey(value);
      if (old != null) {
        final oldRecords = PicElement.decodeKey(old);
        if (oldRecords.type == records.type) {
          return;
        }
      }

      floatingBarViewModel.main = switch (records.type) {
        PicElementType.text => MainFloatingBarEnum.text,
        PicElementType.mask => MainFloatingBarEnum.mask,
        PicElementType.sticker => MainFloatingBarEnum.sticker,
      };
    }
  }

  void _onListenerCurrentSubFloatingBarEvent(CurrentSubFloatingBarEvent? old, CurrentSubFloatingBarEvent event) {
    if (event.value == TransformFloatingBarEnum.rotate) {
      context.read(picTemplateEditorProvider.notifier).push();
    }
  }

  void _onBack() {
    if (floatingBarViewModel.pop()) {
      return;
    }

    it.get<AlertDialogService>().add(
          AlertDialogInfo(
            barrierDismissible: true,
            contents: [
              AlertDialogContent(
                content: context.tr(LKey.editor_back_alert),
                type: AlertDialogContentType.text,
              ),
            ],
            actions: [
              AlertDialogAction(
                title: context.tr(LKey.common_exit),
                action: (ctx) {
                  ctx.popPage();
                },
              ),
              AlertDialogAction(
                title: context.tr(LKey.common_save),
                action: (ctx) {
                  ctx.pop();
                  _onSave();
                },
              ),
            ],
          ),
        );
  }

  void _onSave([bool force = false]) {
    final render = areaKey.currentContext.findRenderObject() as RenderRepaintBoundary;
    final pixelRatio = MediaQuery.devicePixelRatioOf(context);

    it.get<AlertDialogService>().add(LoadingDialog());
    context
        .read(picTemplateEditorProvider.notifier)
        .submit(
          render,
          pixelRatio: pixelRatio,
          saveStatus: SaveStatus.export,
          force: force,
        )
        .then((_) {
      context.popPage();
    }).onError((err, trace) {
      if (err is BaseException && err.code == AppError.forbidden.code) {
        it.get<AlertDialogService>().add(
              AlertDialogInfo(
                contents: [
                  AlertDialogContent(
                    content: context.tr(LKey.editor_not_availiable_export_slot),
                    type: AlertDialogContentType.text,
                  ),
                ],
                actions: [
                  AlertDialogAction(
                    title: context.tr(LKey.common_watch_ad),
                    action: (ctx) {
                      ctx.pop();
                      _onSave(true);
                    },
                  ),
                  AlertDialogAction(
                    title: context.tr(LKey.common_upgrade),
                    action: (ctx) {
                      ctx.pushNamed(Routes.inAppPurchases);
                    },
                  ),
                  AlertDialogAction(
                    title: context.tr(LKey.common_cancel),
                  ),
                ],
              ),
            );
      } else if (err != null) {
        it.get<AlertDialogService>().add(
              ErrorDialogInfo.fromBaseException(
                context,
                exception: BaseException.from(err),
              ),
            );
      }
    });
  }
}
