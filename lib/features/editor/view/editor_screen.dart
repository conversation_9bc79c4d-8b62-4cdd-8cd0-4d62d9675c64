// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart' show RenderRepaintBoundary;
import 'package:flutter/services.dart';
import 'package:flutter_windowmanager_plus/flutter_windowmanager_plus.dart';
import 'package:it/it.dart';

import '../../../component/dependency_injector/di.dart';
import '../../../component/services/alert_dialog_service/alert_dialog_service.dart';
import '../../../component/services/alert_dialog_service/model/error_dialog_info.dart';
import '../../../component/services/alert_dialog_service/model/loading_dialog.dart';
import '../../../component/widget/inherited_consumer.dart';
import '../../../configurations/app_error.dart';
import '../../../data/data.dart';
import '../../../resource/localization/lkey.dart';
import '../../../resource/style/app_theme_ext.dart';
import '../../../routes/routes.dart';
import '../model/bottom_bar_enum.dart';
import '../model/edit_area_key.dart';
import '../model/events.dart';
import '../model/floating_bar_enum.dart';
import '../providers/bottom_bar_provider.dart';
import '../providers/bottom_bar_state.dart';
import '../providers/editor_providers.dart';
import '../providers/floating_bar_viewmodel.dart';
import '../providers/pic_element_provider.dart';
import 'editor_constant.dart';
import 'widget/edit_area.dart';
import 'widget/editor_bottom_bar.dart';

part './editor_screen.action.dart';

class EditorScreen extends StatefulWidget {
  EditorScreen({super.key});

  static EditAreaKey area(BuildContext context) {
    return context.stateOf<_EditorScreenState>().areaKey;
  }

  @override
  State<EditorScreen> createState() => _EditorScreenState();
}

class _EditorScreenState extends AppScreenState<EditorScreen> {
  late final FloatingBarViewModel floatingBarViewModel = ViewModel.of<FloatingBarViewModel>(context);

  late final PicTemplateEditor picTemplateEditor = context.read(picTemplateEditorProvider.notifier);
  late final PicElementSelection picElementSelection = context.read(picElementSelectionProvider.notifier);

  final EditAreaKey areaKey = EditAreaKey();

  Future<void>? _futureInitial;

  @override
  void initState() {
    super.initState();
    _futureInitial = initial();
  }

  @override
  void onViewCreated() {
    super.onViewCreated();
    context.observe<CurrentSubFloatingBarEvent>(_onListenerCurrentSubFloatingBarEvent);
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: ThemeData.estimateBrightnessForColor(theme.themeColor.background).opposite,
      ),
      child: FutureBuilder<void>(
        future: _futureInitial,
        builder: (context, snap) {
          if (snap.connectionState != ConnectionState.done) {
            return ColoredBox(
              color: theme.themeColor.background,
              child: const LoadingWidget(),
            );
          }

          context.listen<String?>(picElementSelectionProvider, _onElementSelectionChanged);

          return PopScope(
            canPop: false,
            onPopInvokedWithResult: (didPop, result) {
              if (didPop) return;

              _onBack();
            },
            child: Scaffold(
              // appBar: EditAppbar(
              //   onBack: _onBack,
              //   onSave: _onSave,
              // ),
              resizeToAvoidBottomInset: false,
              extendBody: true,
              body: EditArea(
                areaKey: areaKey,
                onBack: _onBack,
              ),
              bottomNavigationBar: const EditorBottomBar(),
            ),
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _futureInitial = null;
    FlutterWindowManagerPlus.clearFlags(FlutterWindowManagerPlus.FLAG_SECURE);
    super.dispose();
  }
}
