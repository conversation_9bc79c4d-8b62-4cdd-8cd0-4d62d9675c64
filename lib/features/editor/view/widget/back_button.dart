// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/material.dart';
import 'package:it/it.dart';

import '../../../../resource/icon_constants.dart';
import '../../model/events.dart';
import 'edit_floating_bar.dart';

class EditBackButton extends StatelessWidget {
  const EditBackButton({super.key, required this.onBack});

  final VoidCallback onBack;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 12,
      top: MediaQuery.paddingOf(context).top + 12,
      child: IconFloatingButton(
        icon: IconConstants.ic_arrow_left,
        onPressed: context.on<ReplaceBackActionEvent>()?.onBack ?? onBack,
      ),
    );
  }
}
