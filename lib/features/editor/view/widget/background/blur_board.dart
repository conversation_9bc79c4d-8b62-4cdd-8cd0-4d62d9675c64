// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:ui';

import 'package:flutter/material.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../data/data.dart';
import '../../../providers/editor_providers.dart';
import '../pic_image.dart';

class BlurBoard extends StatelessWidget {
  const BlurBoard({super.key});

  @override
  Widget build(BuildContext context) {
    final template = context.read(picTemplateEditorProvider.notifier).template;
    final child = PicImage.fromTemplate(template);
    return ClipRect(
      child: Builder(
        builder: (context) {
          final blur = context.watch(picTemplateEditorProvider.select((e) => e.value.blur ?? PicBlur.none));
          return ImageFiltered(
            imageFilter: ImageFilter.blur(
              sigmaX: blur.sigmaX,
              sigmaY: blur.sigmaY,
            ),
            child: child,
          );
        },
      ),
    );
  }
}
