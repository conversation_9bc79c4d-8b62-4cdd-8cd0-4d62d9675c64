// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:ui' as ui;

import 'package:core/core.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:it/it.dart';

import '../../../../../component/dependency_injector/di.dart';
import '../../../../../component/services/alert_dialog_service/alert_dialog_service.dart';
import '../../../../../component/services/alert_dialog_service/model/loading_dialog.dart';
import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../data/model/template/pic_template.dart';
import '../../../funcs/painting.dart';
import '../../../mixin/bottom_action_mixin.dart';
import '../../../model/crop_rectangle.dart';
import '../../../providers/bottom_bar_state.dart';
import '../../../providers/editor_providers.dart';
import '../../../providers/floating_bar_viewmodel.dart';
import '../edit_area.dart';
import '../edit_floating_bar.dart';

class CropBoard extends StatefulWidget {
  const CropBoard({super.key});

  @override
  State<CropBoard> createState() => _CropBoardState();
}

class _CropBoardState extends State<CropBoard> with StateBottomActionMixin {
  final ValueNotifier<CropRectangle> rectInteractive = ValueNotifier<CropRectangle>(CropRectangle.zero);
  final CropRectangleDragHandler cropRectangleDragHandler = CropRectangleDragHandler();
  final AlertDialogService alertDialogService = it.get<AlertDialogService>();

  late final OverlayArea overlayState = OverlayArea.of(context);

  OverlayAreaEntry? _overlaySubmit;

  ui.Image? image;
  Rect boundary = Rect.zero;

  @override
  void initState() {
    super.initState();
    _registerRecognizer();

    _bindingOverlay();
  }

  @override
  Widget build(BuildContext context) {
    final template = templateEditor.template;

    context.listen(picTemplateEditorProvider, _onListen);

    return GestureDetector(
      onPanStart: _onDragStart,
      onPanUpdate: cropRectangleDragHandler.onDragUpdate,
      onPanEnd: (details) {
        cropRectangleDragHandler
          ..onDragEnd(details)
          ..set(null);
      },
      onPanCancel: () {
        cropRectangleDragHandler
          ..onDragCancel()
          ..set(null);
      },
      child: WidgetBoundsWrapper(
        onBoundsChanged: (value) {
          boundary = value;
          _caculateRect();
        },
        child: CustomPaint(
          foregroundPainter: _CropPainter(
            notifier: rectInteractive,
          ),
          child: CoreImage(
            template.originImg,
            onLoaded: (info) {
              if (image == info?.image) return;
              image = info?.image;
              _caculateRect();
            },
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    rectInteractive.dispose();
    _removeOverlay();
    super.dispose();
  }

  @override
  void onSubmitState() {
    _crop().then((_) {
      super.onSubmitState();
    });
  }

  void _onListen(History<PicEditingTemplate>? old, History<PicEditingTemplate> state) {
    if (old?.value.ratio != state.value.ratio) {
      _caculateRect(forcedFollowBoundary: true);
    }
  }

  void _caculateRect({bool forcedFollowBoundary = false}) {
    if (image == null || boundary.isEmpty) return;

    final template = templateEditor.template;

    Rect base;

    // Nếu template.boundary = image.size thì có thể hiểu là ảnh chưa được cắt trước đó
    // thì mặc định sẽ full hết view của boundary
    if (forcedFollowBoundary || template.imageBounds.w == image!.width && template.imageBounds.h == image!.height) {
      double w = boundary.width;
      double h = boundary.height;

      if (template.ratio != null) {
        w = boundary.width;
        h = w / template.ratio!;
        if (h > boundary.height) {
          h = boundary.height;
          w = h * template.ratio!;
        }
      }

      base = Rect.fromCenter(
        center: Offset(boundary.width / 2, boundary.height / 2),
        width: w,
        height: h,
      );
    }
    // ngược lại thì cần scale và tính rect crop theo tỉ lệ được crop trước đó của template.imageBounds
    else {
      final scaleX = boundary.width / image!.width;
      final scaleY = boundary.height / image!.height;

      base = Rect.fromLTWH(
        template.imageBounds.x * scaleX,
        template.imageBounds.y * scaleY,
        template.imageBounds.w * scaleX,
        template.imageBounds.h * scaleY,
      );
    }

    rectInteractive.value = CropRectangle(base);
  }

  Future<void> _crop() {
    alertDialogService.add(LoadingDialog());

    final scaleX = image!.width / boundary.width;
    final scaleY = image!.height / boundary.height;
    final rect = rectInteractive.value.rect;

    return templateEditor
        .cropBackground(
      Rect.fromLTWH(
        rect.left * scaleX,
        rect.top * scaleY,
        rect.width * scaleX,
        rect.height * scaleY,
      ),
    )
        .then((_) {
      alertDialogService.closeCurrentAlert((d) => d is LoadingDialog);
    });
  }

  void _onDragStart(DragStartDetails details) {
    final rectangle = rectInteractive.value;

    if (CropRectangle.hitTest(rectangle.topLeft, details.localPosition)) {
      cropRectangleDragHandler.set(cropRectangleDragHandler.onTopLeft);
    } else if (CropRectangle.hitTest(rectangle.topRight, details.localPosition)) {
      cropRectangleDragHandler.set(cropRectangleDragHandler.onTopRight);
    } else if (CropRectangle.hitTest(rectangle.bottomLeft, details.localPosition)) {
      cropRectangleDragHandler.set(cropRectangleDragHandler.onBottomLeft);
    } else if (CropRectangle.hitTest(rectangle.bottomRight, details.localPosition)) {
      cropRectangleDragHandler.set(cropRectangleDragHandler.onBottomRight);
    } else {
      if (templateEditor.template.ratio != null) {
        if (rectangle.rect.contains(details.localPosition)) {
          cropRectangleDragHandler.set(cropRectangleDragHandler.center);
        }
      } else if (CropRectangle.hitTest(rectangle.centerLeft, details.localPosition)) {
        cropRectangleDragHandler.set(cropRectangleDragHandler.centerLeft);
      } else if (CropRectangle.hitTest(rectangle.centerRight, details.localPosition)) {
        cropRectangleDragHandler.set(cropRectangleDragHandler.centerRight);
      } else if (CropRectangle.hitTest(rectangle.centerTop, details.localPosition)) {
        cropRectangleDragHandler.set(cropRectangleDragHandler.centerTop);
      } else if (CropRectangle.hitTest(rectangle.centerBottom, details.localPosition)) {
        cropRectangleDragHandler.set(cropRectangleDragHandler.centerBottom);
      } else if (rectangle.rect.contains(details.localPosition)) {
        cropRectangleDragHandler.set(cropRectangleDragHandler.center);
      }
    }

    cropRectangleDragHandler.onDragStart(details);
  }

  void _registerRecognizer() {
    cropRectangleDragHandler
      ..onTopLeft = (PanGestureRecognizer()
        ..onUpdate = (details) {
          final ratio = templateEditor.template.ratio;
          final rectangle = rectInteractive.value;

          double dy = details.delta.dy;
          if (ratio != null) {
            dy = details.delta.dx / ratio;

            if (!boundary.contains(rectangle.rect.topLeft.translate(details.delta.dx, dy))) {
              return;
            }
          }

          rectInteractive.value = rectangle.translale(
            left: details.delta.dx,
            top: dy,
            boundary: boundary,
          );
        })
      ..onTopRight = (PanGestureRecognizer()
        ..onUpdate = (details) {
          final ratio = templateEditor.template.ratio;
          final rectangle = rectInteractive.value;

          double dy = details.delta.dy;
          if (ratio != null) {
            dy = -details.delta.dx / ratio;
            if (!boundary.contains(rectangle.rect.topRight.translate(details.delta.dx, dy))) {
              return;
            }
          }

          rectInteractive.value = rectangle.translale(
            right: details.delta.dx,
            top: dy,
            boundary: boundary,
          );
        })
      ..onBottomLeft = (PanGestureRecognizer()
        ..onUpdate = (details) {
          final ratio = templateEditor.template.ratio;
          final rectangle = rectInteractive.value;

          double dx = details.delta.dx;
          if (ratio != null) {
            dx = -details.delta.dy * ratio;
            if (!boundary.contains(rectangle.rect.bottomLeft.translate(dx, details.delta.dy))) {
              return;
            }
          }

          rectInteractive.value = rectangle.translale(
            left: dx,
            bottom: details.delta.dy,
            boundary: boundary,
          );
        })
      ..onBottomRight = (PanGestureRecognizer()
        ..onUpdate = (details) {
          final ratio = templateEditor.template.ratio;
          final rectangle = rectInteractive.value;

          double dx = details.delta.dx;
          if (ratio != null) {
            dx = details.delta.dy * ratio;
            if (!boundary.contains(rectangle.rect.bottomRight.translate(dx, details.delta.dy))) {
              return;
            }
          }
          rectInteractive.value = rectangle.translale(
            right: dx,
            bottom: details.delta.dy,
            boundary: boundary,
          );
        })
      ..centerLeft = (PanGestureRecognizer()
        ..onUpdate = (details) {
          final rectangle = rectInteractive.value;
          rectInteractive.value = rectangle.translale(
            left: details.delta.dx,
            boundary: boundary,
          );
        })
      ..centerTop = (PanGestureRecognizer()
        ..onUpdate = (details) {
          final rectangle = rectInteractive.value;
          rectInteractive.value = rectangle.translale(
            top: details.delta.dy,
            boundary: boundary,
          );
        })
      ..center = (PanGestureRecognizer()
        ..onUpdate = (details) {
          final rectangle = rectInteractive.value;
          rectInteractive.value = rectangle.shift(
            details.delta,
            boundary: boundary,
          );
        })
      ..centerRight = (PanGestureRecognizer()
        ..onUpdate = (details) {
          final rectangle = rectInteractive.value;
          rectInteractive.value = rectangle.translale(
            right: details.delta.dx,
            boundary: boundary,
          );
        })
      ..centerBottom = (PanGestureRecognizer()
        ..onUpdate = (details) {
          final rectangle = rectInteractive.value;
          rectInteractive.value = rectangle.translale(
            bottom: details.delta.dy,
            boundary: boundary,
          );
        });
  }

  void _bindingOverlay() {
    _overlaySubmit = OverlayAreaEntry(
      builder: (ctx) {
        final padding = MediaQuery.paddingOf(ctx) + kPaddingAll8;
        return Positioned(
          top: padding.top,
          right: padding.right,
          child: IconFloatingButton(
            onPressed: () {
              _crop().then((_) {
                context.safety((context) {
                  ViewModel.of<FloatingBarViewModel>(context).pop();
                });
              });
            },
            child: const Icon(
              Icons.check_rounded,
              size: 26,
            ),
          ),
        );
      },
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        overlayState.add(_overlaySubmit!);
      }
    });
  }

  void _removeOverlay() {
    if (_overlaySubmit?.mounted == true) {
      _overlaySubmit?.remove();
    }
  }
}

class _CropPainter extends CustomPainter {
  final ValueNotifier<CropRectangle> notifier;

  _CropPainter({
    required this.notifier,
  }) : super(repaint: notifier);

  final Paint _paint = Paint();

  Rect bounds = Rect.zero;

  @override
  void paint(Canvas canvas, Size size) {
    if (notifier.value == CropRectangle.zero) {
      return;
    }
    bounds = Offset.zero & size;
    canvas
      ..saveLayer(bounds, _paint)
      ..drawPaint(
        _paint
          ..color = Colors.black45
          ..blendMode = BlendMode.srcOver,
      );

    final rCrop = notifier.value.rect;
    canvas
      ..drawRect(
        rCrop,
        _paint..blendMode = BlendMode.clear,
      )
      ..restore();

    paintLineGridLayout(canvas, rect: rCrop, paint: _paint..color = Colors.white);

    notifier.value.paintCorners(canvas, _paint..style = PaintingStyle.fill);
  }

  @override
  bool shouldRepaint(covariant _CropPainter old) {
    return old.notifier != notifier;
  }
}
