// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../data/data.dart';
import '../../../funcs/rendering.dart';
import '../../../model/image_filter_fragment.dart';
import '../../../providers/editor_providers.dart';
import '../../editor_constant.dart';
import '../pic_image.dart';

class FilterBoard extends StatefulWidget {
  const FilterBoard({super.key});

  @override
  State<FilterBoard> createState() => _FilterBoardState();
}

class _FilterBoardState extends StateBase<FilterBoard> {
  final ValueNotifier<PicImgFilter> notifier = ValueNotifier<PicImgFilter>(PicImgFilter.none);
  late final PicTemplateEditor picTemplateEditor = context.read(picTemplateEditorProvider.notifier);

  @override
  void onViewCreated() {
    super.onViewCreated();

    notifier.value = context.read(picTemplateEditorProvider).value.filter;
    context.listen(picTemplateEditorProvider.select((e) => e.value.filter), (old, state) {
      notifier.value = state;
    });
  }

  @override
  Widget build(BuildContext _) {
    final template = picTemplateEditor.template;
    final fittedSize = caculateFittedSizeOf(context, template.imageBounds.size);

    return CustomPaint(
      size: fittedSize,
      painter: _Painter(
        image: template.image,
        fragment: imageFilterFragment,
        filter: notifier,
        transform: caculateImageMatrix(
          template.imageBounds.size,
          fittedSize,
          rotation: template.rotation,
          flipEnum: template.flipEnum,
        ).storage,
      ),
    );
  }

  @override
  void dispose() {
    notifier.dispose();
    super.dispose();
  }
}

class _Painter extends CustomPainter {
  final ui.Image image;
  final ImageFilterFragment fragment;
  final ValueNotifier<PicImgFilter> filter;
  final Float64List transform;

  _Painter({
    required this.image,
    required this.fragment,
    required this.filter,
    required this.transform,
  }) : super(repaint: filter);

  @override
  void paint(ui.Canvas canvas, ui.Size size) {
    canvas
      ..save()
      ..clipRect(Offset.zero & size);

    PicImage.drawImage(
      canvas,
      transform: transform,
      image: image,
      uniForms: filter.value,
    );

    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
