// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/material.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../providers/editor_providers.dart';
import '../pic_image.dart';

class OpacityBoard extends StatelessWidget {
  const OpacityBoard({super.key});

  @override
  Widget build(BuildContext context) {
    final child = PicImage.fromTemplate(context.read(picTemplateEditorProvider).value);
    return Builder(
      builder: (context) {
        final template = context.watch(picTemplateEditorProvider).value;
        return Opacity(
          opacity: template.opacity,
          child: child,
        );
      },
    );
  }
}
