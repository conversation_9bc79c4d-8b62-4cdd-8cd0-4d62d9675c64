// Flutter project by quanghu<PERSON>x (<EMAIL>)

import 'dart:ui' as ui;

import 'package:core/core.dart';
import 'package:flutter/material.dart';
import 'package:it/it.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../data/data.dart';
import '../../../../../resource/localization/lkey.dart';
import '../../../../../resource/style/app_theme_ext.dart';
import '../../../funcs/painting.dart';
import '../../../funcs/rendering.dart';
import '../../../model/image_filter_fragment.dart';
import '../../../providers/bottom_bar_state.dart';
import '../../../providers/edit_tooltip_provider.dart';
import '../../../providers/editor_providers.dart';
import '../../../providers/floating_bar_viewmodel.dart';
import '../../../repository/edit_tooltip_repository.dart';
import '../box_toast.dart';
import '../edit_area.dart';
import '../edit_floating_bar.dart';
import '../pic_image.dart';

class RotationBoard extends StatefulWidget {
  const RotationBoard();

  @override
  State<RotationBoard> createState() => _RotationBoardState();
}

class _RotationBoardState extends StateBase<RotationBoard> {
  late final FloatingBarViewModel floatingBarViewModel = ViewModel.of(context);
  late final OverlayArea overlay = OverlayArea.of(context);
  late final PicTemplateEditor picTemplateEditor = context.read(picTemplateEditorProvider.notifier);

  final ValueNotifier<Matrix4> transformNotifier = ValueNotifier(Matrix4.zero());

  Size fittedSize = Size.zero;

  late final OverlayAreaEntry overlayRotating = OverlayAreaEntry(
    builder: (_) {
      return BoxToast(
        alignment: const Alignment(0, -0.8),
        child: _RotationToast(
          key: const ValueKey('_rotation_toast'),
          transformNotifier: transformNotifier,
        ),
      );
    },
  );

  late final OverlayAreaEntry overlaySubmit = OverlayAreaEntry(
    builder: (ctx) {
      final padding = MediaQuery.paddingOf(ctx) + kPaddingAll8;
      return Positioned(
        top: padding.top,
        right: padding.right,
        child: IconFloatingButton(
          onPressed: () {
            ViewModel.of<FloatingBarViewModel>(context).pop();
          },
          child: const Icon(
            Icons.check_rounded,
            size: 26,
          ),
        ),
      );
    },
  );

  late final OverlayAreaEntry overlayTooltip = OverlayAreaEntry(
    builder: (context) {
      return BoxToast(
        alignment: const Alignment(0, 0.7),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(
              child: Text(
                context.tr(LKey.rotation_tool_tip),
                style: Theme.of(context).themeText.caption,
              ),
            ),
            kBox8,
            IconButton(
              padding: EdgeInsets.zero,
              iconSize: kSixteen,
              constraints: const BoxConstraints.tightFor(width: kTwentyFour, height: kTwentyFour),
              tooltip: context.tr(LKey.dont_display_again),
              icon: Icon(
                Icons.close,
                color: Theme.of(context).themeColor.secondary200,
              ),
              onPressed: () {
                context.read(editTooltipStateProvider.notifier).saveShownTooltip(EditTooltipFeature.rotate);
                overlayTooltip.remove();
              },
            ),
          ],
        ),
      );
    },
  );

  @override
  void onViewCreated() {
    final template = picTemplateEditor.template;
    transformNotifier.value = caculateImageMatrix(
      template.imageBounds.size,
      fittedSize,
      rotation: template.rotation,
      flipEnum: template.flipEnum,
    );

    if (context.read(editTooltipStateProvider).contains(EditTooltipFeature.rotate)) {
      overlay.add(overlayTooltip);
    }
    overlay.add(overlaySubmit);

    floatingBarViewModel.replaceBackAction(() {
      ViewModel.of<FloatingBarViewModel>(context).pop();
      picTemplateEditor.pop();
    });
  }

  @override
  Widget build(BuildContext context) {
    final template = picTemplateEditor.template;
    // Tính kích thước hiển thị image sau khi scale theo kích thước view - padding
    fittedSize = caculateFittedSize(
      template.imageBounds.size,
      kPaddingAll8.deflateSize(MediaQuery.sizeOf(context)),
    );

    context.listen(picTemplateEditorProvider, onListen);

    return GestureDetector(
      onHorizontalDragStart: (details) {
        overlay.add(overlayRotating);
      },
      onHorizontalDragUpdate: (details) {
        transformNotifier.value = caculateImageMatrix(
          template.imageBounds.size,
          fittedSize,
          rotation: Matrix4Util.getRotationZ(transformNotifier.value) + details.delta.dx / 100,
          flipEnum: template.flipEnum,
        );
      },
      onHorizontalDragEnd: _submitRotation,
      onHorizontalDragCancel: _submitRotation,
      child: CustomPaint(
        size: fittedSize,
        painter: _RotationBoardPainter(
          transformNotifier: transformNotifier,
          image: template.image,
          uniForms: template.filter != PicImgFilter.none ? template.filter : null,
        ),
      ),
    );
  }

  @override
  void dispose() {
    floatingBarViewModel.replaceBackAction(null);
    transformNotifier.dispose();
    _disposeOverlay(overlaySubmit);
    _disposeOverlay(overlayRotating);
    _disposeOverlay(overlayTooltip);
    super.dispose();
  }

  void _submitRotation([Object? _]) {
    final radians = Matrix4Util.getRotationZ(transformNotifier.value);
    picTemplateEditor.updateAttributes(rotation: radians);
    if (overlayRotating.mounted) {
      overlayRotating.remove();
    }
  }

  void _disposeOverlay(final OverlayAreaEntry overlay) {
    if (overlay.mounted) {
      overlay.remove();
    }
  }

  void onListen(History<PicTemplate>? old, History<PicTemplate> state) {
    if (Matrix4Util.getRotationZ(transformNotifier.value) != state.value.rotation) {
      final template = picTemplateEditor.template;
      transformNotifier.value = caculateImageMatrix(
        template.imageBounds.size,
        fittedSize,
        rotation: state.value.rotation,
        flipEnum: template.flipEnum,
      );
    }
  }
}

class _RotationBoardPainter extends CustomPainter {
  final ValueNotifier<Matrix4> transformNotifier;
  final ui.Image image;
  final UniForms? uniForms;

  _RotationBoardPainter({
    required this.transformNotifier,
    required this.image,
    this.uniForms,
  }) : super(repaint: transformNotifier);

  final Paint _paint = Paint();
  final Paint _maskPaint = Paint();

  Rect rCrop = Rect.zero;

  @override
  void paint(Canvas canvas, Size size) {
    if (transformNotifier.value.isZero()) return;

    rCrop = Offset.zero & size;

    final matrix = caculateRotationAndFittedMatrix(Matrix4Util.getRotationZ(transformNotifier.value), size).storage;

    canvas
      ..saveLayer(null, _paint)
      ..drawRect(
        rCrop,
        Paint()
          ..imageFilter = ui.ImageFilter.matrix(matrix)
          ..blendMode = BlendMode.srcOver
          ..color = Colors.black,
      );

    PicImage.drawImage(
      canvas,
      transform: transformNotifier.value.storage,
      image: image,
      uniForms: uniForms,
      paint: _paint
        ..blendMode = BlendMode.srcIn
        ..imageFilter = null,
    );

    canvas
      ..restore()

      // save layer để xử lý vẽ lớp phủ mờ bên dưới
      ..saveLayer(
        null,
        _maskPaint
          ..blendMode = BlendMode.srcOver
          ..color = Colors.black,
      )

      // vẽ lớp phủ mờ lên ảnh
      ..drawRect(
        rCrop,
        _maskPaint
          ..color = Colors.black54
          ..blendMode = BlendMode.srcOver
          ..imageFilter = ui.ImageFilter.matrix(matrix),
      )

      // đục lớp phủ mờ bên trên theo [rCrop] để hiển thị ảnh bên dưới
      ..drawRect(
        rCrop,
        _maskPaint
          ..blendMode = BlendMode.clear
          ..imageFilter = null,
      )

      // khôi phục lại canvas, sau lệnh saveLayer bên trên
      ..restore();

    paintLineGridLayout(
      canvas,
      rect: rCrop,
      paint: _paint
        ..color = Colors.white
        ..blendMode = BlendMode.srcOver,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

class _RotationToast extends StatelessWidget {
  final ValueNotifier<Matrix4> transformNotifier;
  const _RotationToast({super.key, required this.transformNotifier});

  @override
  Widget build(BuildContext context) {
    final matrix = context.listenable(transformNotifier).value;
    final radians = Matrix4Util.getRotationZ(matrix);
    double degress = MathUtil.radians2Degrees(radians);
    if (degress < 0) {
      degress = 360 + degress;
    }

    return Text(
      '${degress.toStringAsFixed(2)}°',
    );
  }
}
