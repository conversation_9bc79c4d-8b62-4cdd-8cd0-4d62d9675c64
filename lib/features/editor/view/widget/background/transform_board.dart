// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/material.dart';
import 'package:it/it.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../model/events.dart';
import '../../../model/floating_bar_enum.dart';
import '../../../providers/editor_providers.dart';
import '../pic_image.dart';
import 'blur_board.dart';
import 'crop_board.dart';
import 'filter_board.dart';
import 'opacity_board.dart';
import 'rotate_board.dart';

class TransformBoard extends StatelessWidget {
  const TransformBoard({super.key});

  @override
  Widget build(BuildContext context) {
    final bottom = context.on<CurrentSubFloatingBarEvent>()?.value;

    switch (bottom) {
      case TransformFloatingBarEnum.crop:
        return const CropBoard();
      case TransformFloatingBarEnum.rotate:
        return const RotationBoard();
      case TransformFloatingBarEnum.blur:
        return const BlurBoard();
      case TransformFloatingBarEnum.opacity:
        return const OpacityBoard();
      case TransformFloatingBarEnum.filter:
        return const FilterBoard();
    }

    final template = context.watch(picTemplateEditorProvider).value;
    return PicImage.fromTemplate(template);
  }
}
