// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/common_const.dart';
import 'package:flutter/material.dart';

import '../../../../resource/style/app_theme_ext.dart';

class BoxToast extends StatelessWidget {
  const BoxToast({
    super.key,
    required this.alignment,
    required this.child,
  });

  final Alignment alignment;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Align(
      alignment: alignment,
      child: Material(
        elevation: 1,
        color: theme.themeColor.container,
        shape: const StadiumBorder(),
        child: Padding(
          padding: kPaddingAll8,
          child: child,
        ),
      ),
    );
  }
}
