// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

import '../../../../resource/style/app_theme_ext.dart';
import '../editor_screen.dart';
import 'edit_area.dart';

class DraggableWidget extends StatefulWidget {
  static const double iconSize = 24;
  static const double borderWidth = 1.5;
  static const double inflateSize = DraggableWidget.iconSize;

  static const EdgeInsets halfIconInsets = EdgeInsets.all(iconSize / 2);

  const DraggableWidget({
    super.key,
    required this.rect,
    required this.rotation,
    required this.builder,
    required this.onUpdateRect,
    this.child,
    this.isSelected = false,
    this.disable = false,
    this.onTap,
    this.onDoubleTap,
    this.onDelete,
    this.onDuplicate,
    this.onDragUpdate,
    this.onDragEnd,
  });

  final Rect rect;
  final double rotation;
  final Widget Function(Rect, double, Widget?) builder;
  final Widget? child;
  final bool isSelected, disable;
  final VoidCallback? onTap, onDoubleTap, onDelete, onDuplicate;
  final void Function(Rect, double)? onDragUpdate, onDragEnd;
  final Rect Function(Rect, Offset, double) onUpdateRect;

  @override
  State<DraggableWidget> createState() => _DraggableWidgetState();
}

class _DraggableWidgetState extends StateBase<DraggableWidget> with ListenerGestureRecognizer {
  late final editArea = EditArea.of(context);
  late final OverlayArea overlayState = OverlayArea.of(context);

  final ValueUpdater<double> _scaleUpdater = ValueUpdater<double>(1.0, (old, value) => value / old);
  final ValueUpdater<double> _rotationUpdater = ValueUpdater<double>(0.0, (old, value) => value - old);

  final ScaleGestureRecognizer _scaleRecognizer = ScaleGestureRecognizer();
  final Matrix4 _rotation = Matrix4.identity();

  late Rect _rect = widget.rect;

  OverlayAreaEntry? _overlay;

  @override
  void initState() {
    super.initState();

    _scaleRecognizer
      ..onStart = _onScaleStart
      ..onUpdate = _onScaleUpdate
      ..onEnd = _onScaleEnd;

    if (widget.rotation != 0) {
      _rotation.rotateZ(widget.rotation);
    }

    editArea.addListener(this);
  }

  @override
  void onViewCreated() {
    super.onViewCreated();
    // tạo và insert 1 overlay để vẽ boder, và các btn của drag
    _bindingOverlay();
  }

  @override
  void didUpdateWidget(covariant DraggableWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (_rect != widget.rect && _overlay?.mounted == true && widget.isSelected) {
      _overlay?.setState(() {});
    }

    _rect = widget.rect;

    if (_overlay?.mounted == true && !widget.isSelected) {
      _overlay?.remove();
    } else if (_overlay?.mounted != true && widget.isSelected) {
      overlayState.add(_overlay!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Positioned.fromRect(
      rect: _rect,
      child: Transform(
        transform: _rotation,
        alignment: Alignment.center,
        child: GestureDetector(
          onTap: widget.disable ? null : widget.onTap,
          onDoubleTap: widget.disable ? null : widget.onDoubleTap,
          child: widget.builder(
            _rect,
            Matrix4Util.getRotationZ(_rotation),
            widget.child,
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    if (_overlay?.mounted == true) {
      _overlay?.remove();
      _overlay = null;
    }

    editArea.removeListener(this);
    _scaleRecognizer.dispose();
    super.dispose();
  }

  @override
  List<GestureRecognizer> get gestures => [
        if (widget.isSelected) _scaleRecognizer,
      ];

  void _onScaleStart(ScaleStartDetails details) {}

  void _onScaleUpdate(ScaleUpdateDetails details) {
    _rect = widget.onUpdateRect.call(_rect, details.focalPointDelta, _scaleUpdater.update(details.scale));

    if (details.rotation != 0) {
      _rotation.rotateZ(_rotationUpdater.update(details.rotation));
    }

    setState(() {
      widget.onDragUpdate?.call(
        _rect,
        Matrix4Util.getRotationZ(_rotation),
      );
      _overlay?.setState(() {});
    });
  }

  void _onScaleEnd(ScaleEndDetails details) {
    _scaleUpdater.value = 1.0;
    _rotationUpdater.value = 0.0;

    widget.onDragEnd?.call(
      _rect,
      Matrix4Util.getRotationZ(_rotation),
    );
  }

  void _bindingOverlay() {
    _overlay = OverlayAreaEntry(
      onBoundsChanged: (value) {
        _overlay?.setState(() {});
      },
      builder: (ctx) {
        final rr = _rect.inflate(DraggableWidget.inflateSize + (widget.isSelected ? DraggableWidget.borderWidth : 0));
        final offset = localToGlobal(EditorScreen.area(context).currentContext, rr.topLeft);
        return Positioned.fromRect(
          rect: offset & rr.size,
          child: Transform(
            transform: _rotation,
            alignment: Alignment.center,
            child: Stack(
              children: [
                IgnorePointer(
                  child: Container(
                    margin: DraggableWidget.halfIconInsets,
                    decoration: !widget.isSelected
                        ? null
                        : BoxDecoration(
                            border: Border.all(
                              color: theme.themeColor.neutral400,
                              width: DraggableWidget.borderWidth,
                            ),
                          ),
                  ),
                ),

                /// delete
                Positioned(
                  left: 0,
                  top: 0,
                  width: DraggableWidget.iconSize,
                  height: DraggableWidget.iconSize,
                  child: GestureDetector(
                    onTap: widget.onDelete,
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: theme.themeColor.surface,
                        border: Border.all(color: theme.themeColor.neutral300),
                      ),
                      child: Icon(
                        Icons.close,
                        size: kEighteen,
                        color: theme.themeColor.onSurface,
                      ),
                    ),
                  ),
                ),

                /// duplicate
                Positioned(
                  right: 0,
                  top: 0,
                  width: DraggableWidget.iconSize,
                  height: DraggableWidget.iconSize,
                  child: GestureDetector(
                    onTap: widget.onDuplicate,
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: theme.themeColor.surface,
                        border: Border.all(color: theme.themeColor.neutral300),
                      ),
                      child: Icon(
                        Icons.copy_all_outlined,
                        size: kEighteen,
                        color: theme.themeColor.onSurface,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );

    if (widget.isSelected) {
      overlayState.add(_overlay!);
    }
  }

  Offset localToGlobal(BuildContext context, Offset offset) {
    final renderBox = context.findRenderObject() as RenderBox;
    return renderBox.localToGlobal(offset, ancestor: overlayState.context.findRenderObject());
  }
}

class ValueUpdater<T> {
  final T Function(T old, T value) _onUpdated;

  ValueUpdater(
    this._value,
    this._onUpdated,
  );

  T _value;
  T get value => _value;
  set value(T value) {
    _value = value;
  }

  T update(T value) {
    final result = _onUpdated(_value, value);
    _value = value;
    return result;
  }
}
