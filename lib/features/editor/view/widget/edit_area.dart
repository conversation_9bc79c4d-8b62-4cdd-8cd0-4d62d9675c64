// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:it/it.dart';

import '../../../../component/widget/inherited_consumer.dart';
import '../../../../data/data.dart';
import '../../model/edit_area_key.dart';
import '../../model/events.dart';
import '../../model/floating_bar_enum.dart';
import '../../providers/editor_providers.dart';
import '../../providers/floating_bar_viewmodel.dart';
import '../../providers/pic_element_provider.dart';
import 'background/transform_board.dart';
import 'edit_floating_bar.dart';
import 'back_button.dart';
import 'mask/element_mask_widget.dart';
import 'pic_image.dart';
import 'sticker/element_sticker_widget.dart';
import 'text/element_text_widget.dart';

class EditArea extends StatefulWidget {
  static EditAreaGesture of(BuildContext context) {
    final state = context.findAncestorStateOfType<_EditAreaState>();
    if (state == null) {
      throw Exception('No EditAreaState found in context');
    }
    return state;
  }

  const EditArea({
    super.key,
    required this.areaKey,
    required this.onBack,
  });

  final EditAreaKey areaKey;
  final VoidCallback onBack;

  @override
  State<EditArea> createState() => _EditAreaState();
}

class _EditAreaState extends State<EditArea> with EditAreaGesture, ListenerGestureRecognizer {
  late final FloatingBarViewModel floatingBarViewModel = ViewModel.of<FloatingBarViewModel>(context);
  late final picElementSelection = context.read(picElementSelectionProvider.notifier);

  final TapGestureRecognizer tapRecognizer = TapGestureRecognizer();

  @override
  final List<GestureRecognizer> gestures = <GestureRecognizer>[];

  @override
  void initState() {
    super.initState();

    gestures.add(tapRecognizer..onTap = _onTapArea);
    listeners.add(this);
  }

  @override
  Widget build(BuildContext context) {
    return _OverlayArea(
      key: const ValueKey('overlay_area'),
      below: [
        Center(
          child: Listener(
            onPointerDown: _onPointerDown,
            onPointerPanZoomStart: _onPointerPanZoomStart,
            child: RepaintBoundary(
              key: widget.areaKey,
              child: Builder(
                builder: (context) {
                  final mainEnum = context.on<CurrentMainFloatingBarEvent>()?.value;

                  return switch (mainEnum) {
                    MainFloatingBarEnum.transform => const TransformBoard(),
                    _ => ClipRect(
                        child: Builder(
                          builder: (context) {
                            final template = context
                                .watch(
                                  picTemplateEditorProvider,
                                  when: (old, state) => !listEquals(old?.value.elements, state.value.elements),
                                )
                                .value;

                            return Stack(
                              clipBehavior: Clip.none,
                              children: [
                                PicImage.fromTemplate(template),
                                // elements
                                ...template.elements.expand((e) sync* {
                                  if (e is PicElementText) {
                                    yield ElementTextWidget(
                                      elementKey: e.key,
                                    );
                                  } else if (e is PicElementMask) {
                                    yield ElementMaskWidget(
                                      elementKey: e.key,
                                    );
                                  } else if (e is PicElementSticker) {
                                    yield ElementStickerWidget(
                                      elementKey: e.key,
                                    );
                                  }
                                }),
                              ],
                            );
                          },
                        ),
                      )
                  };
                },
              ),
            ),
          ),
        ),
      ],
      above: [
        const EditFloatingBar(),
        EditBackButton(onBack: widget.onBack),
      ],
    );
  }

  @override
  void dispose() {
    removeListener(this);
    tapRecognizer.dispose();
    gestures.clear();
    super.dispose();
  }

  void _onPointerDown(PointerDownEvent event) {
    for (final e in listeners) {
      e._handlePointerDown(event);
    }
  }

  void _onPointerPanZoomStart(PointerPanZoomStartEvent event) {
    for (final e in listeners) {
      e._handlePointerPanZoomStart(event);
    }
  }

  void _onTapArea() {
    picElementSelection.key = null;

    if (floatingBarViewModel.sub?.maintain != true && floatingBarViewModel.main?.maintain != true) {
      floatingBarViewModel.pop();
    }
  }
}

mixin EditAreaGesture {
  final List<ListenerGestureRecognizer> listeners = <ListenerGestureRecognizer>[];

  void addListener(ListenerGestureRecognizer listener) {
    if (listeners.contains(listener)) return;
    listeners.add(listener);
  }

  void removeListener(ListenerGestureRecognizer listener) {
    listeners.remove(listener);
  }
}

mixin ListenerGestureRecognizer {
  List<GestureRecognizer> get gestures;

  void _handlePointerDown(PointerDownEvent event) {
    for (final recognizer in gestures) {
      recognizer.addPointer(event);
    }
  }

  void _handlePointerPanZoomStart(PointerPanZoomStartEvent event) {
    for (final GestureRecognizer recognizer in gestures) {
      recognizer.addPointerPanZoom(event);
    }
  }
}

class _OverlayArea extends StatefulWidget {
  const _OverlayArea({super.key, required this.below, required this.above});

  final List<Widget> below, above;

  @override
  State<_OverlayArea> createState() => _OverlayAreaState();
}

class _OverlayAreaState extends State<_OverlayArea> implements OverlayArea {
  late final List<OverlayAreaEntry> children = [];

  bool _skipBoundsChanged = true;

  @override
  Widget build(BuildContext context) {
    return WidgetBoundsWrapper(
      onBoundsChanged: (value) {
        if (_skipBoundsChanged) {
          _skipBoundsChanged = false;
          return;
        }

        for (final e in children) {
          e.onBoundsChanged?.call(value.size);
        }
      },
      child: Stack(
        children: [
          ...widget.below,
          ...children.map((e) => _OverlayStateEntry(entry: e)),
          ...widget.above,
        ],
      ),
    );
  }

  @override
  void add(OverlayAreaEntry entry) {
    setState(() {
      children.add(entry.._owner = this);
    });
  }

  @override
  void remove(OverlayAreaEntry entry) {
    if (entry._owner == this) {
      setState(() {
        children.remove(entry.._owner = null);
      });
    }
  }

  @override
  void dispose() {
    for (final e in children) {
      e._owner = null;
    }

    children.clear();
    super.dispose();
  }

  @override
  void setState(VoidCallback fn) {
    runImmediatelyOrAfterFrame(() {
      if (mounted) {
        super.setState(fn);
      }
    });
  }
}

abstract class OverlayArea {
  static OverlayArea of(BuildContext context) {
    final state = context.findAncestorStateOfType<_OverlayAreaState>();
    if (state == null) {
      throw Exception('No OverlayAreaState found in $context');
    }
    return state;
  }

  BuildContext get context;

  void add(OverlayAreaEntry child);

  void remove(OverlayAreaEntry child);
}

class OverlayAreaEntry {
  final WidgetBuilder builder;
  final ValueChanged<Size>? onBoundsChanged;
  OverlayAreaEntry({
    required this.builder,
    this.onBoundsChanged,
  });

  OverlayArea? _owner;
  State? _state;

  bool get mounted => _owner != null;

  void remove() {
    if (mounted) {
      _owner?.remove(this);
    }
  }

  void setState(VoidCallback fn) {
    // ignore: invalid_use_of_protected_member
    _state?.setState(fn);
  }
}

class _OverlayStateEntry extends StatefulWidget {
  _OverlayStateEntry({
    required this.entry,
  }) : super(key: ValueKey(entry.hashCode));

  final OverlayAreaEntry entry;

  @override
  State<_OverlayStateEntry> createState() => _OverlayStateEntryState();
}

class _OverlayStateEntryState extends State<_OverlayStateEntry> {
  @override
  void initState() {
    super.initState();
    widget.entry._state = this;
  }

  @override
  Widget build(BuildContext context) {
    return widget.entry.builder(context);
  }

  @override
  void setState(VoidCallback fn) {
    runImmediatelyOrAfterFrame(() {
      if (mounted) {
        super.setState(fn);
      }
    });
  }

  @override
  void dispose() {
    widget.entry._state = null;
    super.dispose();
  }
}
