// Flutter project by quang<PERSON><PERSON><PERSON> (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';
import 'package:it/it.dart';

import '../../../../component/widget/inherited_consumer.dart';
import '../../../../data/data.dart';
import '../../../../data/model/common/mask_shape.dart';
import '../../../../resource/style/app_theme_ext.dart';
import '../../../../routes/routes.dart';
import '../../../text_input/model/text_input_intial.dart';
import '../../model/events.dart';
import '../../model/floating_bar_enum.dart';
import '../../providers/editor_providers.dart';
import '../../providers/floating_bar_viewmodel.dart';
import '../../providers/pic_element_provider.dart';
import '../editor_screen.dart';
import 'floating_layer_editting.dart';

class EditFloatingBar extends StatefulWidget {
  const EditFloatingBar({super.key});

  @override
  State<EditFloatingBar> createState() => _EditFloatingBarState();
}

class _EditFloatingBarState extends State<EditFloatingBar> with TickerProviderStateMixin {
  late final FloatingBarViewModel floatingBarViewModel = ViewModel.of<FloatingBarViewModel>(context);
  late final PicElementSelection picElementSelection = context.read(picElementSelectionProvider.notifier);
  late final AnimationController _controller = AnimationController(
    value: 1.0,
    duration: kAnimationDuration,
    vsync: this,
  );

  late final AnimationController _opacityController = AnimationController(
    value: 1.0,
    duration: kAnimationDuration,
    vsync: this,
  );

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.observe<CurrentMainFloatingBarEvent>(_onListenerCurrentMainFloatingBarEvent);
      context.observe<CurrentSubFloatingBarEvent>(_onListenerCurrentSubFloatingBarEvent);
    });
  }

  @override
  Widget build(BuildContext context) {
    final main = context.on<CurrentMainFloatingBarEvent>()?.value;
    final sub = context.on<CurrentSubFloatingBarEvent>()?.value;
    if (main?.maintain == true || sub?.maintain == true) {
      if (main == MainFloatingBarEnum.layer) {
        return const FloatingLayerEditting();
      }
      return kBox0;
    }

    final features = main?.features ?? List.from(MainFloatingBarEnum.values);

    final hasElement = context.watch(picTemplateEditorProvider.select((e) => e.value.elements.isNotEmpty));
    if (!hasElement && main == null) {
      features.remove(MainFloatingBarEnum.layer);
    }

    final padding = MediaQuery.paddingOf(context) + kPaddingAll8;
    final theme = Theme.of(context);
    return Positioned(
      top: padding.top,
      right: padding.right,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: features.expandIndexed((index, e) sync* {
          if (index > 0) {
            yield kBox8;
          }
          final feature = features[index];
          final isSelected = (main == feature || sub == feature) && feature.selectable;
          yield Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              FadeTransition(
                opacity: _opacityController,
                child: Text(
                  context.tr(feature.lkey),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w700,
                    color: theme.themeColor.white,
                    shadows: [
                      Shadow(
                        color: theme.themeColor.neutral900.withValues(alpha: .8),
                        offset: Offset.zero,
                        blurRadius: 3,
                      ),
                    ],
                  ),
                ),
              ),
              kBox8,
              IconFloatingButton(
                onPressed: () {
                  if (feature is MainFloatingBarEnum) {
                    floatingBarViewModel.main = feature;
                  } else {
                    floatingBarViewModel.sub = feature;
                  }
                },
                child: ScaleTransition(
                  scale: _controller,
                  child: CoreImage(
                    feature.icon,
                    color: isSelected ? theme.themeColor.primary400 : theme.themeColor.white,
                    width: 26,
                    height: 26,
                  ),
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  @override
  void dispose() {
    _opacityController.dispose();
    _controller.dispose();
    super.dispose();
  }

  void _onListenerCurrentMainFloatingBarEvent(
    CurrentMainFloatingBarEvent? old,
    CurrentMainFloatingBarEvent event,
  ) {
    if (event.value == null) {
      picElementSelection.key = null;
    } else if (picElementSelection.key == null) {
      PicElementType type;
      switch (event.value) {
        case MainFloatingBarEnum.text:
          type = PicElementType.text;
          break;
        case MainFloatingBarEnum.mask:
          type = PicElementType.mask;
          break;
        case MainFloatingBarEnum.sticker:
          type = PicElementType.sticker;
          break;
        default:
          return;
      }
      picElementSelection.key =
          context.read(picTemplateEditorProvider).value.elements.lastWhereOrNull((e) => e.type == type)?.key;
    }

    // nếu không có element type nào thì tự động mở input để user tạo mới
    if (picElementSelection.key == null && floatingBarViewModel.sub == null) {
      switch (event.value) {
        case MainFloatingBarEnum.text:
          floatingBarViewModel.sub = TextFloatingBarEnum.add;
          break;
        case MainFloatingBarEnum.mask:
          floatingBarViewModel.sub = MaskFloatingBarEnum.add;
          break;
        case MainFloatingBarEnum.sticker:
          floatingBarViewModel.sub = StickerFloatingBarEnum.collection;
          break;
        default:
          return;
      }
    }

    _controller
      ..reset()
      ..forward(from: 0.3);
  }

  void _onListenerCurrentSubFloatingBarEvent(CurrentSubFloatingBarEvent? old, CurrentSubFloatingBarEvent event) {
    if (event.value?.selectable == true && _opacityController.status != AnimationStatus.reverse) {
      _opacityController.reverse();
    } else if (old?.value?.selectable == true) {
      _opacityController.forward();
    }

    switch (event.value) {
      case StickerFloatingBarEnum.collection:
        context.pushNamed(Routes.sticker).then((content) {
          context.safety((context) {
            floatingBarViewModel.sub = null;
            if (content is String) {
              context
                  .read(picTemplateEditorProvider.notifier)
                  .addElementSticker(content, boundary: EditorScreen.area(context).size);
            } // nếu không có element sticker nào thì pop menu sticker floating bar
            else if (context.read(picElementSelectionProvider) == null) {
              floatingBarViewModel.main = null;
            }
          });
        });
        break;
      case TextFloatingBarEnum.add:
        context.pushNamed(Routes.textInput).then((params) {
          context.safety((context) {
            floatingBarViewModel.sub = null;
            if (params is TextInputParams) {
              context.read(picTemplateEditorProvider.notifier).addElementText(
                    params.content,
                    metadata: PicElementText.kDefaultMetadata.copyWith(
                      fontEnum: params.style?.fontEnum,
                      color: params.style?.color.toHex(),
                      textAlign: params.style?.textAlign,
                    ),
                    boundary: EditorScreen.area(context).size,
                  );
            } // nếu không có element text nào thì pop menu text floating bar
            else if (context.read(picElementSelectionProvider) == null) {
              floatingBarViewModel.main = null;
            }
          });
        });
        break;
      case TextFloatingBarEnum.background:
        context.pushNamed(Routes.pickImage).then((paths) {
          if (paths is List<String>) {
            context.safety((context) {
              final elementKey = context.read(picElementSelectionProvider);
              if (elementKey == null) return;
              context.read(picElementEditorProvider(elementKey).notifier).changeElementTextBackground(source: paths[0]);
            });
          }
        });
        break;
      case StickerFloatingBarEnum.flip_horizontal:
        _onSelectFlipStickerItem(context.read(picElementSelectionProvider)!, PicFlipEnum.horizontal);
        break;
      case StickerFloatingBarEnum.flip_vertical:
        _onSelectFlipStickerItem(context.read(picElementSelectionProvider)!, PicFlipEnum.vertical);
        break;
      case MaskFloatingBarEnum.add:
        context.pushNamed(Routes.pickImage).then((paths) {
          context.safety((context) {
            floatingBarViewModel.sub = null;
            if (paths is List<String>) {
              context
                  .read(picTemplateEditorProvider.notifier)
                  .addElementMask(paths[0], boundary: EditorScreen.area(context).size);
            }
            // nếu không có element mask nào thì pop menu mask floating bar
            else if (context.read(picElementSelectionProvider) == null) {
              floatingBarViewModel.main = null;
            }
          });
        });
        break;
      case MaskFloatingBarEnum.replace:
        context.pushNamed(Routes.pickImage).then((paths) {
          floatingBarViewModel.sub = null;
          if (paths is List<String>) {
            context.safety((context) {
              final elementKey = context.read(picElementSelectionProvider);
              if (elementKey == null) return;
              context.read(picElementEditorProvider(elementKey).notifier).replaceElementMaskSource(paths[0]);
            });
          }
        });
        break;
      case MaskFloatingBarEnum.shape:
        context.pushNamed(Routes.shape).then((shape) {
          floatingBarViewModel.sub = null;
          if (shape is MaskShape) {
            context.safety((context) {
              final elementKey = context.read(picElementSelectionProvider);
              if (elementKey == null) return;
              context.read(picElementEditorProvider(elementKey).notifier).changeElementMaskShape(shape);
            });
          }
        });
      case TransformFloatingBarEnum.flip_horizontal:
        _onSelectFlipBackgroundItem(context, PicFlipEnum.horizontal);
        break;
      case TransformFloatingBarEnum.flip_vertical:
        _onSelectFlipBackgroundItem(context, PicFlipEnum.vertical);
        break;
      default:
    }
  }

  void _onSelectFlipStickerItem(String elementKey, PicFlipEnum? flipEnum) {
    final element = context.read(picElementEditorProvider(elementKey)) as PicElementSticker;
    if (element.flipEnum != null) {
      flipEnum = element.flipEnum!.replace(flipEnum);
    }

    context.read(picElementEditorProvider(elementKey).notifier).updateElementStickerAttributes(
          flipEnum: flipEnum,
          flipEnumNullable: true,
        );
  }

  void _onSelectFlipBackgroundItem(BuildContext context, PicFlipEnum? flipEnum) {
    final template = context.read(picTemplateEditorProvider).value;
    if (template.flipEnum != null) {
      flipEnum = template.flipEnum!.replace(flipEnum);
    }

    context.read(picTemplateEditorProvider.notifier).updateAttributes(
          flipEnum: flipEnum,
          flipEnumNullable: true,
        );
  }
}

class IconFloatingButton extends StatelessWidget {
  const IconFloatingButton({
    super.key,
    this.child,
    this.icon,
    this.isSelected = false,
    this.onPressed,
  }) : assert(child != null || icon != null, 'child or icon must be provided');

  final Widget? child;
  final String? icon;
  final bool isSelected;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return IconButton(
      onPressed: onPressed,
      padding: EdgeInsets.zero,
      style: IconButton.styleFrom(
        shape: const CircleBorder(),
        highlightColor: Colors.black45,
        shadowColor: theme.themeColor.neutral200.withValues(alpha: .6),
        elevation: .5,
      ),
      icon: child ??
          CoreImage(
            icon,
            color: isSelected ? theme.themeColor.primary400 : theme.themeColor.white,
            width: 26,
            height: 26,
          ),
    );
  }
}
