// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';
import 'package:it/it.dart';

import '../../../../component/widget/inherited_consumer.dart';
import '../../../../resource/style/app_theme_ext.dart';
import '../../model/bottom_bar_enum.dart';
import '../../model/events.dart';
import '../../model/floating_bar_enum.dart';
import '../../providers/bottom_bar_provider.dart';
import 'sub_bottom_bar/blur_sub_bottom_bar.dart';
import 'sub_bottom_bar/mask_filter_bottom_bar.dart';
import 'sub_bottom_bar/mask_stroke_bottom_bar.dart';
import 'sub_bottom_bar/ratio_sub_bottom_bar.dart';
import 'sub_bottom_bar/rotate_sub_bottom_bar.dart';
import 'sub_bottom_bar/sticker_opacity_bottom_bar.dart';
import 'sub_bottom_bar/text_bend_sub_bottom_bar.dart';
import 'sub_bottom_bar/text_gradient_sub_bottom_bar.dart';
import 'sub_bottom_bar/text_opacity_sub_bottom_bar.dart';
import 'sub_bottom_bar/text_shadow_sub_bottom_bar.dart';
import 'sub_bottom_bar/text_stroke_sub_bottom_bar.dart';
import 'sub_bottom_bar/transform_filter_bottom_bar.dart';
import 'sub_bottom_bar/transform_opacity_bottom_bar.dart';

class EditorBottomBar extends StatelessWidget {
  static const double height = 56;
  static const EdgeInsets margin = EdgeInsets.only(bottom: 16, left: 8, right: 8);

  const EditorBottomBar({super.key});

  @override
  Widget build(BuildContext context) {
    final bottomEnum = context.on<CurrentSubFloatingBarEvent>()?.value;
    final theme = Theme.of(context);
    return Container(
      decoration: BoxDecoration(
        color: theme.themeColor.neutral200.withValues(alpha: .2),
      ),
      padding: MediaQuery.paddingOf(context).add(kPaddingVertical12),
      child: switch (bottomEnum) {
        StickerFloatingBarEnum.opacity => const StickerOpacitySubBottomBar(),
        TextFloatingBarEnum.bend => const TextBendSubBottomBar(),
        TextFloatingBarEnum.opacity => const TextOpacitySubBottomBar(),
        TextFloatingBarEnum.shadow => const TextShadowSubBottomBar(),
        TextFloatingBarEnum.stroke => const TextStrokeSubBottomBar(),
        TextFloatingBarEnum.gradient => const TextGradientSubBottomBar(),
        MaskFloatingBarEnum.filter => const MaskFilterBottomBar(),
        MaskFloatingBarEnum.stroke => const MaskStrokeBottomBar(),
        TransformFloatingBarEnum.crop => const RatioSubBottomBar(),
        TransformFloatingBarEnum.rotate => const RotateSubBottomBar(),
        TransformFloatingBarEnum.blur => const BlurSubBottomBar(),
        TransformFloatingBarEnum.opacity => const TransformOpacityBottomBar(),
        TransformFloatingBarEnum.filter => const TransformFilterBottomBar(),
        _ => kBox0,
      },
    );
  }
}

class BottomAction extends StatelessWidget {
  BottomAction({
    super.key,
    required this.item,
    this.icon,
    this.onTap,
    this.width = 64,
    this.automaticallyImplySelection = true,
    this.disable = false,
  }) : assert(item.icon != null || icon != null);

  final BottomItemEnum item;
  final Widget? icon;
  final VoidCallback? onTap;
  final double width;
  final bool automaticallyImplySelection, disable;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final state = context.watch(bottomBarEditorProvider);
    final isSelected = state.bottom?.value == item || state.subBottomEnum == item;

    return InkWell(
      onTap: () {
        if (disable) return;
        if (automaticallyImplySelection) {
          if (item is MainBottomBarItem) {
            context.read(bottomBarEditorProvider.notifier).selectBottom(item as MainBottomBarItem);
          } else if (item is SubBottomBarItem) {
            if ((item as SubBottomBarItem).supportBottomBar) {
              context.read(bottomBarEditorProvider.notifier).selectBottom(item);
            } else {
              context.read(bottomBarEditorProvider.notifier).selectSubBottom(item as SubBottomBarItem);
            }
          }
        }
        onTap?.call();
      },
      child: SizedBox(
        width: width,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            icon ??
                CoreImage(
                  item.icon,
                  color: disable
                      ? theme.themeColor.disabled
                      : isSelected
                          ? theme.themeColor.primary
                          : theme.themeColor.onContainer,
                  width: kEighteen,
                  height: kEighteen,
                ),
            kBox8,
            Text(
              context.tr(item.lkey),
              style: theme.themeText.caption.copyWith(
                color: disable
                    ? theme.themeColor.disabled
                    : isSelected
                        ? theme.themeColor.primary
                        : theme.themeColor.onContainer,
              ),
              maxLines: kOne.toInt(),
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
