// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:it/it.dart';

import '../../../../component/widget/inherited_consumer.dart';
import '../../../../data/data.dart';
import '../../../../resource/icon_constants.dart';
import '../../../../resource/image_constants.dart';
import '../../../../resource/style/app_theme_ext.dart';
import '../../model/bottom_bar_enum.dart';
import '../../model/image_data.dart';
import '../../providers/bottom_bar_provider.dart';
import '../../providers/editor_providers.dart';
import '../../providers/floating_bar_viewmodel.dart';
import '../../providers/pic_element_provider.dart';
import 'mask/element_mask_widget.dart';
import 'sticker/element_sticker_widget.dart';
import 'text/element_text_widget.dart';

class FloatingLayerEditting extends StatefulWidget {
  const FloatingLayerEditting({super.key});

  @override
  State<FloatingLayerEditting> createState() => _FloatingLayerEdittingState();
}

class _FloatingLayerEdittingState extends State<FloatingLayerEditting> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final sized = MediaQuery.sizeOf(context);

    final template = context
        .watch(picTemplateEditorProvider, when: (old, state) => !listEquals(old?.value.elements, state.value.elements))
        .value;

    if (template.elements.isEmpty) return kBox0;

    return Positioned(
      top: sized.height * .1,
      right: 0,
      child: TapRegion(
        onTapOutside: (event) {
          ViewModel.of<FloatingBarViewModel>(context).pop();
        },
        child: Container(
          width: 100,
          height: sized.height * .4,
          padding: kPaddingAll8,
          decoration: BoxDecoration(
            color: theme.themeColor.container,
            border: Border.all(
              color: theme.byBrightness(
                light: theme.themeColor.neutral200,
                dark: theme.themeColor.neutral800,
              ),
            ),
            borderRadius: const BorderRadius.only(
              topLeft: kRadius8,
              bottomLeft: kRadius8,
            ),
          ),
          child: const _ExpandedPannel(),
        ),
      ),
    );
  }
}

class _ExpandedPannel extends StatelessWidget {
  static const EdgeInsets kInsetsHeader = EdgeInsets.only(bottom: 4);

  const _ExpandedPannel();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final template = context
        .watch(picTemplateEditorProvider, when: (old, state) => !listEquals(old?.value.elements, state.value.elements))
        .value;

    final elements = template.elements.reversed;
    return ReorderableListView.builder(
      itemCount: elements.length,
      padding: EdgeInsets.zero,
      header: Container(
        margin: kInsetsHeader,
        padding: kInsetsHeader,
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: theme.themeColor.neutral500,
            ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CoreImage(
              key: const ValueKey('layer_icon'),
              IconConstants.ic_layers,
              color: theme.themeColor.onContainer,
              width: kTwentyFour,
              height: kTwentyFour,
            ),
            Flexible(
              child: Text(
                ' Layers', // hardcode
                maxLines: 1,
                overflow: TextOverflow.fade,
                style: theme.themeText.bodyText1,
              ),
            ),
          ],
        ),
      ),
      proxyDecorator: (child, index, animation) {
        return DecoratedBox(
          decoration: BoxDecoration(
            borderRadius: kBorderRadius12,
            boxShadow: theme.themeDecoration.boxShadow,
          ),
          child: child,
        );
      },
      itemBuilder: (context, index) {
        final element = elements.elementAt(index);
        Widget child;
        if (element is PicElementText) {
          child = ElementTextViewer(
            element: element,
            rect: element.rect.toRect(),
            metadata: element.metadata,
          );
        } else if (element is PicElementMask) {
          child = ElementMaskViewer(
            element: element,
            rect: element.rect.toRect(),
            source: PreloadImageObject.instance.get(element.source),
            providerFilter: picElementEditorProvider(element.key).select((e) => (e as PicElementMask).filter),
          );
        } else if (element is PicElementSticker) {
          child = ElementStickerViewer(
            element: element,
            rect: element.rect.toRect(),
          );
        } else {
          return kBox0;
        }

        return _Item(
          key: ValueKey(element.key),
          element: element,
          child: child,
        );
      },
      onReorder: (oldIndex, newIndex) {
        if (oldIndex < newIndex) {
          newIndex -= 1;
        }

        context.read(picTemplateEditorProvider.notifier).reorderElement(oldIndex, newIndex);
      },
    );
  }
}

class _Item extends StatelessWidget {
  const _Item({
    super.key,
    required this.element,
    required this.child,
  });

  final PicElement element;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bottomEnum = context.read(bottomBarEditorProvider).bottom?.value;
    final enable = (bottomEnum == MainBottomBarItem.mask && element.type == PicElementType.mask) ||
        (bottomEnum == MainBottomBarItem.text && element.type == PicElementType.text);
    final widget = Container(
      alignment: Alignment.center,
      margin: kPaddingVertical4,
      padding: kPaddingAll8,
      decoration: BoxDecoration(
        image: const DecorationImage(
          image: AssetImage(ImageConstants.bg_transparent),
          repeat: ImageRepeat.repeat,
        ),
        borderRadius: kBorderRadius4,
        color: theme.themeColor.neutral100,
      ),
      child: FittedBox(
        fit: BoxFit.cover,
        child: child,
      ),
    );

    if (enable) {
      final selection = context.watch(picElementSelectionProvider);
      final isSelected = selection == element.key;
      final inkWell = GestureDetector(
        onTap: () {
          context.read(picElementSelectionProvider.notifier).key = element.key;
        },
        child: widget,
      );

      if (isSelected) {
        return Container(
          padding: kPaddingAll4,
          decoration: BoxDecoration(
            border: Border.all(color: theme.themeColor.primary),
            borderRadius: kBorderRadius4,
          ),
          child: inkWell,
        );
      }
      return inkWell;
    }

    return widget;
  }
}
