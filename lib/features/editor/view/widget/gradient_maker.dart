// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:math' as math;

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../../component/util/arc_util.dart';
import '../../../../data/data.dart';
import '../../../../resource/localization/lkey.dart';
import '../../../../resource/style/app_theme_ext.dart';
import 'sub_bottom_bar/color_select_bottom_bar.dart';
import 'sub_bottom_bar/slider_field.dart';

class GradientMaker extends StatefulWidget {
  const GradientMaker({
    super.key,
    this.initGradient,
    this.initRotation,
    required this.onChanged,
    this.enableRotation = false,
  });

  final LinearGradient? initGradient;
  final double? initRotation;
  final ValueChanged<LinearGradient> onChanged;
  final bool enableRotation;

  @override
  State<GradientMaker> createState() => _GradientMakerState();
}

class _GradientMakerState extends State<GradientMaker> {
  late LinearGradient _gradient = widget.initGradient ?? PicGradient.blackAndWhite.toLinearGradient(false);
  late double _rotation = widget.initRotation ?? 0.0;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Padding(
      padding: kPaddingHorizontal16,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          kBox8,
          GradienrSlider(
            value: _gradient,
            labelStyle: theme.themeText.caption,
            labelBackgroundColor: theme.themeColor.container,
            onChanged: (value) {
              _gradient = value;
              widget.onChanged.call(_gradient);
            },
          ),
          kBox16,
          _ColorsPicker(
            colors: _gradient.colors,
            onSelect: (color) {
              double? max = _gradient.stops?.last;
              if (max != null) {
                if (max + 0.1 > 1) {
                  for (var i = 0; i < _gradient.stops!.length; i++) {
                    final stop = _gradient.stops![i];
                    if (stop - 0.1 <= 0) continue;
                    _gradient.stops![i] = stop - 0.1;
                  }
                } else {
                  max += 0.1;
                }
                setState(() {
                  _gradient = LinearGradient(
                    colors: [..._gradient.colors, color],
                    stops: [..._gradient.stops!, max!],
                  );
                });
              } else {
                setState(() {
                  _gradient = LinearGradient(
                    colors: [..._gradient.colors, color],
                    stops: List.of([0.0, 1.0]),
                  );
                });
              }
              widget.onChanged.call(_gradient);
            },
            onRemove: (color) {
              final indexed = _gradient.colors.indexWhere((e) => e == color);
              if (indexed == -1) return;

              setState(() {
                _gradient = LinearGradient(
                  colors: List.from(_gradient.colors)..removeAt(indexed),
                  stops: List.from(_gradient.stops!)..removeAt(indexed),
                );
                widget.onChanged.call(_gradient);
              });
            },
          ),
          kBox24,
          SliderField(
            value: _rotation,
            min: -angle180,
            max: angle180,
            centerThumb: true,
            title: context.tr(LKey.common_rotation),
            suffix: SuffixField(
              value: MathUtil.radians2Degrees(_rotation).toStringAsFixed(1),
            ),
            onChanged: widget.enableRotation
                ? (value) {
                    setState(() {
                      _rotation = value;
                      _gradient = rotate(_gradient, _rotation);
                      widget.onChanged.call(_gradient);
                    });
                  }
                : null,
          ),
          kBox8,
        ],
      ),
    );
  }

  LinearGradient rotate(LinearGradient gradient, double rotation) {
    return LinearGradient(
      colors: gradient.colors,
      stops: gradient.stops,
      transform: GradientRotation(rotation),
    );
  }
}

class GradienrSlider extends StatefulWidget {
  const GradienrSlider({
    super.key,
    required this.value,
    required this.onChanged,
    this.thumbSize = 24,
    this.thicknessRatio = .3,
    this.labelStyle,
    this.labelBackgroundColor,
  });

  final LinearGradient value;
  final ValueChanged<LinearGradient> onChanged;

  final double thumbSize;
  final double thicknessRatio;
  final TextStyle? labelStyle;
  final Color? labelBackgroundColor;

  @override
  State<GradienrSlider> createState() => _GradienrSliderState();
}

class _GradienrSliderState extends State<GradienrSlider> {
  late final ValueNotifier<LinearGradient> _notifier = ValueNotifier(widget.value);
  late final ValueNotifier<int> _selectedIndex = ValueNotifier(-1);

  @override
  void didUpdateWidget(covariant GradienrSlider oldWidget) {
    super.didUpdateWidget(oldWidget);

    _notifier.value = widget.value;
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (_, constraints) {
        return GestureDetector(
          onHorizontalDragStart: (details) {
            if (_selectedIndex.value >= 0) return;

            final gradient = _notifier.value;
            for (var i = gradient.colors.length - 1; i >= 0; i--) {
              final stop = gradient.stops![i];
              final delta = details.localPosition - Offset(constraints.maxWidth * stop, widget.thumbSize / 2);
              if (delta.distance < 36) {
                _selectedIndex.value = i;
                break;
              }
            }
          },
          onHorizontalDragUpdate: (details) {
            if (_selectedIndex.value < 0) return;

            final gradient = _notifier.value;
            final stops = List<double>.from(gradient.stops!);
            final colors = List<Color>.from(gradient.colors);

            final delta = math.max(
              .0,
              math.min(
                1.0,
                stops[_selectedIndex.value] + details.delta.dx / constraints.maxWidth,
              ),
            );
            stops[_selectedIndex.value] = delta;

            int swapped = -1;
            Color temp;
            for (var i = 0; i < stops.length; i++) {
              final stop = stops[i];
              for (var j = i + 1; j < stops.length; j++) {
                if (stop > stops[j]) {
                  stops[i] = stops[j];
                  stops[j] = stop;

                  temp = colors[i];
                  colors[i] = colors[j];
                  colors[j] = temp;
                  if (details.delta.dx.isNegative) {
                    swapped = i;
                  } else {
                    swapped = j;
                  }
                }
              }
              if (swapped >= 0 && _selectedIndex.value == i) {
                _selectedIndex.value = swapped;
              }
            }

            _notifier.value = copyWith(
              stops: stops,
              colors: colors,
            );

            widget.onChanged.call(_notifier.value);
          },
          onHorizontalDragCancel: _onSubmit,
          onHorizontalDragEnd: _onSubmit,
          child: CustomPaint(
            size: Size(
              constraints.maxWidth,
              widget.thumbSize + widget.thumbSize * widget.thicknessRatio + _Painter.blurWidth * 2,
            ),
            isComplex: true,
            painter: _Painter(
              repaint: Listenable.merge([_notifier, _selectedIndex]),
              gradient: _notifier,
              indexed: _selectedIndex,
              thickness: widget.thicknessRatio,
              thumbSize: widget.thumbSize,
              labelStyle: widget.labelStyle,
              labelBackgroundColor: widget.labelBackgroundColor,
            ),
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _notifier.dispose();
    _selectedIndex.dispose();
    super.dispose();
  }

  void _onSubmit([value]) {
    _selectedIndex.value = -1;
    widget.onChanged.call(_notifier.value);
  }

  LinearGradient copyWith({
    List<Color>? colors,
    List<double>? stops,
  }) {
    return LinearGradient(
      colors: colors ?? _notifier.value.colors,
      stops: stops ?? _notifier.value.stops,
    );
  }
}

class _Painter extends CustomPainter {
  static final TextPainter textPainter = TextPainter(textDirection: TextDirection.ltr);
  static final blurWidth = Shadow.convertRadiusToSigma(6);

  final ValueNotifier<LinearGradient> gradient;
  final ValueNotifier<int> indexed;

  final double thumbSize;
  final double thickness;
  final TextStyle? labelStyle;
  final Color? labelBackgroundColor;

  _Painter({
    required super.repaint,
    required this.gradient,
    required this.indexed,
    required this.thumbSize,
    required this.thickness,
    this.labelStyle,
    this.labelBackgroundColor,
  });

  RRect _rRect = RRect.zero;

  @override
  void paint(Canvas canvas, Size size) {
    _rRect = RRect.fromRectAndRadius(
      Rect.fromCenter(
        center: size.center(Offset.zero),
        width: size.width - thumbSize / 2,
        height: size.height * thickness,
      ),
      Radius.circular(size.height * thickness / 2),
    );

    final colors = gradient.value.colors;
    final paint = Paint()
      ..shader = LinearGradient(
        colors: colors,
        stops: gradient.value.stops,
      ).createShader(_rRect.middleRect)
      ..style = PaintingStyle.fill;

    canvas
      ..drawRRect(_rRect, paint)
      ..drawRRect(
        _rRect,
        paint
          ..color = Colors.grey.shade200
          ..shader = null
          ..strokeWidth = kOne
          ..style = PaintingStyle.stroke,
      );

    for (var i = 0; i < colors.length; i++) {
      if (indexed.value == i) continue;
      final color = colors[i];
      final stop = gradient.value.stops![i];
      final center = Offset(_rRect.width * stop, size.height / 2);
      _paintThumb(canvas, center: center, color: color);
    }

    if (indexed.value >= 0) {
      final color = colors[indexed.value];
      final stop = gradient.value.stops![indexed.value];
      final center = Offset(_rRect.width * stop, size.height / 2);

      textPainter
        ..text = TextSpan(
          text: stop.toStringAsFixed(2),
          style: labelStyle ?? const TextStyle(color: Colors.black54, fontSize: 8),
        )
        ..layout();

      _rRect = RRect.fromRectAndRadius(
        Rect.fromCenter(
          center: center - Offset(0, thumbSize + textPainter.height + kFour),
          width: textPainter.width + kSixteen, // padding horizontal
          height: textPainter.height + kEight, // padding vertical
        ),
        Radius.circular((textPainter.height + kEight) / 2),
      );

      canvas
        ..drawRRect(
          _rRect,
          Paint()
            ..color = Colors.black12
            ..maskFilter = MaskFilter.blur(BlurStyle.normal, _Painter.blurWidth),
        )
        ..drawRRect(_rRect, Paint()..color = labelBackgroundColor ?? Colors.white);

      textPainter.paint(
        canvas,
        _rRect.center - Offset(textPainter.width / 2, textPainter.height / 2),
      );

      _paintThumb(canvas, center: center, color: color);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }

  void _paintThumb(
    Canvas canvas, {
    required Offset center,
    required Color color,
  }) {
    final Paint paint = Paint();
    final radius = thumbSize * thickness;
    canvas
      ..drawCircle(
        center,
        radius * 2,
        paint
          ..color = Colors.black12
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, _Painter.blurWidth),
      )
      ..drawCircle(
        center,
        radius * 1.5,
        paint
          ..color = Colors.white
          ..maskFilter = null,
      )
      ..drawCircle(
        center,
        radius,
        paint..color = color,
      );

    if (color == Colors.white) {
      canvas.drawCircle(
        center,
        radius,
        paint
          ..color = Colors.grey
          ..isAntiAlias = true
          ..strokeWidth = 1.5
          ..style = PaintingStyle.stroke,
      );
    }
  }
}

class _ColorsPicker extends StatelessWidget {
  static const double itemVisiableMobile = 10.5, itemVisiableTablet = 17.5;

  final List<Color> colors;
  final ValueChanged<Color> onSelect, onRemove;

  const _ColorsPicker({
    required this.colors,
    required this.onSelect,
    required this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: kThirtyTwo,
      child: LayoutBuilder(
        builder: (_, constraints) {
          final itemVisiable = isTablet(context) ? itemVisiableTablet : itemVisiableMobile;
          final itemWidth = (constraints.maxWidth - (kFour * (itemVisiable.toInt()))) / itemVisiable;
          return ListView.separated(
            itemCount: ColorSelectBottomBar.kColors.length,
            scrollDirection: Axis.horizontal,
            itemBuilder: (context, index) {
              final color = ColorSelectBottomBar.kColors[index];
              final isSelected = colors.contains(color);
              return InkWell(
                onTap: () {
                  if (isSelected) {
                    if (colors.length == 2) return;
                    onRemove(color);
                  } else {
                    if (colors.length == 7) return;
                    onSelect(color);
                  }
                },
                child: Container(
                  width: itemWidth,
                  height: kThirtyTwo,
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                    border: isSelected
                        ? Border.all(
                            color: ThemeData.estimateBrightnessForColor(color) == Brightness.dark
                                ? Colors.white54
                                : Colors.black45,
                            width: 3,
                          )
                        : null,
                  ),
                ),
              );
            },
            separatorBuilder: (context, index) => kBox4,
          );
        },
      ),
    );
  }
}
