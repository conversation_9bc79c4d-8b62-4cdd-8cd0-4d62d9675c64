// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../../component/widget/inherited_consumer.dart';
import '../../../../resource/style/app_theme_ext.dart';
import '../../mixin/bottom_action_mixin.dart';
import '../../model/bottom_bar_enum.dart';
import '../../providers/bottom_bar_provider.dart';
import 'sub_bottom_bar/mask_sub_bottom_bar.dart';
import 'sub_bottom_bar/sticker_sub_bottom_bar.dart';
import 'sub_bottom_bar/text_sub_bottom_bar.dart';
import 'sub_bottom_bar/transform_sub_bottom_bar.dart';

class MainBottomBar extends StatefulWidget {
  const MainBottomBar({super.key, required this.item});

  final MainBottomBarItem item;

  @override
  State<MainBottomBar> createState() => _MainBottomBarState();
}

class _MainBottomBarState extends State<MainBottomBar> with StateBottomActionMixin {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    Widget optWidget;
    switch (widget.item) {
      case MainBottomBarItem.transform:
        optWidget = const TransformSubBottomBar();
        break;
      case MainBottomBarItem.text:
        optWidget = const TextSubBottomBar();
        break;
      case MainBottomBarItem.sticker:
        optWidget = const StickerSubBottomBar();
        break;
      case MainBottomBarItem.mask:
        optWidget = const MaskSubBottomBar();
        break;
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ColoredBox(
          color: theme.themeColor.container,
          child: optWidget,
        ),
        const _ActionBar(),
      ],
    );
  }
}

class _ActionBar extends StatelessWidget {
  const _ActionBar();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final state = context.watch(bottomBarEditorProvider);
    return Container(
      height: kThirtyTwo,
      padding: kPaddingHorizontal4,
      decoration: BoxDecoration(
        color: theme.themeColor.surface,
        border: Border(top: BorderSide(color: theme.themeColor.neutral100)),
      ),
      child: Row(
        children: [
          IconButton(
            padding: EdgeInsets.zero,
            onPressed: state.action?.value.onPopState,
            iconSize: kEighteen,
            icon: Icon(
              Icons.close_rounded,
              color: theme.themeColor.onSurface,
            ),
          ),
          Expanded(
            child: Text(
              context.tr(state.bottom?.value.lkey ?? ''),
              textAlign: TextAlign.center,
              maxLines: kOne.toInt(),
              overflow: TextOverflow.ellipsis,
              style: theme.themeText.bodyText1.copyWith(
                color: theme.themeColor.onSurface,
              ),
            ),
          ),
          IconButton(
            padding: EdgeInsets.zero,
            onPressed: state.action?.value.onSubmitState,
            iconSize: kEighteen,
            icon: Icon(
              Icons.check_rounded,
              color: theme.themeColor.onSurface,
            ),
          ),
        ],
      ),
    );
  }
}
