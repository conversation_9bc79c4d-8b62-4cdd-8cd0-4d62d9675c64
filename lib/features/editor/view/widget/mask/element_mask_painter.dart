// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../../../data/data.dart';
import '../../../../../data/model/common/mask_shape.dart';
import '../../../model/preload_mask_path.dart';
import '../../editor_constant.dart';

class ElementMaskPainter extends CustomPainter {
  final ui.Image source;
  final MaskShape shape;
  final Float64List transform;
  final PicImgFilter? uniForms;
  final Rect _inputRect;

  ElementMaskPainter({
    required this.shape,
    required this.source,
    required this.transform,
    required this.uniForms,
  }) : _inputRect = Offset.zero & source.size;

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint();

    final Path _path = PreloadPathsMaskShape.instance
        .getCombine(shape.asset)
        .transform(Matrix4.diagonal3Values(size.width / shape.width, size.height / shape.height, 1.0).storage);

    if (shape.stroke?.hasStroke == true) {
      final stroke = shape.stroke!;
      final hasTranslate = stroke.translate != PicPosition.zero;
      if (hasTranslate) {
        canvas
          ..save()
          ..translate(
            stroke.translate.x * size.width,
            stroke.translate.y * size.height,
          );
      }

      canvas.drawPath(
        _path,
        paint
          ..style = ui.PaintingStyle.stroke
          ..strokeWidth = stroke.width * size.width
          ..color = stroke.color.toColor(),
      );

      if (hasTranslate) {
        canvas.restore();
      }
    }

    canvas
      ..save()
      ..clipPath(_path)
      ..transform(transform);

    if (uniForms != null) {
      canvas.drawRect(_inputRect, paint..blendMode = ui.BlendMode.srcOver);

      paint
        ..shader = imageFilterFragment.createShader(source, uniForms!)
        ..isAntiAlias = true;
      canvas.drawPaint(paint);
    } else {
      canvas.drawImage(source, Offset.zero, paint..blendMode = BlendMode.srcOver);
    }

    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
