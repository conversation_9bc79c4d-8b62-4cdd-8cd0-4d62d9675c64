// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:typed_data';

import 'package:core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../data/data.dart';
import '../../../../../routes/routes.dart';
import '../../../../interactive/model/interactive_args.dart';
import '../../../model/image_data.dart';
import '../../../providers/editor_providers.dart';
import '../../../providers/pic_element_provider.dart';
import '../../editor_screen.dart';
import '../draggable_widget.dart';
import 'element_mask_painter.dart';

class ElementMaskWidget extends StatelessWidget {
  const ElementMaskWidget({
    super.key,
    required this.elementKey,
    this.disable = false,
  });

  final String elementKey;
  final bool disable;

  @override
  Widget build(BuildContext context) {
    final picTemplateEditor = context.read(picTemplateEditorProvider.notifier);
    final provider = picElementEditorProvider(elementKey);
    final elementEditor = context.read(provider.notifier);
    final element = context.watch(provider) as PicElementMask;
    final isSelected = context.watch(picElementSelectionProvider) == element.key;
    final source = PreloadImageObject.instance.get(element.source);
    final filter = provider.select((e) => (e as PicElementMask).filter);
    final areaKey = EditorScreen.area(context);
    return DraggableWidget(
      rect: element.rect.toRect(),
      rotation: element.rotation,
      isSelected: isSelected,
      disable: disable,
      onUpdateRect: (rect, shift, scale) {
        rect = rect.shift(shift);
        if (scale != 1.0) {
          final sized = rect.size * scale;
          if (sized.shortestSide >= kTwentyFour && sized.shortestSide < areaKey.size.shortestSide) {
            rect = Rect.fromCenter(center: rect.center, width: sized.width, height: sized.height);
          }
        }
        return rect;
      },
      onTap: () {
        context.read(picElementSelectionProvider.notifier).key = element.key;
      },
      onDoubleTap: () {
        if (!isSelected) {
          context.read(picElementSelectionProvider.notifier).key = element.key;
          return;
        }

        context
            .pushNamed(
          Routes.interativeMask,
          arguments: MaskInteractiveArgs(
            source: source.image,
            uniForms: element.filter,
            shape: element.shape,
            metadata: element.metadata,
          ),
        )
            .then((value) {
          if (value is MaskMetadata) {
            elementEditor.updateElementMaskAttributes(
              metadata: value,
            );
          }
        });
      },
      onDragEnd: (rect, rotation) {
        elementEditor.updateElementMaskAttributes(
          rect: PicRect.fromRect(rect),
          rotation: rotation,
        );
      },
      onDuplicate: () {
        picTemplateEditor.duplicateElement(element.key);
      },
      onDelete: () {
        picTemplateEditor.deleteElement(element.key);
      },
      builder: (rect, rotation, _) {
        return ElementMaskViewer(
          element: element,
          source: source,
          rect: rect,
          providerFilter: filter,
        );
      },
    );
  }
}

class ElementMaskViewer extends StatelessWidget {
  const ElementMaskViewer({
    super.key,
    required this.element,
    required this.source,
    required this.rect,
    required this.providerFilter,
  });

  final PicElementMask element;
  final ImageData source;
  final Rect rect;
  final ProviderListenable<PicImgFilter?> providerFilter;

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: rect.size,
      painter: ElementMaskPainter(
        source: source.image,
        shape: element.shape,
        transform: _caculateMaskMatrix(source.size, rect.size, element.metadata),
        uniForms: context.watch(providerFilter),
      ),
    );
  }

  Float64List _caculateMaskMatrix(
    Size inputSize,
    Size outputSize,
    MaskMetadata? metadata,
  ) {
    Matrix4 _matrix = Matrix4.identity();
    if (inputSize.shortestSide == inputSize.width) {
      final x = outputSize.width / inputSize.width;
      _matrix
        ..translate(outputSize.width / 2, outputSize.height / 2)
        ..scale(x, x, 1.0)
        ..translate(-inputSize.width / 2, -inputSize.height / 2);
    } else {
      final y = outputSize.height / inputSize.height;
      _matrix
        ..translate(outputSize.width / 2, outputSize.height / 2)
        ..scale(y, y, 1.0)
        ..translate(-inputSize.width / 2, -inputSize.height / 2);
    }

    if (metadata != null) {
      _matrix *= metadata.toMatrix4(inputSize.center(Offset.zero));
    }

    return _matrix.storage;
  }
}
