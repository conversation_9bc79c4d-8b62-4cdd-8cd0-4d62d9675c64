// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/material.dart';

import '../../../../component/widget/inherited_consumer.dart';
import '../../providers/pic_element_provider.dart';

abstract class PicElementSelectionBuilder extends StatelessWidget {
  const PicElementSelectionBuilder({super.key});

  @override
  Widget build(BuildContext context) {
    final selectionKey = context.watch(picElementSelectionProvider);
    if (selectionKey == null) {
      return const Text('no selection');
    }

    return builder(context, picElementEditorProvider(selectionKey));
  }

  Widget builder(BuildContext context, PicElementEditorProvider provider);
}
