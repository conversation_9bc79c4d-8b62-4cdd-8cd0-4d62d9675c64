// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:ui' as ui;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../../../data/data.dart';
import '../../funcs/rendering.dart';
import '../../model/image_filter_fragment.dart';
import '../editor_constant.dart';

class PicImage extends StatelessWidget {
  static void drawImage(
    final Canvas canvas, {
    required final ui.Image image,
    required final Float64List transform,
    UniForms? uniForms,
    Paint? paint,
  }) {
    canvas
      ..save()
      ..transform(transform);

    final _paint = paint ?? Paint();
    if (uniForms != null) {
      canvas.drawPaint(
        _paint
          ..shader = imageFilterFragment.createShader(image, uniForms)
          ..isAntiAlias = true
          ..filterQuality = FilterQuality.high,
      );

      _paint
        ..shader = null
        ..imageFilter = null;
    } else {
      canvas.drawImage(image, Offset.zero, _paint);
    }

    canvas.restore();
  }

  PicImage({
    super.key,
    required this.image,
    this.rotation,
    this.flipEnum,
    this.fit = BoxFit.contain,
    this.filter,
    this.outputSize,
    this.transform,
  }) : _sourceSize = Size(image.width.toDouble(), image.height.toDouble());

  final ui.Image image;
  final double? rotation;
  final BoxFit fit;
  final PicFlipEnum? flipEnum;
  final PicImgFilter? filter;

  final Float64List? transform;
  final Size? outputSize;

  final Size _sourceSize;

  factory PicImage.fromTemplate(
    final PicEditingTemplate template, {
    BoxFit? fit,
    Size? outputSize,
    Float64List? transform,
  }) {
    return PicImage(
      image: template.image,
      rotation: template.rotation,
      flipEnum: template.flipEnum,
      filter: template.filter,
      fit: fit ?? BoxFit.contain,
      outputSize: outputSize,
      transform: transform,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Tính kích thước hiển thị image sau khi scale theo kích thước view
    final fittedSize = caculateFittedSize(_sourceSize, outputSize ?? MediaQuery.sizeOf(context), fit: fit);

    return CustomPaint(
      size: fittedSize,
      painter: _Painter(
        image: image,
        uniForms: filter != PicImgFilter.none ? filter : null,
        transform: transform ??
            caculateImageMatrix(
              _sourceSize,
              fittedSize,
              rotation: rotation,
              flipEnum: flipEnum,
            ).storage,
      ),
    );
  }
}

class _Painter extends CustomPainter {
  final ui.Image image;
  final Float64List transform;
  final UniForms? uniForms;

  _Painter({
    required this.image,
    required this.transform,
    this.uniForms,
  });

  @override
  void paint(Canvas canvas, Size size) {
    canvas
      ..save()
      ..clipRect(Offset.zero & size);
    PicImage.drawImage(
      canvas,
      transform: transform,
      image: image,
      uniForms: uniForms,
    );
    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
