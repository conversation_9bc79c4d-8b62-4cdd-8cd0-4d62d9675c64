// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:core/common_const.dart';
import 'package:flutter/material.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../data/data.dart';
import '../../../funcs/rendering.dart';
import '../../../model/image_data.dart';
import '../../../providers/editor_providers.dart';
import '../../../providers/pic_element_provider.dart';
import '../../editor_screen.dart';
import '../draggable_widget.dart';
import '../pic_image.dart';

class ElementStickerWidget extends StatelessWidget {
  const ElementStickerWidget({
    super.key,
    required this.elementKey,
    this.disable = false,
  });

  final String elementKey;
  final bool disable;

  @override
  Widget build(BuildContext context) {
    final provider = picElementEditorProvider(elementKey);
    final elementEditor = context.read(provider.notifier);
    final element = context.watch(provider) as PicElementSticker;
    final isSelected = context.watch(picElementSelectionProvider) == element.key;
    final areaKey = EditorScreen.area(context);
    return DraggableWidget(
      rect: element.rect.toRect(),
      rotation: element.rotation,
      isSelected: isSelected,
      disable: disable,
      onUpdateRect: (rect, shift, scale) {
        rect = rect.shift(shift);
        if (scale != 1.0) {
          final sized = rect.size * scale;
          if (sized.shortestSide >= kTwentyFour && sized.longestSide < areaKey.size.shortestSide) {
            rect = Rect.fromCenter(center: rect.center, width: sized.width, height: sized.height);
          }
        }
        return rect;
      },
      onDragEnd: (rect, rotation) {
        elementEditor.updateElementStickerAttributes(
          rect: PicRect.fromRect(rect),
          rotation: rotation,
        );
      },
      onTap: () {
        context.read(picElementSelectionProvider.notifier).key = element.key;
      },
      onDuplicate: () {
        context.read(picTemplateEditorProvider.notifier).duplicateElement(element.key);
      },
      onDelete: () {
        context.read(picTemplateEditorProvider.notifier).deleteElement(element.key);
      },
      builder: (rect, _, __) {
        return ElementStickerViewer(
          element: element,
          rect: rect,
        );
      },
    );
  }
}

class ElementStickerViewer extends StatelessWidget {
  const ElementStickerViewer({
    super.key,
    required this.element,
    required this.rect,
  });

  final PicElementSticker element;
  final Rect rect;

  @override
  Widget build(BuildContext context) {
    final imageData = PreloadImageObject.instance.get(element.source);
    final fittedSize = caculateFittedSize(imageData.size, rect.size, fit: BoxFit.contain);

    final child = CustomPaint(
      size: fittedSize,
      painter: _StickerPainter(
        image: imageData.image,
        transform: caculateImageMatrix(
          imageData.size,
          fittedSize,
          flipEnum: element.flipEnum,
        ).storage,
      ),
    );

    if (element.opacity < 1) {
      return Opacity(
        opacity: element.opacity,
        child: child,
      );
    }

    return child;
  }
}

class _StickerPainter extends CustomPainter {
  final ui.Image image;
  final Float64List transform;

  _StickerPainter({
    required this.image,
    required this.transform,
  });

  @override
  void paint(Canvas canvas, Size size) {
    canvas
      ..save()
      ..clipRect(Offset.zero & size);

    PicImage.drawImage(
      canvas,
      image: image,
      transform: transform,
    );

    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
