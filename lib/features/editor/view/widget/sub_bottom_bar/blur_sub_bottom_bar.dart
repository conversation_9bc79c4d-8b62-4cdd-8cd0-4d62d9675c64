// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../data/model/template/pic_blur.dart';
import '../../../../../resource/localization/lkey.dart';
import '../../../../../resource/style/app_theme_ext.dart';
import '../../../providers/editor_providers.dart';
import 'slider_field.dart';

class BlurSubBottomBar extends StatelessWidget {
  static const double height = 56;

  const BlurSubBottomBar({super.key});

  @override
  Widget build(BuildContext context) {
    final picTemplateEditor = context.read(picTemplateEditorProvider.notifier);
    final blurProvider = picTemplateEditorProvider.select((e) => e.value.blur ?? PicBlur.none);
    return SizedBox(
      height: height,
      child: Row(
        children: [
          kBox16,
          Expanded(
            child: Builder(
              builder: (context) {
                final blur = context.watch(blurProvider);
                return _BlurField(
                  title: context.tr(LKey.blur_sigma_x),
                  value: blur.sigmaX,
                  onChanged: (value) {
                    picTemplateEditor.updateAttributes(
                      blur: blur.copyWith(sigmaX: value),
                    );
                  },
                  onChangeEnd: (value) {
                    picTemplateEditor.updateAttributes(
                      blur: blur.copyWith(sigmaX: value),
                    );
                  },
                );
              },
            ),
          ),
          kBox24,
          Expanded(
            child: Builder(
              builder: (context) {
                final blur = context.watch(blurProvider);
                return _BlurField(
                  title: context.tr(LKey.blur_sigma_y),
                  value: blur.sigmaY,
                  onChanged: (value) {
                    picTemplateEditor.updateAttributes(
                      blur: blur.copyWith(sigmaY: value),
                    );
                  },
                  onChangeEnd: (value) {
                    picTemplateEditor.updateAttributes(
                      blur: blur.copyWith(sigmaY: value),
                    );
                  },
                );
              },
            ),
          ),
          kBox16,
        ],
      ),
    );
  }
}

class _BlurField extends StatelessWidget {
  static const double max = 100;
  static const double min = 0;

  const _BlurField({
    required this.title,
    required this.value,
    this.onChanged,
    this.onChangeEnd,
  });

  final String title;
  final double value;
  final ValueChanged<double>? onChanged, onChangeEnd;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Row(
          children: [
            Text(
              title,
              style: theme.themeText.bodyText2,
            ),
            const Spacer(),
            SuffixField(value: value.toStringAsFixed(1)),
          ],
        ),
        SizedBox(
          height: kTwentyFour,
          child: SliderField(
            max: max,
            min: min,
            value: value,
            onChanged: onChanged,
            onChangedEnd: onChangeEnd,
          ),
        ),
      ],
    );
  }
}
