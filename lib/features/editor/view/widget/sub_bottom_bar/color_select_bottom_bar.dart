// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../../../resource/style/app_theme_ext.dart';
import '../../../../../resource/style/color/app_theme_color.dart';

class ColorSelectBottomBar extends StatefulWidget {
  static const double kHeight = 80.0;
  static const double itemVisiableMobile = 7.5;
  static const double itemVisiableTablet = 14.5;

  static const List<Color> kColors = [
    Color(0xff000000),
    Color(0xff424242),
    Color(0xff757575),
    Color(0xffbdbdbd),
    Color(0xffeeeeee),
    Color(0xffffffff),
    Color(0xff546e7a),
    Color(0xff90a4ae),
    Color(0xffcfd8dc),
    Color(0xffffecb3),
    Color(0xfffff9c4),
    Color(0xfff1f8e9),
    Color(0xffe3f2fd),
    Color(0xffede7f6),
    Color(0xfffce4ec),
    Color(0xfffbe9e7),
    Color(0xffe97deb),
    Color(0xff4b369d),
    Color(0xff70369d),
    Color(0xffe91e63),
    Color(0xffe81416),
    Color(0xfff44336),
    Color(0xfff57f17),
    Color(0xffffa500),
    Color(0xfff4b400),
    Color(0xfffff59d),
    Color(0xffffeb3b),
    Color(0xfffaeb36),
    Color(0xff3e2723),
    Color(0xff5d4037),
    Color(0xffa1887f),
    Color(0xffd7ccc8),
    Color(0xff263238),
    Color(0xff004d40),
    Color(0xff006064),
    Color(0xff009688),
    Color(0xff79c314),
    Color(0xff80deea),
    Color(0xffa1c2fa),
    Color(0xff01579b),
    Color(0xff487de7),
    Color(0xff0200fc),
    Color(0xff1a237e),
  ];

  const ColorSelectBottomBar({
    super.key,
    this.height = ColorSelectBottomBar.kHeight,
    this.borderWidth = kSix,
    this.shape = BoxShape.rectangle,
    this.initialColor,
    this.colors = kColors,
    required this.onSelect,
  });

  final double height, borderWidth;
  final BoxShape shape;
  final Color? initialColor;
  final List<Color> colors;
  final ValueChanged<Color> onSelect;

  @override
  State<ColorSelectBottomBar> createState() => _ColorSelectBottomBarState();
}

class _ColorSelectBottomBarState extends State<ColorSelectBottomBar> {
  late final ValueNotifier<Color?> _valueNotifier = ValueNotifier(widget.initialColor);

  @override
  void didUpdateWidget(covariant ColorSelectBottomBar oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.initialColor != widget.initialColor) {
      _valueNotifier.value = widget.initialColor;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      height: widget.height,
      alignment: Alignment.bottomCenter,
      child: LayoutBuilder(
        builder: (context, constraints) {
          final itemVisiable =
              isTablet(context) ? ColorSelectBottomBar.itemVisiableTablet : ColorSelectBottomBar.itemVisiableMobile;
          final itemWidth = constraints.maxWidth / itemVisiable;

          return ListView.builder(
            itemCount: widget.colors.length,
            scrollDirection: Axis.horizontal,
            itemBuilder: (context, index) {
              final color = widget.colors[index];
              final isSelected = context.listenable(_valueNotifier).value == color;

              return InkWell(
                onTap: () {
                  if (_valueNotifier.value == color) return;
                  _valueNotifier.value = color;
                  widget.onSelect(color);
                },
                child: DecoratedBox(
                  decoration: BoxDecoration(
                    color: color,
                    shape: widget.shape,
                    border: widget.shape == BoxShape.rectangle
                        ? (isSelected)
                            ? Border(
                                bottom: BorderSide(
                                  width: widget.borderWidth,
                                  color: highlightColor(index, theme.themeColor),
                                ),
                              )
                            : null
                        : Border.all(
                            width: isSelected ? widget.borderWidth : 1,
                            color: isSelected ? highlightColor(index, theme.themeColor) : theme.themeColor.disabled,
                          ),
                  ),
                  child: SizedBox(
                    width: itemWidth,
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _valueNotifier.dispose();
    super.dispose();
  }

  Color highlightColor(int index, AppThemeColor theme) {
    switch (index) {
      case 4:
      case 5:
      case >= 9 && <= 15:
      case >= 25 && <= 27:
        return theme.neutral800;
      default:
        return theme.neutral100;
    }
  }
}
