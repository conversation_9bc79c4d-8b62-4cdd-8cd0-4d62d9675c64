// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../../../component/widget/slider_marked.dart';
import '../../../../../data/data.dart';
import '../../../../../resource/style/app_theme_ext.dart';
import '../../../model/bottom_bar_enum.dart';

class FilterSubBottomBar extends StatefulWidget {
  static const double optionHeight = 56;
  static const double itemVisiableMobile = 6.5;
  static const double itemVisiableTablet = 10.5;

  const FilterSubBottomBar({
    super.key,
    required this.initialValue,
    this.onChangeEnd,
    this.onChanged,
  });

  final PicImgFilter initialValue;
  final ValueChanged<PicImgFilter>? onChanged, onChangeEnd;

  @override
  State<FilterSubBottomBar> createState() => _FilterSubBottomBarState();
}

class _FilterSubBottomBarState extends State<FilterSubBottomBar> {
  FilterBottomBarItem state = FilterBottomBarItem.brightness;

  double value = 0, max = 1, min = 0;
  int valueStringAsFixed = 1, divisionStringAsFixed = 2;
  late PicImgFilter filter = widget.initialValue;

  @override
  void initState() {
    super.initState();

    max = state.max;
    min = state.min;
    valueStringAsFixed = state.fractionDigits;
    divisionStringAsFixed = valueStringAsFixed - 1;
  }

  @override
  void didUpdateWidget(covariant FilterSubBottomBar oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.initialValue != oldWidget.initialValue) {
      filter = widget.initialValue;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final valueTextStyle = theme.themeText.caption.copyWith(
      color: theme.themeColor.primary,
      fontWeight: FontWeight.bold,
    );

    final divisionsStyle = theme.themeText.caption.copyWith(
      color: theme.themeColor.neutral500,
    );

    switch (state) {
      case FilterBottomBarItem.brightness:
        value = filter.brightness;
        break;
      case FilterBottomBarItem.contrast:
        value = filter.contrast;
        break;
      case FilterBottomBarItem.saturation:
        value = filter.saturation;
        break;
      case FilterBottomBarItem.exposure:
        value = filter.exposure;
        break;
      case FilterBottomBarItem.vibrance:
        value = filter.vibrance;
        divisionStringAsFixed = 1;
        break;
      case FilterBottomBarItem.highlight:
        value = filter.highlight;
        divisionStringAsFixed = 1;
        break;
      case FilterBottomBarItem.shadow:
        value = filter.shadow;
        divisionStringAsFixed = 1;
        break;
      case FilterBottomBarItem.temperature:
        value = filter.temperature;
        divisionStringAsFixed = 1;
        break;
      case FilterBottomBarItem.tint:
        value = filter.tint;
        break;
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      mainAxisSize: MainAxisSize.min,
      children: [
        SliderMarked(
          value: value.clamp(min, max),
          valueStringAsFixed: valueStringAsFixed,
          divisionStringAsFixed: divisionStringAsFixed,
          min: min,
          max: max,
          valueStyle: valueTextStyle,
          divisionsStyle: divisionsStyle,
          onUpdated: (value) {
            filter = _onUpdated(
              value,
              state,
              filter,
            );
            widget.onChanged?.call(filter);
          },
          onEnd: (value) {
            filter = _onUpdated(
              value,
              state,
              filter,
            );

            widget.onChangeEnd?.call(filter);
          },
        ),
        kBox8,
        SizedBox(
          height: FilterSubBottomBar.optionHeight,
          child: LayoutBuilder(
            builder: (context, constraints) {
              final itemVisiable =
                  isTablet(context) ? FilterSubBottomBar.itemVisiableTablet : FilterSubBottomBar.itemVisiableMobile;
              final itemWidth = constraints.maxWidth / itemVisiable;
              return ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: FilterBottomBarItem.values.length,
                itemBuilder: (context, index) {
                  final item = FilterBottomBarItem.values[index];
                  return _Item(
                    width: itemWidth,
                    item: item,
                    isSelected: state == item,
                    onTap: () {
                      setState(() {
                        state = item;
                        max = state.max;
                        min = state.min;
                        valueStringAsFixed = state.fractionDigits;
                        divisionStringAsFixed = valueStringAsFixed - 1;
                      });
                    },
                  );
                },
              );
            },
          ),
        ),
      ],
    );
  }

  PicImgFilter _onUpdated(double value, FilterBottomBarItem item, PicImgFilter filter) {
    switch (item) {
      case FilterBottomBarItem.brightness:
        filter = filter.copyWith(brightness: value);
        break;
      case FilterBottomBarItem.contrast:
        filter = filter.copyWith(contrast: value);
        break;
      case FilterBottomBarItem.saturation:
        filter = filter.copyWith(saturation: value);
        break;
      case FilterBottomBarItem.exposure:
        filter = filter.copyWith(exposure: value);
        break;
      case FilterBottomBarItem.vibrance:
        filter = filter.copyWith(vibrance: value);
        break;
      case FilterBottomBarItem.highlight:
        filter = filter.copyWith(highlight: value);
        break;
      case FilterBottomBarItem.shadow:
        filter = filter.copyWith(shadow: value);
        break;
      case FilterBottomBarItem.temperature:
        filter = filter.copyWith(temperature: value);
        break;
      case FilterBottomBarItem.tint:
        filter = filter.copyWith(tint: value);
        break;
    }

    return filter;
  }
}

class _Item extends StatelessWidget {
  const _Item({
    required this.item,
    required this.width,
    required this.isSelected,
    required this.onTap,
  });

  final FilterBottomBarItem item;
  final double width;
  final bool isSelected;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return InkWell(
      overlayColor: WidgetStateProperty.all(Colors.transparent),
      splashFactory: NoSplash.splashFactory,
      onTap: onTap,
      child: SizedBox(
        width: width,
        child: Column(
          children: [
            Padding(
              padding: kPaddingAll4,
              child: CoreImage(
                item.icon,
                width: kEighteen,
                height: kEighteen,
                color: theme.themeColor.onContainer,
              ),
            ).when((it) {
              if (isSelected) {
                return DecoratedBox(
                  decoration: BoxDecoration(
                    color: theme.themeColor.neutral800,
                    borderRadius: kBorderRadius4,
                  ),
                  child: it,
                );
              }
              return it;
            }),
            kBox8,
            Text(
              context.tr(item.lkey),
              style: theme.themeText.caption,
            ),
          ],
        ),
      ),
    );
  }
}
