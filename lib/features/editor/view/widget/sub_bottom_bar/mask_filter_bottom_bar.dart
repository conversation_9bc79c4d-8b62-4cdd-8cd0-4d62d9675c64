// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/material.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../data/data.dart';
import '../../../providers/pic_element_provider.dart';
import '../pic_element_selection_builder.dart';
import 'filter_sub_bottom_bar.dart';

class MaskFilterBottomBar extends PicElementSelectionBuilder {
  const MaskFilterBottomBar({super.key});

  @override
  Widget builder(BuildContext context, PicElementEditorProvider provider) {
    final element = context.read(provider) as PicElementMask;
    return FilterSubBottomBar(
      initialValue: element.filter ?? PicImgFilter.none,
      onChanged: (value) {
        context.read(provider.notifier).updateElementMaskAttributes(filter: value);
      },
      onChangeEnd: (value) {
        context.read(provider.notifier).updateElementMaskAttributes(filter: value);
      },
    );
  }
}
