// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:convert';

import 'package:core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../data/data.dart';
import '../../../../../data/model/common/mask_shape.dart';
import '../../../../../resource/style/app_theme_ext.dart';
import '../../../model/image_data.dart';
import '../../../providers/pic_element_provider.dart';
import '../pic_element_selection_builder.dart';

class MaskShapesBottomBar extends PicElementSelectionBuilder {
  const MaskShapesBottomBar({super.key});

  static final future = Future.microtask(() async {
    final b = await rootBundle.load('assets/jsons/shapes.json');
    final m = jsonDecode(utf8.decode(b.buffer.asUint8List())) as Map<String, dynamic>;

    return (m['shapes'] as List<dynamic>).cast<Map<String, dynamic>>().map(MaskShape.fromJson).toList();
  });

  @override
  Widget builder(BuildContext context, PicElementEditorProvider provider) {
    final theme = Theme.of(context);
    final sized = MediaQuery.sizeOf(context);

    return SizedBox(
      height: sized.height * .3,
      child: FutureBuilder(
        future: future,
        builder: (context, snap) {
          if (snap.hasData == false) return const LoadingWidget();

          final element = context.watch(provider) as PicElementMask;
          final shapes = snap.data as List<MaskShape>;
          return GridView.builder(
            padding: kPaddingAll8,
            itemCount: shapes.length + 1,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: isTablet(context) ? 6 : 4,
              crossAxisSpacing: kEight,
              mainAxisSpacing: kEight,
            ),
            addRepaintBoundaries: false,
            itemBuilder: (context, index) {
              if (index == 0) {
                final data = PreloadImageObject.instance.get(element.source);
                final shape = MaskShape.fromRect(
                  asset: element.source,
                  rect: Offset.zero & Size(100, data.size.aspectRatio * 100),
                );

                return InkWell(
                  onTap: () {
                    context.read(provider.notifier).changeElementMaskShape(shape);
                  },
                  child: Container(
                    padding: kPaddingAll8,
                    decoration: BoxDecoration(
                      borderRadius: kBorderRadius8,
                      border: Border.all(
                        color: (element.shape.asset == shape.asset)
                            ? theme.themeColor.primary
                            : theme.themeColor.neutral400,
                      ),
                    ),
                    child: Icon(
                      Icons.close_rounded,
                      size: 36,
                      color: theme.themeColor.neutral600,
                    ),
                  ),
                );
              }

              final shape = shapes[index - 1];

              return InkWell(
                onTap: () {
                  context.read(provider.notifier).changeElementMaskShape(shape);
                },
                child: Container(
                  padding: kPaddingAll8,
                  decoration: BoxDecoration(
                    borderRadius: kBorderRadius8,
                    border: Border.all(
                      color:
                          (element.shape.asset == shape.asset) ? theme.themeColor.primary : theme.themeColor.neutral400,
                    ),
                  ),
                  child: CoreImage(shape.asset),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
