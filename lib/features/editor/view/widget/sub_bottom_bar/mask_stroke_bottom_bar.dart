// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:math';

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../data/data.dart';
import '../../../../../data/model/common/mask_shape.dart';
import '../../../../../resource/localization/lkey.dart';
import '../../../providers/pic_element_provider.dart';
import '../pic_element_selection_builder.dart';
import 'color_select_bottom_bar.dart';
import 'slider_field.dart';

class MaskStrokeBottomBar extends PicElementSelectionBuilder {
  const MaskStrokeBottomBar({super.key});

  @override
  Widget builder(BuildContext context, PicElementEditorProvider provider) {
    double distance = 0, radians = 0; // default values

    final element = context.watch(provider) as Pic<PERSON>lementMask;
    final stroke = element.shape.stroke;
    if (stroke != null) {
      final translate = stroke.translate;
      distance = sqrt(pow(translate.x, 2) + pow(translate.y, 2));
      if (distance != 0) {
        // tính góc dựa vào trị tuyệt đối của [offsetShadow.x] để lấy dc góc nhỏ hơn 90 | -90 độ
        radians = acos(stroke.translate.x.abs() / distance);
        // nếu [offsetShadow.x] thực là âm thì tính góc bù
        if (translate.x < 0) {
          radians = pi - radians;
        }
        // tiếp tục, nếu [offsetShadow.y] âm thì tính góc đối.
        if (translate.y < 0) {
          radians = -radians;
        }
      } else {
        radians = 0;
      }
    }

    return Padding(
      padding: kPaddingAll16,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SliderField(
            title: context.tr(LKey.common_rotate),
            value: MathUtil.radians2Degrees(radians).ceilToDouble(),
            min: -180,
            max: 180,
            disabled: distance == 0,
            centerThumb: true,
            onChanged: (v) {
              radians = MathUtil.degrees2Radians(v);
              final x = distance * cos(radians);
              final y = distance * sin(radians);
              _updateElementText(
                context,
                provider: provider,
                translate: PicPosition(x: x, y: y),
              );
            },
            suffix: SuffixField(value: MathUtil.radians2Degrees(radians).toStringAsFixed(1)),
          ),
          kBox16,
          SliderField(
            title: context.tr(LKey.common_distance),
            value: distance,
            max: MaskStroke.kMaxTranslateRatio,
            onChanged: (v) {
              distance = v;
              final x = distance * cos(radians);
              final y = distance * sin(radians);
              _updateElementText(
                context,
                provider: provider,
                translate: PicPosition(x: x, y: y),
              );
            },
            suffix: SuffixField(
              value: distance.when((it) {
                return it / MaskStroke.kMaxTranslateRatio * 100;
              }).toStringAsFixed(1),
            ),
          ),
          kBox16,
          SliderField(
            title: context.tr(LKey.text_stroke_width),
            value: (stroke?.width ?? 0) / MaskStroke.kMaxWidth * 100,
            min: 0,
            max: 100,
            onChanged: (v) {
              _updateElementText(
                context,
                provider: provider,
                width: v / 100 * MaskStroke.kMaxWidth,
              );
            },
            suffix: SuffixField(
              value: stroke?.width.when((it) {
                    return it / MaskStroke.kMaxWidth * 100;
                  }).toStringAsFixed(1) ??
                  '0',
            ),
          ),
          kBox16,
          Row(
            children: [
              Expanded(
                child: Text(context.tr(LKey.common_color)),
              ),
              kBox8,
              Expanded(
                flex: 4,
                child: ColorSelectBottomBar(
                  height: kThirtyTwo,
                  borderWidth: 3,
                  shape: BoxShape.circle,
                  initialColor: stroke?.color.toColor(),
                  onSelect: (Color value) {
                    _updateElementText(
                      context,
                      provider: provider,
                      color: value.toHex(),
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _updateElementText(
    BuildContext context, {
    required PicElementEditorProvider provider,
    PicPosition? translate,
    String? color,
    double? width,
  }) {
    final element = context.read(provider) as PicElementMask;
    final stroke = element.shape.stroke?.copyWith(
          width: width,
          color: color,
          translate: translate,
        ) ??
        const MaskStroke();

    context.read(provider.notifier).updateElementMaskAttributes(
          stroke: stroke,
        );
  }
}
