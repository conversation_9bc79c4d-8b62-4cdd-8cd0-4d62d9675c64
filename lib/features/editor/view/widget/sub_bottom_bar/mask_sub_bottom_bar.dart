// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../data/data.dart';
import '../../../../../routes/routes.dart';
import '../../../model/bottom_bar_enum.dart';
import '../../../providers/bottom_bar_provider.dart';
import '../../../providers/editor_providers.dart';
import '../../../providers/pic_element_provider.dart';
import '../../editor_screen.dart';
import '../editor_bottom_bar.dart';
import 'mask_filter_bottom_bar.dart';
import 'mask_shapes_bottom_bar.dart';
import 'mask_stroke_bottom_bar.dart';

class MaskSubBottomBar extends StatelessWidget {
  static const height = 56.0;
  static const double itemVisiableMobile = 5.5;
  static const double itemVisiableTablet = 6.5;

  const MaskSubBottomBar({super.key});

  @override
  Widget build(BuildContext context) {
    final elementKey = context.watch(picElementSelectionProvider);
    final state = context.watch(
      bottomBarEditorProvider.select((e) => e.bottom?.value),
      when: (_, state) => state is MainBottomBarItem || state is MaskBottomBarItem,
    );

    switch (state) {
      case MaskBottomBarItem.shape:
        return const MaskShapesBottomBar();
      case MaskBottomBarItem.stroke:
        return const MaskStrokeBottomBar();
      case MaskBottomBarItem.filter:
        return const MaskFilterBottomBar();
    }

    final hasSelect = PicElement.isElementType(elementKey, PicElementType.mask);

    return SizedBox(
      height: height,
      child: LayoutBuilder(
        builder: (context, constraints) {
          final itemVisiable = isTablet(context) ? itemVisiableTablet : itemVisiableMobile;
          final itemWidth = constraints.maxWidth / itemVisiable;
          return ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: MaskBottomBarItem.values.length,
            itemBuilder: (context, index) {
              final item = MaskBottomBarItem.values[index];
              return BottomAction(
                item: item,
                width: itemWidth,
                disable: !hasSelect && item != MaskBottomBarItem.add,
                automaticallyImplySelection: item.supportBottomBar,
                onTap: () {
                  _onSelect(context, item);
                },
              );
            },
          );
        },
      ),
    );
  }

  void _onSelect(BuildContext context, MaskBottomBarItem item) {
    if (item == MaskBottomBarItem.add) {
      context.pushNamed(Routes.pickImage).then((paths) {
        if (paths is List<String>) {
          context.safety((context) {
            context
                .read(picTemplateEditorProvider.notifier)
                .addElementMask(paths[0], boundary: EditorScreen.area(context).size);
          });
        }
      });
    } else if (item == MaskBottomBarItem.replace) {
      context.pushNamed(Routes.pickImage).then((paths) {
        if (paths is List<String>) {
          context.safety((context) {
            final elementKey = context.read(picElementSelectionProvider);
            if (elementKey == null) return;
            context.read(picElementEditorProvider(elementKey).notifier).replaceElementMaskSource(paths[0]);
          });
        }
      });
    } else if (item == MaskBottomBarItem.filter) {
      context.read(bottomBarEditorProvider.notifier).selectSubBottom(FilterBottomBarItem.brightness);
    }
  }
}
