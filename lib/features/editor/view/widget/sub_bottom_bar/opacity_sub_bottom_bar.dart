// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../resource/style/app_theme_ext.dart';
import 'slider_field.dart';

class OpacitySubBottomBar extends StatelessWidget {
  static const double height = 56;

  const OpacitySubBottomBar({
    super.key,
    required this.label,
    required this.provider,
    this.onChanging,
    this.onChanged,
  });

  final String label;
  final ProviderListenable<double> provider;
  final ValueChanged<double>? onChanging, onChanged;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      height: height,
      padding: kPaddingHorizontal16,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  label,
                  style: theme.themeText.bodyText2,
                ),
              ),
              SuffixField(
                value: (context.read(provider) * 100).toStringAsFixed(1),
              ),
            ],
          ),
          kBox12,
          SliderField(
            value: context.watch(provider),
            onChanged: onChanging,
            onChangedEnd: onChanged,
          ),
        ],
      ),
    );
  }
}
