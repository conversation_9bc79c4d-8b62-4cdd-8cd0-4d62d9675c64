// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../resource/style/app_theme_ext.dart';
import '../../../model/bottom_bar_enum.dart';
import '../../../providers/editor_providers.dart';

class RatioSubBottomBar extends StatelessWidget {
  static const double height = 56.0;
  static const double itemVisiableMobile = 5.5;
  static const double itemVisiableTablet = 7.5;

  const RatioSubBottomBar({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      child: LayoutBuilder(
        builder: (context, constraints) {
          final itemVisiable = isTablet(context) ? itemVisiableTablet : itemVisiableMobile;
          final itemWidth = constraints.maxWidth / itemVisiable;

          return ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: RatioBottomBarItem.values.length,
            itemBuilder: (context, index) {
              final item = RatioBottomBarItem.values[index];
              final ratio = context.watch(picTemplateEditorProvider).value.ratio;
              final isSelected = ratio == item.ratio;
              final size = _calculateSize(context, item: item);

              return _Item(
                width: itemWidth,
                size: size,
                item: item,
                isSelected: isSelected,
              );
            },
          );
        },
      ),
    );
  }

  Size _calculateSize(BuildContext context, {required RatioBottomBarItem item}) {
    return switch (item) {
      RatioBottomBarItem.origin => context.read(picTemplateEditorProvider).value.image.size.aspectRatio.when((ratio) {
          if (ratio < 1.0) {
            return Size(kEighteen, kEighteen / ratio);
          } else {
            return Size(kEighteen * ratio, kEighteen);
          }
        }),
      RatioBottomBarItem.ratio_1_1 => const Size.square(kEighteen),
      RatioBottomBarItem.ratio_3_2 || RatioBottomBarItem.ratio_4_3 => Size(kEighteen * item.ratio!, kEighteen),
      RatioBottomBarItem.ratio_3_4 => Size(kTwentyFour * item.ratio!, kTwentyFour),
      RatioBottomBarItem.ratio_5_4 => Size(kThirty, kThirty / item.ratio!),
      RatioBottomBarItem.ratio_16_9 => Size(kThirty, kThirty / item.ratio!),
    };
  }
}

class _Item extends StatelessWidget {
  const _Item({
    required this.item,
    required this.width,
    required this.isSelected,
    required this.size,
  });

  final double width;
  final RatioBottomBarItem item;
  final bool isSelected;
  final Size size;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return InkWell(
      overlayColor: WidgetStateProperty.all(Colors.transparent),
      onTap: () {
        context.read(picTemplateEditorProvider.notifier).updateAttributes(
              ratio: item.ratio,
              ratioNullable: true,
            );
      },
      child: SizedBox(
        width: width,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: size.width,
              height: size.height,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: kBorderRadius4,
                border: Border.all(
                  color: isSelected ? theme.themeColor.primary : theme.themeColor.onContainer,
                ),
              ),
            ),
            kBox8,
            Text(
              context.tr(item.lkey),
              style: theme.themeText.caption.copyWith(
                color: isSelected ? theme.themeColor.primary : theme.themeColor.onContainer,
              ),
              maxLines: kOne.toInt(),
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
