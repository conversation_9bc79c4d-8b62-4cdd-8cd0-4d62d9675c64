// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../resource/style/app_theme_ext.dart';
import '../../../model/rotation_item.dart';
import '../../../providers/editor_providers.dart';

class RotateSubBottomBar extends StatelessWidget {
  static const double height = 42;
  static const Size itemSize = Size.fromWidth(32);

  const RotateSubBottomBar({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return SizedBox(
      height: height,
      child: ScrollbarTheme(
        data: ScrollbarThemeData(
          thumbVisibility: WidgetStateProperty.all(true),
          thumbColor: WidgetStateProperty.all(theme.themeColor.neutral400),
          thickness: WidgetStateProperty.all(kTwo),
        ),
        child: Scrollbar(
          scrollbarOrientation: ScrollbarOrientation.bottom,
          child: ListView.separated(
            padding: const EdgeInsets.symmetric(vertical: kThree),
            primary: true,
            scrollDirection: Axis.horizontal,
            itemCount: RotationItem.values.length,
            itemBuilder: (context, index) {
              final item = RotationItem.values[index];
              return TextButton(
                onPressed: () {
                  context.read(picTemplateEditorProvider.notifier).updateAttributes(rotation: item.radians);
                },
                style: TextButton.styleFrom(
                  overlayColor: theme.themeColor.neutral600,
                  fixedSize: itemSize,
                  padding: kPaddingHorizontal12,
                ),
                child: Text(
                  item.title,
                  style: theme.themeText.bodyText1,
                ),
              );
            },
            separatorBuilder: (context, index) => kBox8,
          ),
        ),
      ),
    );
  }
}
