// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:math' as math;

import 'package:core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../../../resource/style/app_theme_ext.dart';

class SliderField extends StatefulWidget {
  const SliderField({
    super.key,
    this.title,
    this.titleStyle,
    this.suffix,
    required this.value,
    this.min = 0.0,
    this.max = 1.0,
    this.thumbSize = 16,
    this.trackSizeFactor = .2,
    this.trackColor,
    this.activeTrackColor,
    this.centerThumb = false,
    this.thumbDecoration,
    this.disabled = false,
    this.onChanged,
    this.onChangedEnd,
  });

  final String? title;
  final TextStyle? titleStyle;
  final Widget? suffix;
  final double thumbSize, trackSizeFactor;
  final double max, min, value;
  final Decoration? thumbDecoration;
  final Color? trackColor;
  final Color? activeTrackColor;
  final bool centerThumb;
  final bool disabled;
  final ValueChanged<double>? onChanged, onChangedEnd;

  @override
  State<SliderField> createState() => _SliderFieldState();
}

class _SliderFieldState extends State<SliderField> {
  late final ValueNotifier<double> _notifier = ValueNotifier<double>(caculateInitialValue())
    ..addListener(() {
      widget.onChanged?.call(caculateCurrentValue(true));
    });

  @override
  void didUpdateWidget(covariant SliderField oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (_notifier.value != widget.value) {
      _notifier.value = caculateInitialValue();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final decoration = widget.thumbDecoration ??
        BoxDecoration(
          shape: BoxShape.circle,
          color: widget.disabled ? theme.themeColor.neutral400 : theme.themeColor.primary,
          // boxShadow: theme.themeDecoration.boxShadow,
        );

    return Row(
      children: [
        if (widget.title?.isNotEmpty == true) ...[
          Expanded(
            child: Text(
              widget.title!,
              style: widget.titleStyle,
            ),
          ),
          kBox8,
        ],
        Expanded(
          flex: 2,
          child: LayoutBuilder(
            builder: (context, constrained) {
              return GestureDetector(
                onHorizontalDragUpdate: (details) {
                  if (widget.disabled) return;
                  _notifier.value = math.max(.0, math.min(1.0, details.localPosition.dx / constrained.maxWidth));
                },
                onHorizontalDragEnd: (details) {
                  if (widget.disabled) return;
                  widget.onChangedEnd?.call(caculateCurrentValue());
                },
                onHorizontalDragCancel: () {
                  if (widget.disabled) return;
                  widget.onChangedEnd?.call(caculateCurrentValue());
                },
                child: CustomPaint(
                  size: Size.fromHeight(widget.thumbSize),
                  painter: _Painter(
                    notifier: _notifier,
                    thumbSize: widget.thumbSize,
                    trackSizeFactor: widget.trackSizeFactor,
                    thumbPainter: decoration.createBoxPainter(),
                    trackColor: widget.disabled
                        ? theme.themeColor.disabled
                        : widget.trackColor ??
                            theme.byBrightness(
                              light: theme.themeColor.neutral300,
                              dark: theme.themeColor.neutral800,
                            ),
                    activeTrackColor: widget.disabled
                        ? theme.themeColor.disabled
                        : widget.activeTrackColor ?? theme.themeColor.primary400,
                    centerThumb: widget.centerThumb,
                  ),
                ),
              );
            },
          ),
        ),
        if (widget.suffix != null) ...[
          kBox16,
          widget.suffix!,
        ],
      ],
    );
  }

  @override
  void dispose() {
    _notifier.dispose();
    super.dispose();
  }

  double caculateCurrentValue([bool haptic = false]) {
    final value = (widget.max - widget.min) * _notifier.value + widget.min;
    if (haptic) {
      final progress = _notifier.value;
      if (progress == 0 || progress == .5 || progress == 1) {
        HapticFeedback.lightImpact();
      }
    }
    return value;
  }

  double caculateInitialValue() {
    return (widget.value - widget.min) / (widget.max - widget.min);
  }
}

class _Painter extends CustomPainter {
  _Painter({
    required this.notifier,
    required this.thumbSize,
    required this.trackSizeFactor,
    required this.thumbPainter,
    required this.trackColor,
    required this.activeTrackColor,
    required this.centerThumb,
  })  : _configuration = ImageConfiguration(size: Size.square(thumbSize)),
        _paint = Paint(),
        super(repaint: notifier);

  final ValueNotifier<double> notifier;
  final double thumbSize, trackSizeFactor;
  final BoxPainter thumbPainter;
  final Color trackColor;
  final Color activeTrackColor;
  final bool centerThumb;
  final ImageConfiguration _configuration;
  final Paint _paint;

  @override
  void paint(Canvas canvas, Size size) {
    Rect rect = Rect.zero;

    double trackHeight = size.height * trackSizeFactor;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        rect = Rect.fromCenter(center: size.center(Offset.zero), width: size.width, height: trackHeight),
        Radius.circular(size.height / 2),
      ),
      _paint..color = trackColor,
    );

    rect = Rect.zero;
    final dy = size.height / 2 - trackHeight / 2;
    if (centerThumb) {
      if (notifier.value < .5) {
        final x = size.width * notifier.value;
        rect = Rect.fromLTRB(x, dy, size.width / 2, dy + trackHeight);
      } else if (notifier.value > .5) {
        rect = Rect.fromLTRB(size.width / 2, dy, size.width * notifier.value, dy + trackHeight);
      }
    } else {
      rect = Offset(0, dy) & Size(size.width * notifier.value, trackHeight);
    }

    if (rect != Rect.zero) {
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          rect,
          Radius.circular(trackHeight),
        ),
        _paint..color = activeTrackColor,
      );
    }

    thumbPainter.paint(
      canvas,
      Offset(size.width * notifier.value - thumbSize / 2, 0),
      _configuration,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

class SuffixField extends StatelessWidget {
  const SuffixField({required this.value});

  final String value;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      width: kFortyTwo,
      padding: const EdgeInsets.symmetric(vertical: kFour),
      decoration: BoxDecoration(
        color: theme.byBrightness(
          light: theme.themeColor.neutral100,
          dark: theme.themeColor.neutral800,
        ),
        borderRadius: kBorderRadius12,
      ),
      child: Text(
        value,
        style: theme.themeText.caption,
        textAlign: TextAlign.center,
      ),
    );
  }
}
