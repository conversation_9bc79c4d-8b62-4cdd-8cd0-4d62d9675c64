// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

import '../../../../../component/services/remote_config/remote_config_service.dart';
import '../../../../../component/services/remote_config/remote_configuration.dart';
import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../data/model/remote_config/sticker_collection.dart';
import '../../../../../resource/localization/lkey.dart';
import '../../../../../resource/style/app_theme_ext.dart';
import '../../../model/image_data.dart';
import '../../../providers/editor_providers.dart';

import '../../editor_screen.dart';

class StickerCollectionBottomBar extends StatelessWidget {
  const StickerCollectionBottomBar({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final sized = MediaQuery.sizeOf(context);
    final list = RemoteConfigService.instance
        .get<List<dynamic>>(RemoteConfiguration.stickerCollections)
        .map((e) => StickerCollection.fromJson(e as Map<String, dynamic>))
        .toList();

    if (list.isEmpty) {
      return Container(
        height: sized.height * .3,
        alignment: Alignment.center,
        padding: kPaddingAll24,
        child: EmptyState(
          title: Text(
            context.tr(LKey.ERUNOW),
          ),
        ),
      );
    }

    return SizedBox(
      height: sized.height * .37,
      child: DefaultTabController(
        length: list.length,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            kBox8,
            Text.rich(
              TextSpan(
                text: context.tr(LKey.design_by),
                style: theme.themeText.bodyText2.copyWith(
                  color: theme.themeColor.neutral500,
                ),
                children: [
                  TextSpan(
                    text: ' Freepik',
                    style: theme.themeText.bodyText2.copyWith(
                      color: Colors.blue,
                      fontWeight: FontWeight.bold,
                    ),
                    recognizer: TapGestureRecognizer()..onTap = () {},
                  ),
                  const WidgetSpan(child: kBox8),
                ],
              ),
              textAlign: TextAlign.end,
            ),
            kBox4,
            TabBar(
              labelPadding: EdgeInsets.zero,
              isScrollable: true,
              tabAlignment: TabAlignment.start,
              dividerHeight: 3,
              indicatorSize: TabBarIndicatorSize.label,
              indicator: StickerCollectionIndicator(
                color: theme.themeColor.secondary,
              ),
              dividerColor: theme.themeColor.neutral200,
              tabs: list
                  .map(
                    (e) => Padding(
                      padding: kPaddingAll8,
                      child: CoreImage(
                        e.label,
                        fit: BoxFit.cover,
                        width: 48,
                        height: 48,
                      ),
                    ),
                  )
                  .toList(),
            ),
            Expanded(
              child: TabBarView(
                children: list.map((e) => _StickerCollectionBottomBar(collection: e)).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _StickerCollectionBottomBar extends StatelessWidget {
  final StickerCollection collection;

  const _StickerCollectionBottomBar({required this.collection});

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      padding: kPaddingAll12,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: isTablet(context) ? 6 : 4,
        mainAxisSpacing: kEight,
        crossAxisSpacing: kSixteen,
      ),
      itemCount: collection.stickers.length,
      itemBuilder: (context, index) {
        final sticker = collection.stickers[index];
        return InkWell(
          onTap: () {
            context
                .read(picTemplateEditorProvider.notifier)
                .addElementSticker(sticker, boundary: EditorScreen.area(context).size);
          },
          child: CoreImage(
            sticker,
            borderRadius: kBorderRadius12,
            onLoaded: (value) {
              if (value != null) {
                PreloadImageObject.instance.loadByUIImage(sticker, value.image);
              }
            },
          ),
        );
      },
    );
  }
}

class StickerCollectionIndicator extends Decoration {
  final Color color;

  const StickerCollectionIndicator({required this.color});

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _SitkcerCollectionBoxPainter(color: color);
  }
}

class _SitkcerCollectionBoxPainter extends BoxPainter {
  final Color color;

  _SitkcerCollectionBoxPainter({required this.color});

  final painter = Paint();

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    canvas.drawLine(
      Offset(offset.dx, offset.dy + configuration.size!.height - 1.5),
      Offset(offset.dx + configuration.size!.width, offset.dy + configuration.size!.height - 1.5),
      painter
        ..strokeWidth = 3
        ..color = color,
    );
  }
}
