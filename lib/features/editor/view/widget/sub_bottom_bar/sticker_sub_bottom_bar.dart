// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../data/data.dart';
import '../../../model/bottom_bar_enum.dart';
import '../../../providers/bottom_bar_provider.dart';
import '../../../providers/pic_element_provider.dart';
import '../editor_bottom_bar.dart';
import 'sticker_collections_bottom_bar.dart';
import 'sticker_opacity_bottom_bar.dart';

class StickerSubBottomBar extends StatefulWidget {
  const StickerSubBottomBar({super.key});

  @override
  State<StickerSubBottomBar> createState() => _StickerSubBottomBarState();
}

class _StickerSubBottomBarState extends State<StickerSubBottomBar> {
  static const height = 56.0;
  static const double itemVisiableMobile = 5.5;
  static const double itemVisiableTablet = 6.5;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final elementKey = context.read(picElementSelectionProvider);
      if (elementKey == null) {
        context.read(bottomBarEditorProvider.notifier).selectBottom(StickerBottomBarItem.collection);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = context.watch(
      bottomBarEditorProvider.select((e) => e.bottom?.value),
    );

    switch (state) {
      case StickerBottomBarItem.collection:
        return const StickerCollectionBottomBar();
      case StickerBottomBarItem.opacity:
        return const StickerOpacitySubBottomBar();
    }

    final elementKey = context.watch(picElementSelectionProvider);
    final hasSelect = PicElement.isElementType(elementKey, PicElementType.sticker);

    return SizedBox(
      height: height,
      child: LayoutBuilder(
        builder: (context, constraints) {
          final itemVisiable = isTablet(context) ? itemVisiableTablet : itemVisiableMobile;
          final itemWidth = constraints.maxWidth / itemVisiable;
          return ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: StickerBottomBarItem.values.length,
            itemBuilder: (context, index) {
              final item = StickerBottomBarItem.values[index];
              return BottomAction(
                item: item,
                width: itemWidth,
                disable: !hasSelect && item != StickerBottomBarItem.collection,
                automaticallyImplySelection: false,
                onTap: () {
                  if (item.supportBottomBar) {
                    context.read(bottomBarEditorProvider.notifier).selectBottom(item);
                  }

                  switch (item) {
                    case StickerBottomBarItem.flip_horizontal:
                      _onSelectFlipItem(elementKey!, PicFlipEnum.horizontal);
                      break;
                    case StickerBottomBarItem.flip_vertical:
                      _onSelectFlipItem(elementKey!, PicFlipEnum.vertical);
                      break;

                    default:
                  }
                },
              );
            },
          );
        },
      ),
    );
  }

  void _onSelectFlipItem(String elementKey, PicFlipEnum? flipEnum) {
    final element = context.read(picElementEditorProvider(elementKey)) as PicElementSticker;
    if (element.flipEnum != null) {
      flipEnum = element.flipEnum!.replace(flipEnum);
    }

    context.read(picElementEditorProvider(elementKey).notifier).updateElementStickerAttributes(
          flipEnum: flipEnum,
          flipEnumNullable: true,
        );
  }
}
