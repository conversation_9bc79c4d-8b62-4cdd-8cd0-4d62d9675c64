// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../data/data.dart';
import '../../../../../resource/localization/lkey.dart';
import '../../../../../resource/style/app_theme_ext.dart';
import '../../../providers/pic_element_provider.dart';
import '../pic_element_selection_builder.dart';
import 'slider_field.dart';

class TextBendSubBottomBar extends PicElementSelectionBuilder {
  static const double height = 56;

  const TextBendSubBottomBar({super.key});

  @override
  Widget builder(BuildContext context, PicElementEditorProvider provider) {
    final theme = Theme.of(context);
    final bendProvider = provider.select((e) => (e as PicElementText).metadata.bend ?? 0.0);
    final value = context.watch(bendProvider);
    return Container(
      height: height,
      padding: kPaddingHorizontal16,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  context.tr(LKey.common_bend),
                  style: theme.themeText.bodyText2,
                ),
              ),
              SuffixField(
                value: (value * 360).toStringAsFixed(1),
              ),
            ],
          ),
          kBox12,
          SliderField(
            value: value,
            centerThumb: true,
            min: -1,
            max: 1,
            onChanged: (value) {
              context.read(provider.notifier).resizeElementText(
                    bend: double.parse(value.toStringAsFixed(2)),
                  );
            },
          ),
        ],
      ),
    );
  }
}
