// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/src/widgets/framework.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../data/data.dart';
import '../../../providers/pic_element_provider.dart';
import '../pic_element_selection_builder.dart';
import 'color_select_bottom_bar.dart';

class TextColorBottomBar extends PicElementSelectionBuilder {
  const TextColorBottomBar({super.key});

  @override
  Widget builder(BuildContext context, PicElementEditorProvider provider) {
    final element = context.watch(provider) as PicElementText;

    return ColorSelectBottomBar(
      initialColor: element.metadata.color?.toColor(),
      onSelect: (color) {
        final metadata = element.metadata.copyWith(color: color.toHex());
        context.read(provider.notifier).updateElementTextAttributes(metadata: metadata);
      },
    );
  }
}
