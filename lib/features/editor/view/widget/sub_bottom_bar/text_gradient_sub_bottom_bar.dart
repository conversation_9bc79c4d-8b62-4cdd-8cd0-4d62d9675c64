// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/material.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../data/data.dart';
import '../../../providers/pic_element_provider.dart';
import '../gradient_maker.dart';
import '../pic_element_selection_builder.dart';

class TextGradientSubBottomBar extends PicElementSelectionBuilder {
  const TextGradientSubBottomBar({super.key});

  @override
  Widget builder(BuildContext context, PicElementEditorProvider provider) {
    final element = context.read(provider) as PicElementText;
    PicGradient gradient = PicGradient.blackAndWhite;
    if (element.metadata.shader is PicGradient) {
      gradient = element.metadata.shader as PicGradient;
    }

    return GradientMaker(
      initGradient: gradient.toLinearGradient(false),
      initRotation: gradient.rotation,
      enableRotation: element.metadata.hasBend == false,
      onChanged: (value) {
        final element = context.read(provider) as PicElementText;
        context.read(provider.notifier).updateElementTextAttributes(
              metadata: element.metadata.copyWith(
                shader: PicGradient.fromLinearGradient(value),
              ),
            );
      },
    );
  }
}
