// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../resource/localization/lkey.dart';
import '../../../providers/pic_element_provider.dart';
import '../pic_element_selection_builder.dart';
import 'opacity_sub_bottom_bar.dart';

class TextOpacitySubBottomBar extends PicElementSelectionBuilder {
  const TextOpacitySubBottomBar({super.key});

  @override
  Widget builder(BuildContext context, PicElementEditorProvider provider) {
    return OpacitySubBottomBar(
        label: context.tr(LKey.common_opacity),
      provider: provider.select((element) => element.opacity),
      onChanging: (value) {
        context.read(provider.notifier).updateElementTextAttributes(opacity: value);
      },
    );
  }
}
