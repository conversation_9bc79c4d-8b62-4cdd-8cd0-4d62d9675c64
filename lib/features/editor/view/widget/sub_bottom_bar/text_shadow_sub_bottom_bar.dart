// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:math';

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../data/data.dart';
import '../../../../../resource/localization/lkey.dart';
import '../../../providers/pic_element_provider.dart';
import '../pic_element_selection_builder.dart';
import 'color_select_bottom_bar.dart';
import 'slider_field.dart';

class TextShadowSubBottomBar extends PicElementSelectionBuilder {
  const TextShadowSubBottomBar({super.key});

  @override
  Widget builder(BuildContext context, PicElementEditorProvider provider) {
    double distance = 0, radians = 0; // default values

    final element = context.watch(provider) as PicElementText;
    final shadow = element.metadata.shadow;
    if (shadow != null) {
      distance = sqrt(pow(shadow.x, 2) + pow(shadow.y, 2));
      if (distance != 0) {
        // tính góc dựa vào trị tuyệt đối của [offsetShadow.x] để lấy dc góc nhỏ hơn 90 | -90 độ
        radians = acos(shadow.x.abs() / distance);
        // nếu [offsetShadow.x] thực là âm thì tính góc bù
        if (shadow.x < 0) {
          radians = pi - radians;
        }
        // tiếp tục, nếu [offsetShadow.y] âm thì tính góc đối.
        if (shadow.y < 0) {
          radians = -radians;
        }
      } else {
        radians = 0;
      }
    }

    return Padding(
      padding: kPaddingHorizontal16,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          kBox8,
          SliderField(
            title: context.tr(LKey.common_rotate),
            value: MathUtil.radians2Degrees(radians),
            min: -180,
            max: 180,
            disabled: distance == 0,
            centerThumb: true,
            onChanged: (v) {
              radians = MathUtil.degrees2Radians(v);
              final x = distance * cos(radians);
              final y = distance * sin(radians);
              _updateElementText(
                context,
                provider: provider,
                x: x,
                y: y,
              );
            },
            suffix: SuffixField(value: MathUtil.radians2Degrees(radians).toStringAsFixed(1)),
          ),
          kBox8,
          SliderField(
            title: context.tr(LKey.common_distance),
            value: distance.roundToDouble(),
            min: 0,
            max: 20,
            onChanged: (v) {
              distance = v;
              final x = distance * cos(radians);
              final y = distance * sin(radians);
              _updateElementText(
                context,
                provider: provider,
                x: x,
                y: y,
              );
            },
            suffix: SuffixField(value: distance.toStringAsFixed(1)),
          ),
          kBox8,
          SliderField(
            title: context.tr(LKey.common_blur),
            value: shadow?.blur ?? 1,
            min: 0,
            max: 10,
            onChanged: (v) {
              _updateElementText(
                context,
                provider: provider,
                blur: v,
              );
            },
            suffix: SuffixField(value: shadow?.blur.toStringAsFixed(1) ?? '0'),
          ),
          kBox8,
          SliderField(
            title: context.tr(LKey.common_opacity),
            value: shadow?.opacity ?? 1,
            onChanged: (v) {
              _updateElementText(
                context,
                provider: provider,
                opacity: v,
              );
            },
            suffix: SuffixField(value: shadow?.opacity.toStringAsFixed(1) ?? '1'),
          ),
          kBox8,
          Row(
            children: [
              Expanded(
                child: Text(context.tr(LKey.common_color)),
              ),
              kBox8,
              Expanded(
                flex: 4,
                child: ColorSelectBottomBar(
                  height: 32,
                  borderWidth: 3,
                  shape: BoxShape.circle,
                  initialColor: shadow?.color.toColor(),
                  onSelect: (Color value) {
                    _updateElementText(
                      context,
                      provider: provider,
                      color: value.toHex(),
                    );
                  },
                ),
              ),
            ],
          ),
          kBox8,
        ],
      ),
    );
  }

  void _updateElementText(
    BuildContext context, {
    required PicElementEditorProvider provider,
    double? x,
    double? y,
    double? blur,
    String? color,
    double? opacity,
  }) {
    final element = context.read(provider) as PicElementText;
    final shadow = element.metadata.shadow?.copyWith(
          x: x,
          y: y,
          blur: blur,
          color: color,
          opacity: opacity,
        ) ??
        TextShadow(
          x: x ?? 0,
          y: y ?? 0,
          blur: blur ?? 1,
          color: Colors.black.toHex(),
          opacity: opacity ?? 1,
        );

    context.read(provider.notifier).updateElementTextAttributes(
          metadata: element.metadata.copyWith(shadow: shadow),
        );
  }
}
