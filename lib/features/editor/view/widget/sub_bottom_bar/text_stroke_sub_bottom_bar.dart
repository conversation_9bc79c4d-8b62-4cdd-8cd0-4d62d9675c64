// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../data/model/template/pic_element_text.dart';
import '../../../../../data/model/template/pic_stroke.dart';
import '../../../../../resource/localization/lkey.dart';
import '../../../providers/pic_element_provider.dart';
import '../pic_element_selection_builder.dart';
import 'color_select_bottom_bar.dart';
import 'slider_field.dart';

class TextStrokeSubBottomBar extends PicElementSelectionBuilder {
  const TextStrokeSubBottomBar({super.key});

  @override
  Widget builder(BuildContext context, PicElementEditorProvider provider) {
    final element = context.watch(provider) as PicElementText;
    final stroke = element.metadata.stroke;

    return Padding(
      padding: kPaddingAll16,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(context.tr(LKey.common_color)),
              ),
              kBox8,
              Expanded(
                flex: 4,
                child: ColorSelectBottomBar(
                  height: 32,
                  borderWidth: 3,
                  shape: BoxShape.circle,
                  initialColor: stroke?.color.toColor(),
                  onSelect: (Color value) {
                    _updateElementText(
                      context,
                      provider,
                      color: value.toHex(),
                    );
                  },
                ),
              ),
            ],
          ),
          kBox8,
          SliderField(
            title: context.tr(LKey.text_stroke_width),
            value: stroke?.thickness ?? 0,
            max: PicStroke.maxThickness,
            onChanged: (value) {
              _updateElementText(
                context,
                provider,
                thickness: value,
              );
            },
            suffix: SuffixField(
              value: stroke?.thickness.when((it) {
                    return it / PicStroke.maxThickness * 100;
                  }).toStringAsFixed(1) ??
                  '0',
            ),
          ),
          kBox8,
          SliderField(
            title: context.tr(LKey.common_opacity),
            value: stroke?.opacity ?? 1,
            onChanged: (value) {
              _updateElementText(
                context,
                provider,
                opacity: value,
              );
            },
            suffix: SuffixField(value: stroke?.opacity.toStringAsFixed(1) ?? '1'),
          ),
        ],
      ),
    );
  }

  void _updateElementText(
    BuildContext context,
    PicElementEditorProvider provider, {
    String? color,
    double? thickness,
    double? opacity,
  }) {
    final element = context.read(provider) as PicElementText;
    final metadata = element.metadata;
    final stroke = metadata.stroke?.copyWith(
          color: color,
          thickness: thickness,
          opacity: opacity,
        ) ??
        PicStroke(
          color: color ?? Colors.black.toHex(),
          thickness: thickness ?? 0,
          opacity: opacity ?? 1,
        );

    context.read(provider.notifier).updateElementTextAttributes(
          metadata: metadata.copyWith(stroke: stroke),
        );
  }
}
