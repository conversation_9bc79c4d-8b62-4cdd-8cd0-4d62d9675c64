// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../data/data.dart';
import '../../../../../resource/style/app_theme_ext.dart';
import '../../../providers/pic_element_provider.dart';
import '../pic_element_selection_builder.dart';

class TextStyleBottomBar extends PicElementSelectionBuilder {
  static const int itemPerRow = 3;
  static const int itemPerRowTablet = 5;

  static const double ratioShowBottom = 2.5;
  static const double axisSpacing = 12;
  static const double childAspectRatio = 3 / 2;

  const TextStyleBottomBar({super.key});

  @override
  Widget builder(BuildContext context, PicElementEditorProvider provider) {
    final sized = MediaQuery.sizeOf(context);
    final itemVisiable = isTablet(context) ? itemPerRowTablet : itemPerRow;
    final element = context.watch(provider) as PicElementText;

    double width = sized.width - kPaddingAll8.horizontal;
    width -= (itemVisiable - 1) * axisSpacing;
    width /= itemVisiable;
    width /= childAspectRatio;

    return SizedBox(
      height: width * ratioShowBottom + kPaddingAll8.top + (ratioShowBottom.toInt() * axisSpacing),
      child: GridView.builder(
        padding: kPaddingAll8,
        itemCount: TextFontEnum.values.length,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: itemVisiable,
          mainAxisSpacing: axisSpacing,
          crossAxisSpacing: axisSpacing,
          childAspectRatio: childAspectRatio,
        ),
        itemBuilder: (context, index) {
          final fontEnum = TextFontEnum.values[index];
          final selected = fontEnum == element.metadata.fontEnum;

          return _Item(
            fontEnum: fontEnum,
            isSelected: selected,
            onTap: () {
              if (selected) return;
              context.read(provider.notifier).resizeElementText(fontEnum: fontEnum);
            },
          );
        },
      ),
    );
  }
}

class _Item extends StatelessWidget {
  const _Item({
    required this.fontEnum,
    required this.isSelected,
    required this.onTap,
  });

  final TextFontEnum fontEnum;
  final bool isSelected;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: theme.themeColor.surface,
          borderRadius: kBorderRadius8,
          boxShadow: [
            BoxShadow(
              color: theme.themeColor.neutral200,
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
          border: isSelected
              ? Border.all(
                  color: theme.themeColor.primary,
                  width: 2,
                  strokeAlign: BorderSide.strokeAlignOutside,
                )
              : null,
          // borderRadius: kBorderRadius4,
        ),
        child: Column(
          children: [
            Expanded(
              child: Center(
                child: Text(
                  'Aa',
                  style: fontEnum.builder(
                    fontSize: kThirtyTwo,
                    color: isSelected ? theme.themeColor.primary : null,
                  ),
                ),
              ),
            ),
            Text(
              fontEnum.family.replaceAll('_', ' '),
              style: theme.themeText.caption.copyWith(
                color: isSelected ? theme.themeColor.primary : null,
              ),
              maxLines: kOneLine,
              overflow: TextOverflow.ellipsis,
            ),
            kBox8,
          ],
        ),
      ),
    );
  }
}
