// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../data/data.dart';
import '../../../../../routes/routes.dart';
import '../../../model/bottom_bar_enum.dart';
import '../../../providers/bottom_bar_provider.dart';
import '../../../providers/editor_providers.dart';
import '../../../providers/pic_element_provider.dart';
import '../../editor_screen.dart';
import '../editor_bottom_bar.dart';
import 'text_bend_sub_bottom_bar.dart';
import 'text_color_bottom_bar.dart';
import 'text_gradient_sub_bottom_bar.dart';
import 'text_opacity_sub_bottom_bar.dart';
import 'text_shadow_sub_bottom_bar.dart';
import 'text_stroke_sub_bottom_bar.dart';
import 'text_style_bottom_bar.dart';

class TextSubBottomBar extends StatelessWidget {
  static const height = 56.0;
  static const double itemVisiableMobile = 5.5;
  static const double itemVisiableTablet = 6.5;

  const TextSubBottomBar({super.key});

  @override
  Widget build(BuildContext context) {
    final state = context.watch(
      bottomBarEditorProvider.select((e) => e.bottom?.value),
      when: (_, state) => state is MainBottomBarItem || state is TextBottomBarItem,
    );
    switch (state) {
      case TextBottomBarItem.color:
        return const TextColorBottomBar();
      case TextBottomBarItem.font:
        return const TextStyleBottomBar();
      case TextBottomBarItem.gradient:
        return const TextGradientSubBottomBar();
      case TextBottomBarItem.opacity:
        return const TextOpacitySubBottomBar();
      case TextBottomBarItem.bend:
        return const TextBendSubBottomBar();
      case TextBottomBarItem.shadow:
        return const TextShadowSubBottomBar();
      case TextBottomBarItem.stroke:
        return const TextStrokeSubBottomBar();
    }

    final elementKey = context.watch(picElementSelectionProvider);
    final hasSelect = PicElement.isElementType(elementKey, PicElementType.text);

    return SizedBox(
      height: height,
      child: LayoutBuilder(
        builder: (context, constraints) {
          final itemVisiable = isTablet(context) ? itemVisiableTablet : itemVisiableMobile;
          final itemWidth = constraints.maxWidth / itemVisiable;
          return ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: TextBottomBarItem.values.length,
            itemBuilder: (context, index) {
              final item = TextBottomBarItem.values[index];
              return BottomAction(
                item: item,
                width: itemWidth,
                disable: !hasSelect && item != TextBottomBarItem.add,
                automaticallyImplySelection: item.supportBottomBar,
                onTap: () {
                  _onSelect(context, item);
                },
              );
            },
          );
        },
      ),
    );
  }

  void _onSelect(BuildContext context, TextBottomBarItem item) {
    if (item == TextBottomBarItem.add) {
      context.pushNamed(Routes.textInput).then((content) {
        if (content is String) {
          context.safety((context) {
            context
                .read(picTemplateEditorProvider.notifier)
                .addElementText(content, boundary: EditorScreen.area(context).size);
          });
        }
      });
    } else if (item == TextBottomBarItem.background) {
      context.pushNamed(Routes.pickImage).then((paths) {
        if (paths is List<String>) {
          context.safety((context) {
            final elementKey = context.read(picElementSelectionProvider);
            if (elementKey == null) return;
            context.read(picElementEditorProvider(elementKey).notifier).changeElementTextBackground(source: paths[0]);
          });
        }
      });
    }
  }
}
