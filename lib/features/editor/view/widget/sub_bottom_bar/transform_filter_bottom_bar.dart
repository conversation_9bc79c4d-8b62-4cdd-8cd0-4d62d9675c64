// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/widgets.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../providers/editor_providers.dart';
import 'filter_sub_bottom_bar.dart';

class TransformFilterBottomBar extends StatelessWidget {
  const TransformFilterBottomBar({super.key});

  @override
  Widget build(BuildContext context) {
    return FilterSubBottomBar(
      initialValue: context.watch(picTemplateEditorProvider).value.filter,
      onChanged: (value) {
        context.read(picTemplateEditorProvider.notifier).updateAttributes(
              filter: value,
            );
      },
    );
  }
}
