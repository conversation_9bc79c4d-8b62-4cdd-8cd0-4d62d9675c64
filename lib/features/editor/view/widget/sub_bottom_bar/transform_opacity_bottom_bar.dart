// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/widgets.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../resource/localization/lkey.dart';
import '../../../providers/editor_providers.dart';
import 'opacity_sub_bottom_bar.dart';

class TransformOpacityBottomBar extends StatelessWidget {
  const TransformOpacityBottomBar({super.key});

  @override
  Widget build(BuildContext context) {
    return OpacitySubBottomBar(
      label: context.tr(LKey.common_opacity),
      provider: picTemplateEditorProvider.select((e) => e.value.opacity),
      onChanging: (value) => _onChangeOpacity(context, value),
    );
  }

  void _onChangeOpacity(BuildContext context, double value) {
    context.read(picTemplateEditorProvider.notifier).updateAttributes(opacity: value);
  }
}
