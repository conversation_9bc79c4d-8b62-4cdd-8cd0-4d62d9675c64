// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../data/data.dart';
import '../../../../../resource/localization/lkey.dart';
import '../../../model/bottom_bar_enum.dart';
import '../../../providers/bottom_bar_provider.dart';
import '../../../providers/editor_providers.dart';
import '../editor_bottom_bar.dart';
import 'blur_sub_bottom_bar.dart';
import 'filter_sub_bottom_bar.dart';
import 'opacity_sub_bottom_bar.dart';
import 'ratio_sub_bottom_bar.dart';
import 'rotate_sub_bottom_bar.dart';

class TransformSubBottomBar extends StatelessWidget {
  static const double height = 56.0;
  static const double itemVisiableMobile = 5.5;
  static const double itemVisiableTablet = 6.5;

  const TransformSubBottomBar({super.key});

  @override
  Widget build(BuildContext context) {
    final provider = bottomBarEditorProvider.select((e) => e.bottom?.value);
    final state = context.watch(provider);
    switch (state) {
      case TransformBottomBarItem.crop:
        return const RatioSubBottomBar();
      case TransformBottomBarItem.rotate:
        return const RotateSubBottomBar();
      case TransformBottomBarItem.blur:
        return const BlurSubBottomBar();
      case TransformBottomBarItem.opacity:
        return OpacitySubBottomBar(
          label: context.tr(LKey.common_opacity),
          provider: picTemplateEditorProvider.select((e) => e.value.opacity),
          onChanging: (value) => _onChangeOpacity(context, value),
        );
      case TransformBottomBarItem.filter:
        return Builder(
          builder: (context) {
            return FilterSubBottomBar(
              initialValue: context.watch(picTemplateEditorProvider).value.filter,
              onChanged: (value) {
                context.read(picTemplateEditorProvider.notifier).updateAttributes(
                      filter: value,
                    );
              },
            );
          },
        );
    }
    return SizedBox(
      height: height,
      child: LayoutBuilder(
        builder: (context, constraints) {
          final itemVisiable = isTablet(context) ? itemVisiableTablet : itemVisiableMobile;
          final itemWidth = constraints.maxWidth / itemVisiable;
          return ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: TransformBottomBarItem.values.length,
            itemBuilder: (context, index) {
              final item = TransformBottomBarItem.values[index];
              return BottomAction(
                width: itemWidth,
                item: item,
                automaticallyImplySelection: false,
                onTap: () {
                  if (item.supportBottomBar) {
                    context.read(bottomBarEditorProvider.notifier).selectBottom(item);
                  }

                  switch (item) {
                    case TransformBottomBarItem.crop:
                      _onSelectCropItem(context);
                      break;
                    case TransformBottomBarItem.flip_horizontal:
                      _onSelectFlipItem(context, PicFlipEnum.horizontal);
                      break;
                    case TransformBottomBarItem.flip_vertical:
                      _onSelectFlipItem(context, PicFlipEnum.vertical);
                      break;
                    case TransformBottomBarItem.filter:
                      _onSelectFilterItem(context);
                      break;
                    default:
                  }
                },
              );
            },
          );
        },
      ),
    );
  }

  void _onSelectCropItem(BuildContext context) {
    final template = context.read(picTemplateEditorProvider.notifier).template;
    if (template.ratio != null) {
      final ratioBottomItem = RatioBottomBarItem.values.firstWhereOrNull((element) => element.ratio == template.ratio);
      if (ratioBottomItem != null) {
        context.read(bottomBarEditorProvider.notifier).selectSubBottom(ratioBottomItem);
      }
    }
  }

  void _onSelectFlipItem(BuildContext context, PicFlipEnum? flipEnum) {
    final template = context.read(picTemplateEditorProvider).value;
    if (template.flipEnum != null) {
      flipEnum = template.flipEnum!.replace(flipEnum);
    }

    context.read(picTemplateEditorProvider.notifier).updateAttributes(
          flipEnum: flipEnum,
          flipEnumNullable: true,
        );
  }

  void _onChangeOpacity(BuildContext context, double value) {
    context.read(picTemplateEditorProvider.notifier).updateAttributes(opacity: value);
  }

  void _onSelectFilterItem(BuildContext context) {
    context.read(bottomBarEditorProvider.notifier).selectSubBottom(FilterBottomBarItem.brightness);
  }
}
