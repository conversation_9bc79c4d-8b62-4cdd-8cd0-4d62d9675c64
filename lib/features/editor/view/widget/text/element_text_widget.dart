// Flutter project by quang<PERSON><PERSON><PERSON> (<EMAIL>)

import 'dart:math' show max, min;

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../../../component/util/str_util.dart';
import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../data/data.dart';
import '../../../../../routes/routes.dart';
import '../../../../text_input/model/text_input_intial.dart';
import '../../../../text_input/model/typo_form_state.dart';
import '../../../providers/editor_providers.dart';
import '../../../providers/pic_element_provider.dart';
import '../draggable_widget.dart';
import 'text_normal_painter.dart';
import 'text_on_path_painter.dart';

class ElementTextWidget extends StatelessWidget {
  const ElementTextWidget({
    super.key,
    required this.elementKey,
    this.disable = false,
  });

  final String elementKey;
  final bool disable;

  @override
  Widget build(BuildContext context) {
    final provider = picElementEditorProvider(elementKey);
    final elementEditor = context.read(provider.notifier);
    final element = context.watch(provider) as PicElementText;
    final isSelected = context.watch(picElementSelectionProvider) == element.key;

    TextMetadata metadata = element.metadata;
    return DraggableWidget(
      rect: element.rect.toRect(),
      rotation: element.rotation,
      isSelected: isSelected,
      disable: disable,
      onUpdateRect: (rect, shift, scale) {
        rect = rect.shift(shift);
        if (scale != 1.0) {
          metadata = metadata.copyWith(
            fontSize: min(max(metadata.fontSize * scale, 2), 200),
          );

          if (metadata.hasBend) {
            rect = elementEditor.layoutWithBend(
              element.content,
              style: elementEditor.getTextStyle(metadata),
              bend: metadata.bend!,
              center: rect.center,
            );
          } else {
            final sized = elementEditor.layoutSized(
              element.content,
              style: elementEditor.getTextStyle(metadata),
            );
            rect = Rect.fromCenter(
              center: rect.center,
              width: sized.width,
              height: sized.height,
            );
          }
        }
        return rect;
      },
      onDragEnd: (rect, rotation) {
        elementEditor.updateElementTextAttributes(
          rect: PicRect.fromRect(rect),
          rotation: rotation,
          metadata: metadata,
        );
      },
      onTap: () async {
        if (isSelected) {
          final params = await context.pushNamed(
            Routes.textInput,
            arguments: TextInputParams(
              content: element.content,
              style: TypoFormState(
                textAlign: metadata.textAlign ?? PicElementText.kDefaultMetadata.textAlign!,
                color: metadata.color?.toColor() ?? PicElementText.kDefaultMetadata.color!.toColor(),
                fontEnum: metadata.fontEnum,
              ),
            ),
          );

          if (params is TextInputParams) {
            elementEditor.resizeElementText(
              content: params.content,
              fontEnum: params.style?.fontEnum,
              color: params.style?.color,
              textAlign: params.style?.textAlign,
            );
          }

          return;
        }

        context.read(picElementSelectionProvider.notifier).key = element.key;
      },
      onDuplicate: () {
        context.read(picTemplateEditorProvider.notifier).duplicateElement(element.key);
      },
      onDelete: () {
        context.read(picTemplateEditorProvider.notifier).deleteElement(element.key);
      },
      builder: (rect, rotation, _) {
        return ElementTextViewer(
          element: element,
          rect: rect,
          metadata: metadata,
        );
      },
    );
  }
}

class ElementTextViewer extends StatelessWidget {
  const ElementTextViewer({
    super.key,
    required this.element,
    required this.rect,
    required this.metadata,
  });

  final PicElementText element;
  final Rect rect;
  final TextMetadata metadata;

  @override
  Widget build(BuildContext context) {
    final editorBloc = context.read(picElementEditorProvider(element.key).notifier);
    CustomPainter painter;
    final style = editorBloc.getTextStyle(metadata);
    if (element.metadata.hasBend) {
      final arcResult = editorBloc.getArcResult(
        element.content,
        style: style,
        bend: metadata.bend!,
        center: rect.size.center(Offset.zero),
      );

      painter = TextOnPathPainter(
        content: justLine(element.content),
        style: style,
        foreground: foreground(metadata),
        path: arcResult.getPath(),
        shader: element.metadata.shader?.sweep(
          element.metadata.shader is TextBackground ? rect : arcResult.arcRect,
          startAngle: arcResult.startAngle,
          sweepAngle: arcResult.sweepAngle,
        ),
      );
    } else {
      painter = TextNormalPainter(
        content: element.content,
        style: style,
        foreground: foreground(metadata),
        shader: element.metadata.shader?.linear(Offset.zero & rect.size),
        textAlign: metadata.textAlign,
      );
    }

    Widget child = CustomPaint(
      size: rect.size,
      painter: painter,
    );

    if (element.opacity < 1) {
      return Opacity(
        opacity: element.opacity,
        child: child,
      );
    }

    return child;
  }

  TextStyle? foreground(final TextMetadata metadata) {
    if (metadata.stroke != null || metadata.hasShader) {
      return metadata.fontEnum.builder(
        fontSize: metadata.fontSize,
        color: metadata.color?.toColor(),
      );
    }
    return null;
  }
}
