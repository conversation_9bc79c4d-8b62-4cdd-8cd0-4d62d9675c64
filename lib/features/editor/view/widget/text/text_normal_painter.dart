// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/material.dart';

class TextNormalPainter extends CustomPainter {
  static final TextPainter _painter = TextPainter(textDirection: TextDirection.ltr);

  final String content;
  final TextStyle? style, foreground;
  final Shader? shader;
  final TextAlign? textAlign;

  TextNormalPainter({
    required this.content,
    this.style,
    this.foreground,
    this.shader,
    this.textAlign,
  });

  @override
  void paint(Canvas canvas, Size size) {
    _painter
      ..text = TextSpan(
        text: content,
        style: style?.copyWith(
          foreground: foreground == null && shader != null ? (Paint()..shader = shader) : null,
        ),
      )
      ..textAlign = textAlign ?? TextAlign.center
      ..layout(maxWidth: size.width)
      ..paint(canvas, Offset.zero);

    if (foreground != null) {
      _painter
        ..text = TextSpan(
          text: content,
          style: foreground!.copyWith(
            foreground: shader != null ? (Paint()..shader = shader) : null,
          ),
        )
        ..layout(maxWidth: size.width)
        ..paint(canvas, size.center(Offset(-_painter.width / 2, -_painter.height / 2)));
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
