// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:ui';

import 'package:flutter/material.dart';

class TextOnPathPainter extends CustomPainter {
  static final TextPainter _painter = TextPainter(textDirection: TextDirection.ltr);

  final String content;
  final TextStyle? style, foreground;
  final Path path;
  final Shader? shader;

  TextOnPathPainter({
    super.repaint,
    required this.content,
    required this.style,
    this.foreground,
    required this.path,
    this.shader,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final pathMetrics = path.computeMetrics().toList();

    // if (Configurations.kDevMode) {
    //   canvas
    //     ..drawPath(
    //       path,
    //       Paint()
    //         ..color = Colors.black45
    //         ..strokeWidth = 3
    //         ..style = PaintingStyle.stroke,
    //     )
    //     ..drawCircle(size.center(Offset.zero), 2, Paint()..color = Colors.black45);
    // }

    if (foreground == null && shader != null) {
      canvas.saveLayer(null, Paint());
    }

    _paintContent(
      canvas,
      pathMetrics: pathMetrics,
      style: style!,
    );

    if (foreground != null) {
      if (shader != null) {
        canvas.saveLayer(null, Paint());
      }

      _paintContent(
        canvas,
        pathMetrics: pathMetrics,
        style: foreground!,
      );
    }

    if (shader != null) {
      canvas
        ..drawPaint(
          Paint()
            ..shader = shader
            ..blendMode = BlendMode.srcIn,
        )
        ..restore();
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }

  void _paintContent(
    Canvas canvas, {
    required List<PathMetric> pathMetrics,
    required final TextStyle style,
  }) {
    int currentMetric = 0;
    double currDist = 0;

    for (final char in content.characters) {
      _painter
        ..text = TextSpan(text: char, style: style)
        ..layout();

      final charSize = _painter.size;

      final tangent = pathMetrics[currentMetric].getTangentForOffset(currDist + charSize.width / 2)!;
      final currLetterPos = tangent.position;
      final currLetterAngle = tangent.angle;
      canvas
        ..save()
        ..translate(currLetterPos.dx, currLetterPos.dy)
        ..rotate(-currLetterAngle);

      _painter.paint(
        canvas,
        Offset(
          -charSize.width / 2,
          -charSize.height / 2,
        ),
      );

      canvas.restore();
      currDist += charSize.width;
      if (currDist > pathMetrics[currentMetric].length) {
        currDist = 0;
        currentMetric++;
      }

      if (currentMetric == pathMetrics.length) {
        break;
      }
    }
  }
}
