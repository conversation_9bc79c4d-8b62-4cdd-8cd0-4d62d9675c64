// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../component/mixin/refresher_mixin.dart';
import '../../../configurations/app_error.dart';
import '../repository/image_picker_repository.dart';
import 'image_picker_state.dart';

part 'image_picker_provider.g.dart';

@Riverpod(keepAlive: true)
class ImagePicker extends _$ImagePicker {
  late final ImagePickerRepository _repository;

  @override
  AsyncValue<ImagePickerState> build() {
    _repository = ref.watch(imagePickerRepositoryProvider);
    return const AsyncValue.loading();
  }

  Future<void> loadAssetPaths() async {
    final paths = await _repository.getAssetPaths();
    if (!state.hasValue) {
      state = AsyncValue.data(
        ImagePickerState(
          permissionState: await _repository.getPermissionState(),
          assets: paths,
          selectedEntities: List.empty(growable: true),
          entities: Map.identity(),
        ),
      );
    } else {
      final value = state.value!;
      state = AsyncValue.data(
        value.copyWith(assets: paths),
      );
    }
  }

  Future<void> loadAssetEtities(
    AssetPathEntity path, {
    int start = 0,
    int limit = 20,
    required Refresher refresher,
  }) async {
    try {
      assert(state.hasValue, 'state must has value');
      state = const AsyncValue<ImagePickerState>.loading().copyWithPrevious(state, isRefresh: true);

      final entities = await _repository.getAssetEtities(path, start: start, limit: limit);
      final value = state.value!;
      state = AsyncValue.data(
        value.copyWith(
          entities: Map.from(value.entities)
            ..update(
              path,
              (value) => value + entities,
              ifAbsent: () => entities,
            ),
        ),
      );
    } catch (e, trace) {
      state = AsyncValue<ImagePickerState>.error(e, trace).copyWithPrevious(state);
    } finally {
      refresher.refreshCompleted();
    }
  }

  void changeSelectAssetEntity(AssetEntity entity) {
    assert(state.hasValue, 'state must has value');
    final value = state.value!;
    final entities = List<AssetEntity>.from(value.selectedEntities);
    if (entities.contains(entity)) {
      entities.remove(entity);
    } else {
      entities.add(entity);
    }
    state = AsyncValue.data(value.copyWith(selectedEntities: entities));
  }

  void clearSelectedAssetEntity() {
    assert(state.hasValue, 'state must has value');
    final value = state.value!;
    state = AsyncValue.data(value.copyWith(selectedEntities: []));
  }

  Future<List<String>> submitImagesPicked() async {
    assert(state.hasValue, 'state must has value');
    final value = state.value!;

    List<String?> paths = await Future.wait(value.selectedEntities.map(_repository.cloneAssetPicked));
    paths.removeWhere((e) => e == null);

    if (paths.isEmpty) {
      throw BaseException(code: AppError.unknown.code);
    }
    return paths.cast<String>();
  }

  Future<({bool granted, bool limited})> canAccessPhotos() async {
    final permissionState = await _repository.getPermissionState();
    return (
      granted: permissionState == PermissionState.limited || permissionState == PermissionState.authorized,
      limited: permissionState == PermissionState.limited,
    );
  }

  Future<void> presentLimited() async {
    await _repository.presentLimited();
    if (state.hasValue) {
      final permissionState = await _repository.getPermissionState();
      final value = state.requireValue;
      state = AsyncValue.data(
        value.copyWith(permissionState: permissionState),
      );
    }
  }

  Future<void> openSettings() {
    return _repository.openSettings();
  }

  Future<void> refreshAll() async {
    final paths = await _repository.getAssetPaths();

    Map<AssetPathEntity, List<AssetEntity>> entities = <AssetPathEntity, List<AssetEntity>>{};
    for (final path in paths) {
      entities[path] = await _repository.getAssetEtities(path);
    }

    state = AsyncValue.data(
      ImagePickerState(
        permissionState: await _repository.getPermissionState(),
        assets: paths,
        selectedEntities: state.value?.selectedEntities ?? List.empty(growable: true),
        entities: entities,
      ),
    );
  }
}
