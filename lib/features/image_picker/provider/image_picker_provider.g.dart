// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'image_picker_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$imagePickerHash() => r'0471d3bdc3a744e0c597aee496b0f468591430f8';

/// See also [ImagePicker].
@ProviderFor(ImagePicker)
final imagePickerProvider =
    NotifierProvider<ImagePicker, AsyncValue<ImagePickerState>>.internal(
  ImagePicker.new,
  name: r'imagePickerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$imagePickerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ImagePicker = Notifier<AsyncValue<ImagePickerState>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
