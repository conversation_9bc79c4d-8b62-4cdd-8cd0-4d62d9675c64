// Flutter project by quanghuuxx (<EMAIL>)

import 'package:equatable/equatable.dart';
import 'package:photo_manager/photo_manager.dart';

class ImagePickerState extends Equatable {
  final PermissionState permissionState;
  final List<AssetPathEntity> assets;
  final List<AssetEntity> selectedEntities;
  final Map<AssetPathEntity, List<AssetEntity>> entities;

  ImagePickerState({
    required this.permissionState,
    required this.assets,
    required this.selectedEntities,
    required this.entities,
  });

  factory ImagePickerState.empty() {
    return ImagePickerState(
      permissionState: PermissionState.denied,
      assets: List.empty(growable: true),
      selectedEntities: List.empty(growable: true),
      entities: Map.identity(),
    );
  }

  ImagePickerState copyWith({
    PermissionState? permissionState,
    List<AssetPathEntity>? assets,
    List<AssetEntity>? selectedEntities,
    Map<AssetPathEntity, List<AssetEntity>>? entities,
  }) {
    return ImagePickerState(
      permissionState: permissionState ?? this.permissionState,
      assets: assets ?? this.assets,
      selectedEntities: selectedEntities ?? this.selectedEntities,
      entities: entities ?? this.entities,
    );
  }

  @override
  List<Object> get props => [permissionState, assets, selectedEntities, entities];
}
