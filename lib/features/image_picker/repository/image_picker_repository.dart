// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../component/dependency_injector/di.dart';

part 'image_picker_repository.g.dart';

@riverpod
ImagePickerRepository imagePickerRepository(Ref ref) {
  return it.get();
}

abstract class ImagePickerRepository {
  Future<List<AssetPathEntity>> getAssetPaths();
  Future<List<AssetEntity>> getAssetEtities(
    AssetPathEntity path, {
    int start = 0,
    int limit = 20,
  });

  Future<PermissionState> getPermissionState();

  Future<void> presentLimited();

  Future<void> openSettings();

  Future<String?> cloneAssetPicked(AssetEntity entity);
}
