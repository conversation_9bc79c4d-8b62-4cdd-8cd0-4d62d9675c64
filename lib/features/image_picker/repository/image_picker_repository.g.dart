// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'image_picker_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$imagePickerRepositoryHash() =>
    r'a00910480e00b146eb3c0d800bc38a29a22b88cd';

/// See also [imagePickerRepository].
@ProviderFor(imagePickerRepository)
final imagePickerRepositoryProvider =
    AutoDisposeProvider<ImagePickerRepository>.internal(
  imagePickerRepository,
  name: r'imagePickerRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$imagePickerRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ImagePickerRepositoryRef
    = AutoDisposeProviderRef<ImagePickerRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
