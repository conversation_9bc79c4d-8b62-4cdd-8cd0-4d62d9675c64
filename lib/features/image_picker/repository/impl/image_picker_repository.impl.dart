// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:io';

import 'package:core/core.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:photo_manager/photo_manager.dart';

import '../../../editor/model/image_data.dart';
import '../image_picker_repository.dart';

class ImagePickerRepositoryImpl implements ImagePickerRepository {
  @override
  Future<List<AssetPathEntity>> getAssetPaths() async {
    return PhotoManager.getAssetPathList(type: RequestType.image);
  }

  @override
  Future<List<AssetEntity>> getAssetEtities(
    AssetPathEntity path, {
    int start = 0,
    int limit = 20,
  }) async {
    return path.getAssetListRange(start: start, end: start + limit);
  }

  @override
  Future<PermissionState> getPermissionState() {
    return PhotoManager.getPermissionState(
      requestOption: const PermissionRequestOption(
        androidPermission: AndroidPermission(
          type: RequestType.image,
          mediaLocation: false,
        ),
      ),
    );
  }

  @override
  Future<void> presentLimited() async {
    return PhotoManager.presentLimited(
      type: RequestType.image,
    );
  }

  @override
  Future<void> openSettings() {
    return PhotoManager.openSetting();
  }

  @override
  Future<String?> cloneAssetPicked(AssetEntity entity) async {
    final file = await entity.originFile;
    if (file == null) {
      return null;
    }

    final path = PathUtil.join(PathUtil.documentsPath, entity.id + PathUtil.extension(file.path));
    if (File(path).existsSync()) {
      // preload data image object to visiable in View layer
      await PreloadImageObject.instance.load(path);

      return path;
    }

    XFile? clone = await FlutterImageCompress.compressAndGetFile(
      file.path,
      path,
      format: getCompressFormat(file.path),
    );

    if (clone == null) {
      return null;
    }

    // preload data image object to visiable in View layer
    await PreloadImageObject.instance.load(clone.path);

    return clone.path;
  }

  CompressFormat getCompressFormat(String path) {
    final extension = PathUtil.extension(path);
    switch (extension.toLowerCase()) {
      case '.jpg':
      case '.jpeg':
        return CompressFormat.jpeg;
      case '.png':
        return CompressFormat.png;
      case '.webp':
        return CompressFormat.webp;
      case '.heic':
        return CompressFormat.heic;
      default:
        return CompressFormat.jpeg;
    }
  }
}
