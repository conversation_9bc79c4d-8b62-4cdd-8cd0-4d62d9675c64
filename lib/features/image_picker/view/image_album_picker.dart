// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/material.dart';
import 'package:photo_manager/photo_manager.dart';

import '../../../resource/style/app_theme_ext.dart';
import 'image_page_picker.dart';

class ImageAlbumPicker extends StatefulWidget {
  const ImageAlbumPicker({super.key, required this.paths});

  final List<AssetPathEntity> paths;

  @override
  State<ImageAlbumPicker> createState() => _ImageAlbumPickerState();
}

class _ImageAlbumPickerState extends State<ImageAlbumPicker> with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  late final TabController _tabController;

  @override
  void initState() {
    super.initState();

    _tabController = TabController(
      length: widget.paths.length,
      vsync: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    return Column(
      children: [
        TabBar(
          tabAlignment: TabAlignment.start,
          isScrollable: true,
          controller: _tabController,
          indicatorPadding: EdgeInsets.zero,
          indicatorColor: theme.themeColor.secondary,
          unselectedLabelColor: theme.themeColor.neutral400,
          tabs: widget.paths.map((e) => Tab(text: e.name)).toList(),
          dividerColor: theme.themeColor.neutral300,
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: widget.paths.map((e) => ImagePagePicker(path: e)).toList(),
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;
}
