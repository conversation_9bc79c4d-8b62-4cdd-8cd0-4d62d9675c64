// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:photo_manager/photo_manager.dart';

import '../../../component/mixin/refresher_mixin.dart';
import '../../../component/widget/inherited_consumer.dart';
import '../../../component/widget/refresher_widget.dart';
import '../../../data/model/common/paging_query.dart';
import '../../../resource/style/app_theme_ext.dart';
import '../provider/image_picker_provider.dart';
import 'image_picker_bottomsheet.dart';

class ImagePagePicker extends StatefulWidget {
  const ImagePagePicker({
    super.key,
    required this.path,
    this.wantKeepAlive = false,
  });

  final AssetPathEntity path;
  final bool wantKeepAlive;

  @override
  State<ImagePagePicker> createState() => _ImagePagePickerState();
}

class _ImagePagePickerState extends StateBase<ImagePagePicker> with RefresherStateMixin, AutomaticKeepAliveClientMixin {
  late final ImagePicker imagePicker = context.read(imagePickerProvider.notifier);

  @override
  void onViewCreated() {
    super.onViewCreated();

    if (context.read(imagePickerProvider).value?.entities[widget.path]?.isNotEmpty != true) {
      imagePicker.loadAssetEtities(widget.path, refresher: this);
    }
  }

  @override
  void didUpdateWidget(covariant ImagePagePicker oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.path.id != widget.path.id) {
      imagePicker.loadAssetEtities(widget.path, refresher: this);
    }

    if (widget.wantKeepAlive != oldWidget.wantKeepAlive) {
      updateKeepAlive();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    final entities = context.watch(
      imagePickerProvider.select((e) => e.value?.entities[widget.path]),
      when: (old, state) {
        return old == null || !listEquals(old, state);
      },
    );

    if (entities == null) return const LoadingWidget();

    return RefresherWidget<PagingQuery>(
      initialPaging: PagingQuery.initial,
      refreshController: refreshController,
      onMore: (value) {
        imagePicker.loadAssetEtities(
          widget.path,
          start: value.start,
          limit: value.limit,
          refresher: this,
        );
      },
      child: GridView.builder(
        padding: kPaddingVertical16,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: (isTablet(context) ? kFive : kThree).toInt(),
          crossAxisSpacing: kFive,
          mainAxisSpacing: kFive,
        ),
        itemCount: entities.length,
        itemBuilder: (context, index) {
          final entity = entities[index];

          return FutureBuilder(
            future: entity.thumbnailData,
            builder: (context, snapshot) {
              if (snapshot.hasData) {
                Widget child = Image.memory(
                  snapshot.data!,
                  fit: BoxFit.cover,
                );

                final selectedEntities = context.watch(
                  imagePickerProvider.select((e) => e.value?.selectedEntities ?? const []),
                );
                final selected = selectedEntities.contains(entity);
                if (selected) {
                  child = DecoratedBox(
                    position: DecorationPosition.foreground,
                    decoration: BoxDecoration(
                      color: theme.themeColor.secondary200.withValues(alpha: .45),
                      border: Border.all(color: theme.themeColor.secondary300, width: 4),
                    ),
                    child: child,
                  );
                }

                if (!selected && selectedEntities.length >= context.widgetOf<ImagePickerBottomSheet>().pickCount) {
                  return child;
                }

                child = InkWell(
                  onTap: () {
                    imagePicker.changeSelectAssetEntity(entity);
                  },
                  child: child,
                );
                return child;
              }
              return const Skeleton();
            },
          );
        },
      ),
    );
  }

  @override
  bool get wantKeepAlive => widget.wantKeepAlive;
}
