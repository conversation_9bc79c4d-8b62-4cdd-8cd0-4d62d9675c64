// Flutter project by quanghu<PERSON><PERSON> (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart' show TapGestureRecognizer;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:photo_manager/photo_manager.dart' show PermissionState;

import '../../../component/dependency_injector/di.dart';
import '../../../component/services/alert_dialog_service/alert_dialog_service.dart';
import '../../../component/services/alert_dialog_service/model/loading_dialog.dart';
import '../../../component/widget/inherited_consumer.dart';
import '../../../resource/localization/lkey.dart';
import '../../../resource/style/app_theme_ext.dart';
import '../model/image_picker_tab.dart';
import '../provider/image_picker_provider.dart';
import 'image_album_picker.dart';
import 'image_recent_picker.dart';

class ImagePickerBottomSheet extends StatefulWidget {
  const ImagePickerBottomSheet({
    super.key,
    required this.pickCount,
  });

  final int pickCount;

  @override
  State<ImagePickerBottomSheet> createState() => _ImagePickerBottomSheetState();
}

class _ImagePickerBottomSheetState extends StateBase<ImagePickerBottomSheet> with WidgetsBindingObserver {
  late final ImagePicker imagePicker = context.read(imagePickerProvider.notifier);

  final AlertDialogService alertDialogService = it.get<AlertDialogService>();
  final PageController pageController = PageController();

  bool _isUserChangingPermisison = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void onViewCreated() {
    if (!context.read(imagePickerProvider).hasValue) {
      imagePicker.loadAssetPaths();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // divider
        Container(
          width: kOnehundred,
          height: kSix,
          margin: kPaddingAll8,
          decoration: BoxDecoration(
            color: theme.themeColor.neutral300,
            borderRadius: kBorderRadius12,
          ),
        ),
        kBox8,
        Expanded(
          child: Builder(
            builder: (context) {
              final state = context.watch(
                imagePickerProvider,
                when: (old, state) =>
                    !listEquals(old?.value?.assets, state.value?.assets) ||
                    old?.value?.permissionState != state.value?.permissionState,
              );
              if (state is AsyncLoading) {
                return const LoadingWidget();
              }

              if (!state.hasValue || state.requireValue.assets.isEmpty) {
                return const EmptyState();
              }

              final assets = state.requireValue.assets;
              final isLimited = state.requireValue.permissionState == PermissionState.limited;
              final length = ImagePickerTab.values.length;
              return Column(
                children: [
                  Pagebar(
                    controller: pageController,
                    itemCount: length,
                    itemBuilder: (context, index, selected) {
                      final e = ImagePickerTab.values[index];
                      return PageChip(
                        label: Text(context.tr(e.lkey)),
                        selected: selected,
                        onPressed: () {
                          pageController.jumpToPage(index);
                        },
                      );
                    },
                  ),
                  if (isLimited) ...[
                    kBox8,
                    RichText(
                      text: TextSpan(
                        text: '${context.tr(LKey.photo_access_limited)} ',
                        style: theme.themeText.bodyText1,
                        children: [
                          TextSpan(
                            text: context.tr(LKey.photo_access_change),
                            style: theme.themeText.bodyText1.copyWith(
                              fontWeight: FontWeight.w600,
                              color: theme.themeColor.secondary,
                            ),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                showCupertinoModalPopup(
                                  context: context,
                                  builder: (ctx) {
                                    return CupertinoActionSheet(
                                      actions: [
                                        CupertinoActionSheetAction(
                                          child: Text(ctx.tr(LKey.photo_more_choise)),
                                          onPressed: () {
                                            _isUserChangingPermisison = true;
                                            imagePicker.presentLimited();
                                            ctx.pop();
                                          },
                                        ),
                                        CupertinoActionSheetAction(
                                          child: Text(ctx.tr(LKey.photo_access_change)),
                                          onPressed: () {
                                            imagePicker.openSettings().then((_) {
                                              _isUserChangingPermisison = true;
                                            });
                                            ctx.pop();
                                          },
                                        ),
                                      ],
                                    );
                                  },
                                );
                              },
                          ),
                        ],
                      ),
                    ),
                  ],
                  Expanded(
                    child: Stack(
                      children: [
                        PageView.builder(
                          controller: pageController,
                          itemCount: length,
                          physics: const NeverScrollableScrollPhysics(),
                          itemBuilder: (context, index) {
                            final e = ImagePickerTab.values[index];
                            if (e == ImagePickerTab.recent) {
                              final recent = assets.firstWhereOrNull((e) => e.isAll);
                              if (recent == null) {
                                return const EmptyState();
                              }
                              return ImageRecentPicker(recent: recent);
                            }
                            return ImageAlbumPicker(
                              paths: List.from(assets)..removeWhere((e) => e.isAll),
                            );
                          },
                        ),
                        LayoutBuilder(
                          builder: (context, constraints) {
                            if (context.watch(
                              imagePickerProvider.select(
                                (e) => e.value?.selectedEntities.isNotEmpty ?? false,
                              ),
                            )) {
                              return Align(
                                alignment: const Alignment(0.0, .9),
                                child: Container(
                                  width: constraints.maxWidth * .65,
                                  decoration: BoxDecoration(
                                    color: theme.themeColor.container,
                                    borderRadius: kBorderRadius24,
                                    boxShadow: theme.themeDecoration.boxShadow,
                                  ),
                                  child: Row(
                                    children: [
                                      IconButton(
                                        onPressed: () {
                                          imagePicker.clearSelectedAssetEntity();
                                        },
                                        icon: Icon(
                                          Icons.close,
                                          color: theme.themeColor.onSurface,
                                        ),
                                      ),
                                      const Spacer(),
                                      GhostButton(
                                        color: theme.themeColor.secondary,
                                        onPressed: () {
                                          alertDialogService.add(LoadingDialog());
                                          imagePicker.submitImagesPicked().then((paths) {
                                            alertDialogService.closeCurrentAlert((d) => d is LoadingDialog);
                                            imagePicker.clearSelectedAssetEntity();
                                            Navigator.of(context).pop(paths);
                                          }).catchError((e) {
                                            alertDialogService.closeCurrentAlert((d) => d is LoadingDialog);
                                          });
                                        },
                                        text: context.tr(LKey.common_continue),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }
                            return kBox0;
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ],
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    if (state == AppLifecycleState.resumed) {
      if (_isUserChangingPermisison) {
        _isUserChangingPermisison = false;
        final records = await imagePicker.canAccessPhotos();
        if (records.granted) {
          imagePicker.refreshAll();
        }
      }
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    pageController.dispose();
    super.dispose();
  }
}
