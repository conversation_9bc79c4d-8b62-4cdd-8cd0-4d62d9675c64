// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';
import 'package:photo_manager/photo_manager.dart';

import '../../../resource/localization/lkey.dart';
import '../../../resource/style/app_theme_ext.dart';
import 'image_page_picker.dart';
import 'image_picker_bottomsheet.dart';

class ImageRecentPicker extends StatelessWidget {
  const ImageRecentPicker({
    super.key,
    required this.recent,
  });

  final AssetPathEntity recent;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        kBox12,
        Container(
          height: 36,
          alignment: Alignment.center,
          child: Text(
            context.tr(LKey.image_picker_description, params: [context.widgetOf<ImagePickerBottomSheet>().pickCount]),
            style: theme.themeText.bodyText1.copyWith(
              color: theme.themeColor.neutral800,
            ),
            textAlign: TextAlign.justify,
          ),
        ),
        Expanded(
          child: ImagePagePicker(
            path: recent,
            wantKeepAlive: true,
          ),
        ),
      ],
    );
  }
}
