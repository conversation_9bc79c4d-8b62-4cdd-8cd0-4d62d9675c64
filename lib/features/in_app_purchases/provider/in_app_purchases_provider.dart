// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/services.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../configurations/app_error.dart';
import '../../../configurations/configurations.dart';
import '../../shared/customer_purchased/customer_purchased_provider.dart';

part 'in_app_purchases_provider.g.dart';

@riverpod
class InAppPurchasesProvider extends _$InAppPurchasesProvider {
  @override
  AsyncValue<Offering> build() {
    return const AsyncValue.loading();
  }

  Future<void> initial() async {
    if (await Purchases.isConfigured) {
      return;
    }

    try {
      await Purchases.configure(PurchasesConfiguration(configurations.revenuecatApiKey));
      ref.read(customerPurchasedProvider.notifier).onCustomerInfoUpdated(await Purchases.getCustomerInfo());
    } catch (e, trace) {
      Console.log('InAppPurchasesProvider intial error !!', error: e, stackTrace: trace);
    }
  }

  Future<CustomerInfo> restore() {
    return Purchases.restorePurchases();
  }

  Future<bool> purchase(StoreProduct product) async {
    try {
      await Purchases.purchaseStoreProduct(product);
      return true;
    } catch (e, trace) {
      _transformException(e, trace);
      return false;
    }
  }

  Future<void> fetchOfferings() async {
    try {
      state = const AsyncValue<Offering>.loading().copyWithPrevious(state, isRefresh: true);
      final offerings = await Purchases.getOfferings();
      final offer = offerings.getOffering('subscriptions');
      if (offer == null) {
        state = AsyncValue<Offering>.error(
          BaseException(
            code: AppError.notFound.code,
            description: 'Offering not found',
          ),
          StackTrace.current,
        ).copyWithPrevious(state);
      } else {
        state = AsyncValue.data(offer);
      }
    } catch (e, trace) {
      _transformException(e, trace);
    }
  }

  void _transformException(Object error, StackTrace stackTrace) {
    BaseException exception;

    if (error is PlatformException) {
      final detail = error.details as Map<Object?, Object?>;
      switch (detail['readable_error_code']) {
        case 'PurchaseCancelledError':
          exception = BaseException(
            code: 'PurchaseCancelledError',
            description: error.message,
          );
          break;
        case 'ConfigurationError':
          exception = BaseException(
            code: 'PurchasesConfigurationError',
            description: error.message,
          );
          break;
        case 'PurchaseNotAllowedError':
          exception = BaseException(
            code: 'PurchaseNotAllowedError',
            description: error.message,
          );
        default:
          exception = BaseException(
            code: AppError.unknown.code,
            description: error.message,
          );
      }

      state = AsyncValue<Offering>.error(exception, stackTrace).copyWithPrevious(state);
    }
  }
}
