// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'in_app_purchases_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$inAppPurchasesProviderHash() =>
    r'9ee54f1573197a69e8db2b1321ebafaa3ede9ab4';

/// See also [InAppPurchasesProvider].
@ProviderFor(InAppPurchasesProvider)
final inAppPurchasesProviderProvider = AutoDisposeNotifierProvider<
    InAppPurchasesProvider, AsyncValue<Offering>>.internal(
  InAppPurchasesProvider.new,
  name: r'inAppPurchasesProviderProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$inAppPurchasesProviderHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InAppPurchasesProvider = AutoDisposeNotifier<AsyncValue<Offering>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
