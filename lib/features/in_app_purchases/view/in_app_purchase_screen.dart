// Flutter project by quanghuuxx (<EMAIL>)

// ignore_for_file: unused_element_parameter

import 'package:core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:purchases_flutter/purchases_flutter.dart';

import '../../../component/dependency_injector/di.dart';
import '../../../component/services/alert_dialog_service/alert_dialog_service.dart';
import '../../../component/services/alert_dialog_service/model/error_dialog_info.dart';
import '../../../component/services/alert_dialog_service/model/loading_dialog.dart';
import '../../../component/widget/inherited_consumer.dart';
import '../../../resource/localization/lkey.dart';
import '../../../resource/lottie_constants.dart';
import '../../../resource/style/app_theme_ext.dart';
import '../../shared/customer_purchased/customer_purchased_provider.dart';
import '../provider/in_app_purchases_provider.dart';

class InAppPurchasesScreen extends StatefulWidget {
  const InAppPurchasesScreen({super.key});

  @override
  State<InAppPurchasesScreen> createState() => _InAppPurchasesScreenState();
}

class _InAppPurchasesScreenState extends AppScreenState<InAppPurchasesScreen> with SingleTickerProviderStateMixin {
  late AnimationController _shimmerController;

  final PageController pageController = PageController();
  final AlertDialogService alertDialogService = it.get<AlertDialogService>();

  @override
  void initState() {
    super.initState();

    _shimmerController = AnimationController.unbounded(vsync: this)
      ..repeat(min: -0.5, max: 1.5, period: const Duration(milliseconds: 3000));
  }

  @override
  void onViewCreated() {
    super.onViewCreated();

    if (!context.read(inAppPurchasesProviderProvider).hasValue) {
      context.read(inAppPurchasesProviderProvider.notifier).fetchOfferings();
    }
  }

  @override
  Widget build(BuildContext context) {
    context.listen(inAppPurchasesProviderProvider, (old, state) {
      if (state.hasError) {
        final exception = state.error as BaseException;
        alertDialogService.add(
          ErrorDialogInfo.fromBaseException(
            context,
            exception: exception,
            contents: [
              AlertDialogContent(
                content: context.tr('${exception.code}_desc'),
                type: AlertDialogContentType.text,
                argument: theme.themeText.bodyText1.copyWith(
                  color: Theme.of(context).themeColor.neutral700,
                ),
              ),
            ],
            actions: (!state.hasValue)
                ? [
                    AlertDialogAction(
                      title: context.tr(LKey.common_cancel),
                      action: (context) {
                        context.popPage();
                      },
                    ),
                  ]
                : null,
          ),
        );
      }
    });

    return Scaffold(
      appBar: AppBar(),
      body: context.watch(inAppPurchasesProviderProvider).when(
            skipError: true,
            data: (offering) {
              return Column(
                children: [
                  Expanded(child: _PlanPage(offering: offering)),
                  kBox32,
                  GhostButton(
                    text: context.tr(LKey.iap_restore),
                    onPressed: () {
                      context.read(inAppPurchasesProviderProvider.notifier).restore();
                    },
                    color: theme.themeColor.secondary300,
                  ),
                  kBox4,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      GhostButton(
                        text: context.tr(LKey.common_tems_of_service),
                        onPressed: () {},
                        color: theme.themeColor.neutral600,
                        style: theme.themeText.bodyText1.copyWith(
                          decoration: TextDecoration.underline,
                          fontWeight: FontWeight.w300,
                        ),
                      ),
                      GhostButton(
                        text: context.tr(LKey.common_privacy_policy),
                        onPressed: () {},
                        color: theme.themeColor.neutral600,
                        style: theme.themeText.bodyText1.copyWith(
                          decoration: TextDecoration.underline,
                          fontWeight: FontWeight.w300,
                        ),
                      ),
                    ],
                  ),
                  kBox8,
                ],
              );
            },
            error: (_, __) => const EmptyState(),
            loading: () => const LoadingWidget(),
          ),
    );
  }

  @override
  void dispose() {
    pageController.dispose();
    _shimmerController.dispose();
    super.dispose();
  }

  LinearGradient get gradient => LinearGradient(
        colors: const [
          Color(0x00F4F4F4),
          Color(0xAEF4F4F4),
          Color(0x00F4F4F4),
        ],
        stops: const [0.1, 0.4, 0.6],
        begin: const Alignment(-1.0, -0.3),
        end: const Alignment(1.0, 0.3),
        tileMode: TileMode.clamp,
        transform: _SlidingGradientTransform(
          slidePercent: _shimmerController.value,
        ),
      );

  bool get isSized => (context.findRenderObject() as RenderBox?)?.hasSize ?? false;

  Size get size => (context.findRenderObject() as RenderBox).size;

  Offset getDescendantOffset({
    required final RenderBox? descendant,
    Offset offset = Offset.zero,
  }) {
    final shimmerBox = context.findRenderObject() as RenderBox;
    if (descendant == null) return shimmerBox.localToGlobal(offset);

    return descendant.localToGlobal(offset, ancestor: shimmerBox);
  }
}

class _SlidingGradientTransform extends GradientTransform {
  const _SlidingGradientTransform({required this.slidePercent});

  final double slidePercent;

  @override
  Matrix4? transform(Rect bounds, {TextDirection? textDirection}) {
    return Matrix4.translationValues(bounds.width * slidePercent, 0.0, 0.0);
  }
}

class _Shimmer extends StatelessWidget {
  const _Shimmer({required this.child});

  final Widget child;

  @override
  Widget build(BuildContext context) {
    // Collect ancestor shimmer information.
    final state = context.findAncestorStateOfType<_InAppPurchasesScreenState>()!;
    if (!state.isSized) {
      // The ancestor Shimmer widget isn't laid
      // out yet. Return an empty box.
      return const SizedBox();
    }

    context.listenable(state._shimmerController); // trigger update of shimmer

    final shimmerSize = state.size;
    final gradient = state.gradient;
    final offsetWithinShimmer = state.getDescendantOffset(
      descendant: context.findRenderObject() as RenderBox?,
    );

    return ShaderMask(
      blendMode: BlendMode.srcATop,
      shaderCallback: (bounds) {
        return gradient.createShader(
          Rect.fromLTWH(
            -offsetWithinShimmer.dx,
            -offsetWithinShimmer.dy,
            shimmerSize.width,
            shimmerSize.height,
          ),
        );
      },
      child: child,
    );
  }
}

class _PlanPage extends StatelessWidget {
  const _PlanPage({
    required this.offering,
  });

  final Offering offering;

  @override
  Widget build(BuildContext context) {
    if (offering.availablePackages.isEmpty) {
      return const EmptyState();
    }

    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          kBox32,
          Align(
            child: _Shimmer(
              child: LottieWidget(
                LottiesConstants.crown,
              ),
            ),
          ),
          kBox16,
          Text(
            context.tr(LKey.iap_go_premium),
            style: theme.themeText.headline6.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: 24,
            ),
            textAlign: TextAlign.center,
          ),
          kBox16,
          Text(
            context.tr(LKey.iap_go_premium_desc),
            style: theme.themeText.bodyText0,
            textAlign: TextAlign.center,
          ),
          kBox32,
          ...offering.availablePackages.expandIndexed((i, e) sync* {
            if (i > 0) yield kBox8;
            yield _PackageItem(
              package: e,
              onTap: () {
                it.get<AlertDialogService>().add(LoadingDialog());
                context.read(inAppPurchasesProviderProvider.notifier).purchase(e.storeProduct);
              },
            );
          }),
          kBox16,
          _Privilege(context.tr(LKey.iap_privlige_1)),
          kBox8,
          _Privilege(context.tr(LKey.iap_privlige_2)),
          kBox8,
          _Privilege(context.tr(LKey.iap_privlige_3)),
        ],
      ),
    );
  }
}

class _PackageItem extends StatelessWidget {
  static final weekly = RegExp(r'P(\d+)W');
  static final mothly = RegExp(r'P(\d+)M');
  static final yearly = RegExp(r'P(\d+)Y');

  const _PackageItem({
    required this.package,
    this.onTap,
  });

  final Package package;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final subscribed = context.read(customerPurchasedProvider.notifier).subscribed(package.storeProduct.identifier);
    final freePhase = package.storeProduct.defaultOption?.freePhase;
    final child = Container(
      padding: kPaddingAll16,
      decoration: BoxDecoration(
        color: theme.themeColor.container,
        borderRadius: kBorderRadius12,
        border: Border.all(
          width: kTwo,
          color: theme.themeColor.neutral200,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RichText(
                  text: TextSpan(
                    text: package.storeProduct.priceString,
                    style: theme.themeText.bodyText0.copyWith(
                      fontWeight: FontWeight.w600,
                      fontSize: 22,
                    ),
                    children: [
                      TextSpan(
                        text: '  / ${billingPeriod(context)}',
                        style: theme.themeText.bodyText1.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  maxLines: kOneLine,
                  overflow: TextOverflow.ellipsis,
                ),
                kBox4,
                Text(
                  package.storeProduct.title,
                  style: theme.themeText.bodyText2.copyWith(
                    color: theme.themeColor.neutral800,
                  ),
                  maxLines: kTwoLine,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          kBox8,
          _Shimmer(
            child: PrimaryButton(
              text: subscribed
                  ? context.tr(LKey.iap_subscribed)
                  : (freePhase != null ? context.tr(LKey.iap_free_trial) : context.tr(LKey.iap_subscribe)),
              onPressed: subscribed ? null : onTap,
              maxLines: kTwoLine,
              width: kOnehundred,
              padding: kPaddingAll16,
              color: theme.themeColor.secondary600,
              shape: const RoundedRectangleBorder(borderRadius: kBorderRadius12),
            ),
          ),
        ],
      ),
    );

    if (freePhase != null) {
      return Container(
        padding: kPaddingAll4,
        decoration: BoxDecoration(
          color: theme.themeColor.neutral50,
          borderRadius: kBorderRadius12,
        ),
        child: Column(
          children: [
            PhysicalModel(color: theme.themeColor.neutral50, child: child),
            kBox8,
            _Shimmer(
              child: Row(
                children: [
                  kBox4,
                  Icon(
                    Icons.watch_later,
                    color: theme.themeColor.neutralM,
                  ),
                  kBox8,
                  Expanded(
                    child: Text(
                      context.tr(LKey.iap_free_trial_decs, params: [trialPeriod(context, freePhase)]),
                      style: theme.themeText.bodyText2.copyWith(
                        color: theme.themeColor.neutral800,
                      ),
                    ),
                  ),
                  kBox4,
                ],
              ),
            ),
            kBox4,
          ],
        ),
      );
    }

    return child;
  }

  String trialPeriod(BuildContext context, PricingPhase freePhase) {
    final iso = freePhase.billingPeriod!.iso8601;
    if (weekly.hasMatch(iso)) {
      final weeks = weekly.firstMatch(iso)![1] as String;
      return context.tr(LKey.common_num_week, params: [weeks]);
    } else if (mothly.hasMatch(iso)) {
      final months = mothly.firstMatch(iso)![1] as String;
      return context.tr(LKey.common_num_month, params: [months]);
    } else if (yearly.hasMatch(iso)) {
      final years = yearly.firstMatch(iso)![1] as String;
      return context.tr(LKey.common_num_year, params: [years]);
    } else {
      return '';
    }
  }

  String billingPeriod(BuildContext context) {
    if (package.storeProduct.productCategory != ProductCategory.subscription) {
      return '';
    }

    final billingPeriod = package.storeProduct.defaultOption?.billingPeriod?.iso8601;
    if (billingPeriod == null) {
      return '';
    }

    if (weekly.hasMatch(billingPeriod)) {
      final weeks = weekly.firstMatch(billingPeriod)![1] as String;
      if (weeks == '1') {
        return context.tr(LKey.common_weekly);
      }
      return context.tr(LKey.common_num_week, params: [weeks]);
    } else if (mothly.hasMatch(billingPeriod)) {
      final months = mothly.firstMatch(billingPeriod)![1] as String;
      if (months == '1') {
        return context.tr(LKey.common_monthly);
      }
      return context.tr(LKey.common_num_month, params: [months]);
    } else if (yearly.hasMatch(billingPeriod)) {
      final years = yearly.firstMatch(billingPeriod)![1] as String;
      if (years == '1') {
        return context.tr(LKey.common_yearly);
      }
      return context.tr(LKey.common_num_year, params: [years]);
    } else {
      return '';
    }
  }
}

class _Privilege extends StatelessWidget {
  const _Privilege(
    this.title, {
    this.icon,
    this.style,
  });

  final String title;
  final IconData? icon;
  final TextStyle? style;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Text.rich(
      TextSpan(
        children: [
          WidgetSpan(
            alignment: PlaceholderAlignment.middle,
            child: Icon(
              icon ?? Icons.done_rounded,
              color: theme.themeColor.neutral900,
            ),
          ),
          TextSpan(
            text: '  $title',
            style: style ??
                theme.themeText.bodyText0.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
        ],
      ),
      textAlign: TextAlign.start,
    );
  }
}
