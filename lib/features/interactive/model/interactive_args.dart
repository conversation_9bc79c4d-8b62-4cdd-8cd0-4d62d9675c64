// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:ui';

import '../../../data/model/common/mask_shape.dart';
import '../../../data/model/template/pic_element_mask.dart';
import '../../editor/model/image_filter_fragment.dart';

class MaskInteractiveArgs {
  final Image source;
  final UniForms? uniForms;
  final MaskShape shape;
  final MaskMetadata? metadata;

  MaskInteractiveArgs({
    required this.source,
    this.uniForms,
    required this.shape,
    this.metadata,
  });
}
