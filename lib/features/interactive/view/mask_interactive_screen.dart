// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:ui' as ui;

import 'package:core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vector_math/vector_math_64.dart' show Vector3;

import '../../../data/data.dart';
import '../../../data/model/common/mask_shape.dart';
import '../../../resource/style/app_theme_ext.dart';
import '../../editor/model/image_filter_fragment.dart';
import '../../editor/model/preload_mask_path.dart';
import '../../editor/view/editor_constant.dart';
import '../../editor/view/widget/draggable_widget.dart';
import '../model/interactive_args.dart';

class MaskInteractiveScreen extends StatefulWidget {
  const MaskInteractiveScreen({super.key, required this.args});

  final MaskInteractiveArgs args;

  @override
  State<MaskInteractiveScreen> createState() => _MaskInteractiveScreenState();
}

class _MaskInteractiveScreenState extends State<MaskInteractiveScreen> {
  MaskInteractiveArgs get args => widget.args;

  final ValueUpdater<double> _scaleUpdater = ValueUpdater<double>(1.0, (old, value) => value / old);
  final ValueUpdater<double> _rotationUpdater = ValueUpdater<double>(0.0, (old, value) => value - old);
  final ValueNotifier<Matrix4> transform = ValueNotifier(Matrix4.zero());

  Vector3 intialScale = Vector3.zero();
  Offset initialCenter = Offset.zero;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.sizeOf(context);
    final padding = MediaQuery.viewPaddingOf(context);
    final bounds = kPaddingAll8.deflateSize(Size(size.width, size.width * args.shape.aspectRatio));
    final sourceSize = args.source.size;
    if (transform.value.isZero()) {
      if (sourceSize.shortestSide == sourceSize.width) {
        final x = bounds.width / sourceSize.width;
        transform.value = Matrix4.identity()
          ..translate(bounds.width / 2, bounds.height / 2)
          ..scale(x, x, 1.0)
          ..translate(-sourceSize.width / 2, -sourceSize.height / 2);
      } else {
        final y = bounds.height / sourceSize.height;
        transform.value = Matrix4.identity()
          ..translate(bounds.width / 2, bounds.height / 2)
          ..scale(y, y, 1.0)
          ..translate(-sourceSize.width / 2, -sourceSize.height / 2);
      }

      intialScale = Matrix4Util.getScale(transform.value);
      initialCenter = MatrixUtils.transformPoint(transform.value, sourceSize.center(Offset.zero));

      final metadata = args.metadata;
      if (metadata != null) {
        transform.value *= metadata.toMatrix4(sourceSize.center(Offset.zero));
      }
    }

    return AnnotatedRegion(
      value: SystemUiOverlayStyle.light,
      child: Stack(
        children: [
          Positioned.fill(
            child: ColoredBox(
              color: theme.themeColor.neutral500,
              child: GestureDetector(
                onScaleUpdate: (details) {
                  final scale = _scaleUpdater.update(details.scale);
                  final rotate = _rotationUpdater.update(details.rotation);
                  final clone = transform.value.clone();
                  final center = sourceSize.center(Offset.zero);
                  if (scale != 1.0) {
                    clone
                      ..translate(center.dx, center.dy)
                      ..scale(scale, scale, 1.0)
                      ..translate(-center.dx, -center.dy);
                  }

                  if (rotate != 0.0) {
                    final center = sourceSize.center(Offset.zero);
                    clone
                      ..translate(center.dx, center.dy)
                      ..rotateZ(rotate)
                      ..translate(-center.dx, -center.dy);
                  }

                  final scaled = Matrix4Util.getScale(clone);
                  final shift = MathUtil.rotatePoint(details.focalPointDelta, -Matrix4Util.getRotationZ(clone));
                  transform.value = clone..translate(shift.dx / scaled.x, shift.dy / scaled.y);
                },
                onScaleEnd: (details) {
                  _scaleUpdater.value = 1.0;
                  _rotationUpdater.value = 0.0;
                },
                child: Center(
                  child: CustomPaint(
                    size: bounds,
                    painter: _Painter(
                      image: args.source,
                      uniForms: args.uniForms,
                      transform: transform,
                    ),
                    foregroundPainter: _ForgroundPainter(
                      shape: args.shape,
                      inputRect: Offset.zero & sourceSize,
                      transform: transform,
                    ),
                  ),
                ),
              ),
            ),
          ),
          Container(
            padding: padding,
            height: kToolbarHeight + padding.vertical,
            child: NavigationToolbar(
              leading: IconButton(
                icon: const Icon(
                  Icons.close_rounded,
                  size: kThirtyTwo,
                ),
                color: theme.themeColor.white,
                onPressed: () => Navigator.pop(context),
              ),
              trailing: IconButton(
                icon: const Icon(
                  Icons.done_rounded,
                  size: kThirtyTwo,
                ),
                color: theme.themeColor.white,
                disabledColor: theme.themeColor.neutral600,
                onPressed: () {
                  final scale = Matrix4Util.getScale(transform.value);
                  final rotate = Matrix4Util.getRotationZ(transform.value);
                  final translate =
                      MatrixUtils.transformPoint(transform.value, sourceSize.center(Offset.zero)) - initialCenter;

                  Navigator.pop(
                    context,
                    MaskMetadata(
                      scale: scale.x / intialScale.x,
                      rotate: rotate,
                      translate: PicPosition(
                        x: translate.dx / scale.x,
                        y: translate.dy / scale.y,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _Painter extends CustomPainter {
  final ui.Image image;
  final UniForms? uniForms;
  final ValueNotifier<Matrix4> transform;

  _Painter({
    required this.image,
    this.uniForms,
    required this.transform,
  }) : super(repaint: transform);

  @override
  void paint(ui.Canvas canvas, ui.Size size) {
    final Paint paint = Paint();

    canvas
      ..save()
      ..transform(transform.value.storage);

    if (uniForms != null) {
      canvas.clipRect(Offset.zero & image.size);

      paint
        ..shader = imageFilterFragment.createShader(image, uniForms!)
        ..isAntiAlias = true;
      canvas.drawPaint(paint);
    } else {
      canvas.drawImage(image, Offset.zero, paint);
    }

    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

class _ForgroundPainter extends CustomPainter {
  final MaskShape shape;
  final ValueNotifier<Matrix4> transform;
  final Rect inputRect;

  _ForgroundPainter({
    required this.shape,
    required this.inputRect,
    required this.transform,
  });

  Rect rect = Rect.zero;
  Paint painter = Paint();

  final Color barrierColor = Colors.black54;

  @override
  void paint(Canvas canvas, Size size) {
    rect = Offset.zero & size;
    painter = Paint();

    canvas
      ..saveLayer(null, painter)
      ..save()
      ..transform(transform.value.storage)
      ..drawRect(
        inputRect,
        painter..color = barrierColor,
      )
      ..restore()

      // đục hình dạng của shape
      ..scale(
        size.width / shape.width,
        size.height / shape.height,
      )
      ..drawPath(
        PreloadPathsMaskShape.instance.getCombine(shape.asset),
        painter
          ..color = Colors.transparent
          ..blendMode = ui.BlendMode.clear,
      )
      ..restore()
      ..drawRect(
        rect,
        painter
          ..color = Colors.white
          ..blendMode = BlendMode.srcOver
          ..style = PaintingStyle.stroke,
      );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
