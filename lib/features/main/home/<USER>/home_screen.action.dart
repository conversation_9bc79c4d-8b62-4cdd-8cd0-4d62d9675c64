// Flutter project by quanghuux<PERSON> (<EMAIL>)

part of 'home_screen.dart';

extension HomeScreenAction on _HomeScreenState {
  void _onCustomerPurchasedUpdated(CustomerInfo? old, CustomerInfo? state) {
    if (context.read(customerPurchasedProvider.notifier).isUpgraded(old, state)) {
      // show dialog congratulation user buy premium
      it.get<AlertDialogService>().add(
            AlertDialogInfo(
              contents: [
                AlertDialogContent(
                  content: LottieWidget(
                    LottiesConstants.crown,
                    width: 100,
                    height: 100,
                  ),
                  type: AlertDialogContentType.widget,
                ),
                AlertDialogContent(
                  content: context.tr(LKey.iap_upgraded_to_premium),
                  type: AlertDialogContentType.text,
                  argument: theme.themeText.headline6.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                AlertDialogContent(
                  content: context.tr(LKey.iap_upgraded_to_premium_sub_1),
                  type: AlertDialogContentType.text,
                ),
                AlertDialogContent(
                  content: context.tr(LKey.iap_upgraded_to_premium_sub_2),
                  type: AlertDialogContentType.text,
                ),
              ],
              actions: [
                AlertDialogAction(
                  title: context.tr(LKey.common_ok),
                  action: (context) {
                    if (NavigatorObsService.instance.currentRoute.name == Routes.inAppPurchases) {
                      context.popPage();
                    } else {
                      Navigator.pop(context);
                    }
                  },
                ),
              ],
            ),
          );
    }
  }

  Future<bool> canToPickImage() async {
    bool hasAuth = false;
    if (context.read(imagePickerProvider).value?.assets.isNotEmpty == true) {
      hasAuth = true;
    } else {
      final denied = await requestPermission([Permission.photos]);
      if (denied != null) {
        if (shouldShowRequestRationale(denied)) {
          final shouldOpenSettings = await it.get<AlertDialogService>().add<bool>(
                AlertDialogInfo(
                  contents: [
                    AlertDialogContent(
                      content: context.tr(LKey.require_permssion_to_pick_img),
                      type: AlertDialogContentType.text,
                    ),
                  ],
                  actions: [
                    AlertDialogAction(title: context.tr(LKey.common_skip)),
                    AlertDialogAction(
                      title: context.tr(LKey.open_settings),
                      action: (context) {
                        Navigator.pop(context, true);
                      },
                    ),
                  ],
                ),
              );
          saveShouldShowRequestRationale(denied);
          if (shouldOpenSettings == true) {
            await waitOpenAppSettings();
            hasAuth = await didAuthorization(denied);
          }
        } else {
          openAppSettings();
        }
      } else {
        hasAuth = true;
      }
    }

    return hasAuth;
  }

  void _onCreate(RatioBottomBarItem? ratioItem) {
    canToPickImage().then((hasAuth) async {
      if (hasAuth == false) return;

      final paths = await context.pushNamed(Routes.pickImage);
      if (paths is List<String>) {
        context.safety((context) {
          context.pushNamed(
            Routes.preload,
            arguments: PreloadArguments(
              mode: PreloadMode.creation,
              data: CreationArguments(
                path: paths.first,
                ratio: ratioItem?.ratio,
              ),
            ),
          );
        });
      }
    });
  }
}
