// Flutter project by quanghu<PERSON><PERSON> (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:purchases_flutter/models/customer_info_wrapper.dart' show CustomerInfo;

import '../../../../component/dependency_injector/di.dart';
import '../../../../component/services/alert_dialog_service/alert_dialog_service.dart';
import '../../../../component/widget/inherited_consumer.dart';
import '../../../../configurations/configurations.dart';
import '../../../../resource/image_constants.dart';
import '../../../../resource/localization/lkey.dart';
import '../../../../resource/lottie_constants.dart';
import '../../../../resource/style/app_theme_ext.dart';
import '../../../../routes/routes.dart';
import '../../../editor/model/bottom_bar_enum.dart';
import '../../../image_picker/provider/image_picker_provider.dart';
import '../../../preload/model/preload_arguments.dart';
import '../../../shared/customer_purchased/customer_purchased_provider.dart';
import '../../../shared/request_permision/mixin/request_permission_mixin.dart';
import 'widget/create_options_block.dart';
import 'widget/recent_block.dart';

part 'home_screen.action.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends AppScreenState<HomeScreen> with RequestPermissionMixin {
  late final ImagePicker imagePicker = context.read(imagePickerProvider.notifier);

  @override
  void onViewCreated() {
    super.onViewCreated();

    imagePicker.canAccessPhotos().then((record) {
      if (mounted && record.granted) {
        imagePicker.loadAssetPaths();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: kPaddingAll12,
        child: CustomScrollView(
          slivers: [
            SliverAppBar(
              title: const CoreImage(
                ImageConstants.pic_skecth_logo,
                width: kFortyEight,
                height: kFortyEight,
              ),
              automaticallyImplyLeading: false,
              titleSpacing: 0,
              centerTitle: false,
              pinned: true,
              actions: [
                Builder(
                  builder: (context) {
                    context.listen<CustomerInfo?>(customerPurchasedProvider, _onCustomerPurchasedUpdated);
                    return IconButton(
                      padding: EdgeInsets.zero,
                      icon: LottieWidget(
                        context.watch(customerPurchasedProvider).isPremium
                            ? LottiesConstants.crown
                            : LottiesConstants.crownFreezed,
                      ),
                      onPressed: () => context.pushNamed(Routes.inAppPurchases),
                    );
                  },
                ),
                if (configurations.devMode)
                  IconButton(
                    icon: const Icon(Icons.developer_mode),
                    onPressed: () {
                      context.pushNamed(Routes.dev);
                    },
                  ),
              ],
            ),
            SliverToBoxAdapter(child: CreateOptionsBlock(onTap: _onCreate)),
            const SliverPersistentHeader(delegate: RecentTitle(), pinned: true),
            const RecentBlock(),
          ],
        ),
      ),
    );
  }
}
