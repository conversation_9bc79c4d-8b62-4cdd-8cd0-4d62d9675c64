// Flutter project by quanghuuxx (<EMAIL>)

import 'package:collection/src/iterable_extensions.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../component/dependency_injector/di.dart';
import '../../../../data/data.dart';

part 'templates_stored_provider.g.dart';

@riverpod
Stream<List<PicTemplate>> picTemplatesStored(Ref ref) {
  return it.get<PicTemplateBox>().asStream().map(
        (elements) => elements
            .map((e) => e.toPicTemplate())
            .sorted((a, b) => (b.savedAt ?? 0).compareTo(a.savedAt ?? 0))
            .toList(),
      );
}
