// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'templates_stored_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$picTemplatesStoredHash() =>
    r'e476c493d80e80e78e307e42e71ad139883a9a13';

/// See also [picTemplatesStored].
@ProviderFor(picTemplatesStored)
final picTemplatesStoredProvider =
    AutoDisposeStreamProvider<List<PicTemplate>>.internal(
  picTemplatesStored,
  name: r'picTemplatesStoredProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$picTemplatesStoredHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PicTemplatesStoredRef = AutoDisposeStreamProviderRef<List<PicTemplate>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
