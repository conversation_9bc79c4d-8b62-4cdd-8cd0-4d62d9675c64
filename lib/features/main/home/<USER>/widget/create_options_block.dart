// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../../../resource/localization/lkey.dart';
import '../../../../../resource/style/app_theme_ext.dart';
import '../../../../editor/model/bottom_bar_enum.dart';

class CreateOptionsBlock extends StatelessWidget {
  static const double itemVisiableMobile = 3.5;
  static const double itemVisiableTablet = 6.5;

  const CreateOptionsBlock({super.key, required this.onTap});

  final ValueChanged<RatioBottomBarItem?> onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return LayoutBuilder(
      builder: (context, constraints) {
        final itemVisiable = isTablet(context) ? itemVisiableTablet : itemVisiableMobile;
        final itemWidth = (constraints.maxWidth - (8 * itemVisiable.toInt())) / itemVisiable;

        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            kBox12,
            Text(
              context.tr(LKey.common_ratios_popular),
              style: theme.themeText.headline6.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            kBox16,
            SizedBox(
              height: itemWidth,
              child: ListView.separated(
                scrollDirection: Axis.horizontal,
                itemCount: RatioBottomBarItem.values.length,
                separatorBuilder: (context, index) {
                  return kBox8;
                },
                itemBuilder: (context, index) {
                  final item = RatioBottomBarItem.values[index];
                  final size = _calculateSize(context, item: item);
                  return ElevatedButton(
                    onPressed: () {
                      onTap(item);
                    },
                    style: ElevatedButton.styleFrom(
                      alignment: Alignment.bottomLeft,
                      fixedSize: Size.square(itemWidth),
                      padding: kPaddingAll12,
                      shape: const RoundedRectangleBorder(borderRadius: kBorderRadius12),
                      backgroundColor: theme.byBrightness(
                        light: theme.themeColor.neutral200,
                        dark: theme.themeColor.container,
                      ),
                      elevation: 0,
                      shadowColor: Colors.transparent,
                      overlayColor: theme.themeColor.neutral800,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        if (item == RatioBottomBarItem.origin)
                          Icon(
                            Icons.add_rounded,
                            color: theme.themeColor.neutral600,
                            size: 24,
                          )
                        else
                          Container(
                            width: size.width,
                            height: size.height,
                            decoration: BoxDecoration(
                              borderRadius: kBorderRadius4,
                              border: Border.all(
                                color: theme.themeColor.neutral600,
                                width: 1.5,
                              ),
                            ),
                          ),
                        kBox8,
                        Text(
                          context.tr((item == RatioBottomBarItem.origin) ? LKey.common_create : item.lkey),
                          style: theme.themeText.bodyText1,
                          maxLines: kOneLine,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Size _calculateSize(BuildContext context, {required RatioBottomBarItem item}) {
    return switch (item) {
      RatioBottomBarItem.origin => const Size.square(kEighteen),
      RatioBottomBarItem.ratio_1_1 => const Size.square(kEighteen),
      RatioBottomBarItem.ratio_3_2 || RatioBottomBarItem.ratio_4_3 => Size(kEighteen * item.ratio!, kEighteen),
      RatioBottomBarItem.ratio_3_4 => Size(kTwentyFour * item.ratio!, kTwentyFour),
      RatioBottomBarItem.ratio_5_4 => Size(kThirty, kThirty / item.ratio!),
      RatioBottomBarItem.ratio_16_9 => Size(kThirty, kThirty / item.ratio!),
    };
  }
}
