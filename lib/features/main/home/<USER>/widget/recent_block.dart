// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../../component/widget/inherited_consumer.dart';
import '../../../../../resource/localization/lkey.dart';
import '../../../../../resource/style/app_theme_ext.dart';
import '../../../../../routes/routes.dart';
import '../../../../preload/model/preload_arguments.dart';
import '../../provider/templates_stored_provider.dart';

class RecentTitle extends SliverPersistentHeaderDelegate {
  const RecentTitle();

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    final theme = Theme.of(context);
    final result = context.watch(picTemplatesStoredProvider);
    return SizedBox(
      height: kToolbarHeight,
      child: switch (result) {
        AsyncData(:final value) => value.isEmpty
            ? kBox0
            : Container(
                color: theme.themeColor.background,
                padding: kPaddingVertical16,
                child: Text(
                  context.tr(LKey.common_recent),
                  style: theme.themeText.headline6.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
        _ => kBox0,
      },
    );
  }

  @override
  double get maxExtent => kToolbarHeight;

  @override
  double get minExtent => kToolbarHeight;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}

class RecentBlock extends StatelessWidget {
  const RecentBlock({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final result = context.watch(picTemplatesStoredProvider);
    switch (result) {
      case AsyncData(:final value):
        if (value.isEmpty) {
          return SliverToBoxAdapter(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const EmptyState(),
                kBox16,
                Text(
                  context.tr(LKey.nothing_to_show),
                  style: theme.themeText.headline6.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                kBox16,
                Text(
                  context.tr(LKey.suggest_create_template_to_show_hint),
                  textAlign: TextAlign.center,
                  style: theme.themeText.bodyText0.copyWith(
                    color: theme.themeColor.neutral700,
                  ),
                ),
              ],
            ),
          );
        }
        return SliverGrid.builder(
          addRepaintBoundaries: false,
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            mainAxisSpacing: 8,
            crossAxisSpacing: 8,
          ),
          itemCount: value.length,
          itemBuilder: (context, index) {
            final template = value[index];

            return GestureDetector(
              onTap: () {
                context.pushNamed(
                  Routes.preload,
                  arguments: PreloadArguments(
                    data: template,
                    mode: PreloadMode.update,
                  ),
                );
              },
              child: CoreImage(
                template.buildSnapshot(),
                fit: BoxFit.cover,
                border: Border.all(
                  width: 1.5,
                  color: theme.themeColor.neutral400,
                ),
                borderRadius: kBorderRadius8,
              ),
            );
          },
        );
      default:
        return const _Placeholder();
    }
  }
}

class _Placeholder extends StatelessWidget {
  const _Placeholder();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return SliverToBoxAdapter(
      child: Padding(
        padding: kPaddingAll16,
        child: Row(
          children: [
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: theme.themeColor.neutral400,
                ),
                borderRadius: kBorderRadius16,
              ),
              child: const Skeleton(),
            ),
            kBox8,
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: theme.themeColor.neutral400,
                ),
                borderRadius: kBorderRadius8,
              ),
              child: const Skeleton(),
            ),
          ],
        ),
      ),
    );
  }
}
