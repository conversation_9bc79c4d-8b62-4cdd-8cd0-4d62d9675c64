// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:io';
import 'dart:isolate';
import 'dart:ui';

import 'package:core/core.dart';
import 'package:flutter/foundation.dart';

import '../../../configurations/app_error.dart';
import '../../../data/data.dart';
import '../../editor/funcs/rendering.dart';
import '../../editor/model/image_data.dart';
import '../../editor/model/preload_mask_path.dart';
import '../model/preload_arguments.dart';
import '../repository/preload_repository.dart';

abstract class PreloadInteractor {
  Future<PicEditingTemplate> createPicTemplate(CreationArguments args);

  Future<PicEditingTemplate> loadPicEditingTemplate(final PicTemplate template);
}

class PreloadInteractorImpl implements PreloadInteractor {
  final PreloadRepository repository;

  PreloadInteractorImpl({
    required this.repository,
  });

  @override
  Future<PicEditingTemplate> createPicTemplate(CreationArguments args) async {
    final file = File(args.path);
    if (!file.existsSync()) {
      throw BaseException(
        code: AppError.notFound.code,
        description: 'Can`t find file by path: ${args.path}',
      );
    }

    final uint8list = await compute(_decode, file);
    final data = await PreloadImageObject.instance.loadByBytes(source: args.path, uint8list: uint8list);
    final image = data.image;

    double w = image.width.toDouble();
    double h = image.height.toDouble();
    Rect rect = Rect.fromLTWH(0, 0, w, h);

    if (args.ratio != null) {
      h = w / args.ratio!;
      if (h > image.height) {
        h = image.height.toDouble();
        w = h * args.ratio!;
      }

      rect = Rect.fromCenter(
        center: Offset(image.width / 2, image.height / 2),
        width: w,
        height: h,
      );
    }

    final template = PicEditingTemplate(
      id: repository.nextTemplateSaveId(),
      ratio: args.ratio,
      imageBounds: PicRect.fromRect(rect),
      elements: List.empty(growable: true),
      originImg: args.path,
    );

    if (args.ratio != null) {
      final record = await Isolate.run(() => cropImage(rect, args.path));
      await PreloadImageObject.instance.loadByBytes(
        source: template.identifier,
        uint8list: record.bytes,
        replace: true,
      );
    } else {
      PreloadImageObject.instance.replace(template.identifier, data);
    }

    // đợi các future load font bên trong hoàn thành
    await AppTypography.awaitPendingLoadFonts(TextFontEnum.values.map((e) => e.builder()).toList());

    return template;
  }

  @override
  Future<PicEditingTemplate> loadPicEditingTemplate(PicTemplate original) async {
    final file = File(original.originImg);
    if (!file.existsSync()) {
      throw BaseException(
        code: AppError.notFound.code,
        description: 'Can`t find file by path: ${original.originImg}',
      );
    }

    final uint8list = await compute(_decode, file);
    final data = await PreloadImageObject.instance.loadByBytes(source: file.path, uint8list: uint8list);
    final template = PicEditingTemplate.fromPicTemplate(original);
    if (data.image.width != original.imageBounds.w || data.image.height != original.imageBounds.h) {
      final record = await Isolate.run(() => cropImage(original.imageBounds.toRect(), file.path));
      await PreloadImageObject.instance.loadByBytes(
        source: template.identifier,
        uint8list: record.bytes,
        replace: true,
      );
    } else {
      PreloadImageObject.instance.replace(template.identifier, data);
    }

    for (final ele in template.elements) {
      if (ele is PicElementText) {
        // preload data image của text background
        if (ele.metadata.shader is TextBackground) {
          final background = ele.metadata.shader as TextBackground;
          await PreloadImageObject.instance.load(background.source);
        }
      } else if (ele is PicElementMask) {
        // preload data image và path của mask
        await PreloadImageObject.instance.load(ele.source);
        await PreloadPathsMaskShape.instance.load(ele.shape);
      } else if (ele is PicElementSticker) {
        // preload data image của sticker
        await PreloadImageObject.instance.load(ele.source);
      }
    }

    // đợi các future load font bên trong hoàn thành
    await AppTypography.awaitPendingLoadFonts(TextFontEnum.values.map((e) => e.builder()).toList());

    return template;
  }
}

Future<Uint8List> _decode(final File file) async {
  return file.readAsBytes();
}
