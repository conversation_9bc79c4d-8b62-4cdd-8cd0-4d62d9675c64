// Flutter project by quanghuuxx (<EMAIL>)

import '../../../data/model/template/pic_template.dart';

class PreloadArguments {
  final PreloadMode mode;
  final dynamic data;

  PreloadArguments({required this.mode, required this.data}) {
    assert(mode != PreloadMode.creation || data is CreationArguments);
    assert(mode != PreloadMode.update || data is PicTemplate);
  }
}

enum PreloadMode { creation, update }

class CreationArguments {
  final String path;
  final double? ratio;

  CreationArguments({required this.path, required this.ratio});
}
