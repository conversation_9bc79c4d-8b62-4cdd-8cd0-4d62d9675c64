// Flutter project by quanghuuxx (<EMAIL>)

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../component/dependency_injector/di.dart';
import '../../../data/model/template/pic_template.dart';
import '../interactor/preload_interactor.dart';
import '../model/preload_arguments.dart';

part 'provider.g.dart';

@riverpod
class Preload extends _$Preload {
  late final PreloadInteractor _interactor;

  @override
  PicEditingTemplate? build() {
    _interactor = it.get();
    return null;
  }

  Future<PicEditingTemplate> createPicEditingTemplate(final CreationArguments args) async {
    return state = await _interactor.createPicTemplate(args);
  }

  Future<PicEditingTemplate> loadPicEditingTemplate(final PicTemplate template) async {
    return state = await _interactor.loadPicEditingTemplate(template);
  }
}
