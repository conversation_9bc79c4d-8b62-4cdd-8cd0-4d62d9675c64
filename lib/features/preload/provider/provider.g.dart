// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$preloadHash() => r'4cefb58c553797fa6e4d9fc3240988d7a11c010a';

/// See also [Preload].
@ProviderFor(Preload)
final preloadProvider =
    AutoDisposeNotifierProvider<Preload, PicEditingTemplate?>.internal(
  Preload.new,
  name: r'preloadProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$preloadHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Preload = AutoDisposeNotifier<PicEditingTemplate?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
