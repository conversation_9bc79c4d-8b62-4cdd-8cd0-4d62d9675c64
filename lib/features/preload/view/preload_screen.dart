// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:async';

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../component/dependency_injector/di.dart';
import '../../../component/services/alert_dialog_service/alert_dialog_service.dart';
import '../../../component/services/alert_dialog_service/model/error_dialog_info.dart';
import '../../../component/widget/inherited_consumer.dart';
import '../../../resource/localization/lkey.dart';
import '../../../resource/style/app_theme_ext.dart';
import '../../../routes/routes.dart';
import '../model/preload_arguments.dart';
import '../provider/provider.dart';

class PreloadScreen extends StatefulWidget {
  const PreloadScreen({super.key, required this.arguments});

  final PreloadArguments arguments;

  @override
  State<PreloadScreen> createState() => _PreloadScreenState();
}

class _PreloadScreenState extends AppScreenState<PreloadScreen> {
  late final AlertDialogService alertDialogService = it.get<AlertDialogService>();

  final Completer<Duration> completer = Completer<Duration>();

  @override
  void onViewCreated() {
    super.onViewCreated();

    final notifier = context.read(preloadProvider.notifier);
    switch (widget.arguments.mode) {
      case PreloadMode.creation:
        notifier.createPicEditingTemplate(widget.arguments.data);
        break;
      case PreloadMode.update:
        notifier.loadPicEditingTemplate(widget.arguments.data);
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    context.listen(
      preloadProvider,
      (_, state) async {
        await completer.future.then(Future.delayed);
        context.pushReplacementNamed(Routes.editor, arguments: state);
      },
      onError: (error, trace) {
        alertDialogService.add(
          ErrorDialogInfo.fromBaseException(
            context,
            exception: BaseException.from(error),
            actions: [
              AlertDialogAction(
                title: context.tr(LKey.common_close),
                action: (context) => Navigator.of(context).pop(),
              ),
            ],
          ),
        );
      },
    );

    return Material(
      color: theme.themeColor.background,
      child: LoadingWidget(
        onLoaded: (value) {
          completer.complete(value.duration);
        },
      ),
    );
  }
}
