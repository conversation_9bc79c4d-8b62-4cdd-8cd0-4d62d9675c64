import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../component/widget/inherited_consumer.dart';
import '../../data/model/common/mask_shape.dart';
import '../../data/model/template/pic_element_mask.dart';
import '../../resource/style/app_theme_ext.dart';
import '../editor/model/image_data.dart';
import '../editor/providers/pic_element_provider.dart';
import '../editor/view/widget/sub_bottom_bar/mask_shapes_bottom_bar.dart';

class ShapeCollection extends StatefulWidget {
  const ShapeCollection({super.key});

  @override
  State<ShapeCollection> createState() => _ShapeCollectionState();
}

class _ShapeCollectionState extends AppScreenState<ShapeCollection> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      children: [
        // divider
        Container(
          width: kOnehundred,
          height: kSix,
          margin: kPaddingAll8,
          decoration: BoxDecoration(
            color: theme.themeColor.neutral300,
            borderRadius: kBorderRadius12,
          ),
        ),
        kBox8,
        Expanded(
          child: FutureBuilder(
            future: MaskShapesBottomBar.future,
            builder: (context, snap) {
              if (snap.hasData == false) return const LoadingWidget();

              final key = context.watch(picElementSelectionProvider);
              if (key == null) {
                return const Text('no selection');
              }

              final provider = picElementEditorProvider(key);
              final element = context.watch(provider) as PicElementMask;
              final shapes = snap.data as List<MaskShape>;
              return GridView.builder(
                padding: kPaddingAll8,
                itemCount: shapes.length + 1,
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: isTablet(context) ? 6 : 4,
                  crossAxisSpacing: kEight,
                  mainAxisSpacing: kEight,
                ),
                addRepaintBoundaries: false,
                itemBuilder: (context, index) {
                  if (index == 0) {
                    final data = PreloadImageObject.instance.get(element.source);
                    final shape = MaskShape.fromRect(
                      asset: element.source,
                      rect: Offset.zero & Size(100, data.size.aspectRatio * 100),
                    );

                    return InkWell(
                      onTap: () {
                        Navigator.pop(context, shape);
                      },
                      child: Container(
                        padding: kPaddingAll8,
                        decoration: BoxDecoration(
                          borderRadius: kBorderRadius8,
                          border: Border.all(
                            color: (element.shape.asset == shape.asset)
                                ? theme.themeColor.primary
                                : theme.themeColor.neutral400,
                          ),
                        ),
                        child: Icon(
                          Icons.close_rounded,
                          size: 36,
                          color: theme.themeColor.neutral600,
                        ),
                      ),
                    );
                  }

                  final shape = shapes[index - 1];

                  return InkWell(
                    onTap: () {
                      Navigator.pop(context, shape);
                    },
                    child: Container(
                      padding: kPaddingAll8,
                      decoration: BoxDecoration(
                        borderRadius: kBorderRadius8,
                        border: Border.all(
                          color: (element.shape.asset == shape.asset)
                              ? theme.themeColor.primary
                              : theme.themeColor.neutral400,
                        ),
                      ),
                      child: CoreImage(shape.asset),
                    ),
                  );
                },
              );
            },
          ),
        ),
      ],
    );
  }
}
