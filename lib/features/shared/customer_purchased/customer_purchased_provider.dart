// Flutter project by quanghuuxx (<EMAIL>)

import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'customer_purchased_provider.g.dart';

@Riverpod(keepAlive: true)
class CustomerPurchased extends _$CustomerPurchased {
  @override
  CustomerInfo? build() {
    Purchases.addCustomerInfoUpdateListener(onCustomerInfoUpdated);

    ref.onDispose(() {
      Purchases.removeCustomerInfoUpdateListener(onCustomerInfoUpdated);
    });

    return null;
  }

  void onCustomerInfoUpdated(CustomerInfo customerInfo) {
    state = customerInfo;
  }

  bool subscribed(String sku) {
    return state?.activeSubscriptions.contains(sku) ?? false;
  }

  bool isUpgraded(CustomerInfo? previus, CustomerInfo? next) {
    return next?.isPremium == true &&
        next?.isPremium != previus?.isPremium &&
        next?.originalAppUserId == previus?.originalAppUserId;
  }

  bool isRenewed(CustomerInfo? next) {
    final etitle = next?.entitlements.all['pro_access'];
    return etitle?.isActive == true && etitle!.originalPurchaseDate != etitle.latestPurchaseDate;
  }

  bool isRestored(CustomerInfo? previus, CustomerInfo? next) {
    return next?.isPremium == true && next?.originalAppUserId != previus?.originalAppUserId;
  }
}

extension Ext on CustomerInfo? {
  bool get isPremium => isActive('pro_access');

  bool isActive(String indentifier) {
    return this?.entitlements.all[indentifier]?.isActive ?? false;
  }
}
