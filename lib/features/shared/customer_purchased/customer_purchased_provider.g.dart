// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_purchased_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$customerPurchasedHash() => r'1a182d0b955a1f148c0f97fec1976506279c1b17';

/// See also [CustomerPurchased].
@ProviderFor(CustomerPurchased)
final customerPurchasedProvider =
    NotifierProvider<CustomerPurchased, CustomerInfo?>.internal(
  CustomerPurchased.new,
  name: r'customerPurchasedProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$customerPurchasedHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CustomerPurchased = Notifier<CustomerInfo?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
