// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:async';
import 'dart:io';

import 'package:core/core.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../../component/dependency_injector/di.dart';
import '../service/request_permission_service.dart';

mixin RequestPermissionMixin on WidgetsBindingObserver {
  Completer<void>? _openSettingCompleter;
  bool _openedAppSetting = false;

  /// trả về quyền bị từ chối nếu có
  Future<Permission?> requestPermission(
    List<Permission> permissions,
  ) async {
    assert(permissions.isNotEmpty, 'permissions must not be empty');

    for (final permission in permissions) {
      final per = _validPermission(permission);
      if (await didAuthorization(per)) {
        continue;
      }

      final status = await per.request();
      if (!status.isLimited && !status.isGranted) {
        return per;
      }
    }
    return null;
  }

  /// x<PERSON> lý kiểm tra quyền bị decrepted trên các version của các platform
  /// và thay thế bằng các quyền khả dụng khác.
  Permission _validPermission(Permission per) {
    if (Platform.isAndroid) {
      if (per == Permission.photos) {
        final version = int.parse(PackageInfo.instance.device.version);
        if (version <= 12) {
          return Permission.storage;
        }
      }
    }

    return per;
  }

  /// mở setting app, trả về 1 future sẽ được hoàn thành khi user quay trở lại app
  Future<void> waitOpenAppSettings() async {
    _openedAppSetting = await openAppSettings();
    if (_openedAppSetting) {
      _openSettingCompleter = Completer<void>();
      return _openSettingCompleter!.future;
    }
    return;
  }

  @mustCallSuper
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
      if (_openedAppSetting) {
        _openedAppSetting = false;
        _openSettingCompleter!.complete();
        _openSettingCompleter = null;
      }
    }
  }

  Future<bool> didAuthorization(Permission permission) async {
    final status = await _validPermission(permission).status;
    return status.isGranted || status.isLimited;
  }

  bool shouldShowRequestRationale(Permission permission) {
    return it.get<RequestPermissionService>().shouldShowRequestRationale(permission);
  }

  Future<bool> saveShouldShowRequestRationale(Permission permission, {bool shown = true}) {
    return it.get<RequestPermissionService>().saveShouldShowRequestRationale(permission, shown);
  }
}
