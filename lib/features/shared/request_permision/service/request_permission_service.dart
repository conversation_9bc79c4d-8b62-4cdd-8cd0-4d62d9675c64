// Flutter project by quanghuuxx (<EMAIL>)

import 'package:permission_handler/permission_handler.dart';

import '../../../../data/data.dart';

abstract class RequestPermissionService {
  bool shouldShowRequestRationale(Permission permission);

  Future<bool> saveShouldShowRequestRationale(Permission permission, bool shouldShow);
}

class RequestPermissionServiceImpl implements RequestPermissionService {
  final AppPreferences preferences;

  RequestPermissionServiceImpl({required this.preferences});

  @override
  bool shouldShowRequestRationale(Permission permission) {
    return preferences.getBool('${AppPreferences.shouldShowPermissionRequestRationaleKey}_${permission.value}') ?? true;
  }

  @override
  Future<bool> saveShouldShowRequestRationale(Permission permission, bool shown) {
    return preferences.setBool('${AppPreferences.shouldShowPermissionRequestRationaleKey}_${permission.value}', shown);
  }
}
