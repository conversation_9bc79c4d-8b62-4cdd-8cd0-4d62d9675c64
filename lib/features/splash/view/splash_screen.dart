// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../component/widget/inherited_consumer.dart';
import '../../../resource/image_constants.dart';
import '../../../routes/routes.dart';
import '../../in_app_purchases/provider/in_app_purchases_provider.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends AppScreenState<SplashScreen> {
  static const Duration kMinDuration = Duration(seconds: 3);

  @override
  void onViewCreated() {
    super.onViewCreated();

    final start = DateTime.now();
    final nav = Navigator.of(context);
    context
        .read(inAppPurchasesProviderProvider.notifier)
        .initial()
        .timeout(
          const Duration(seconds: 5),
          onTimeout: () {},
        )
        .then((_) async {
      final duration = DateTime.now().difference(start);
      if (duration < kMinDuration) {
        await Future.delayed(kMinDuration - duration);
      }
      nav.pushReplacementNamed(Routes.home);
    });
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return Scaffold(
      body: Center(
        child: CoreImage(
          ImageConstants.pic_skecth_logo,
          width: size.width * .55,
          fit: BoxFit.fitWidth,
        ),
      ),
    );
  }
}
