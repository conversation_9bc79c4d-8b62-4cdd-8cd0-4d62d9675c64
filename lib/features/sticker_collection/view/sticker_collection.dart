// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:ui' show ImageFilter;

import 'package:core/core.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

import '../../../component/services/remote_config/remote_config_service.dart';
import '../../../component/services/remote_config/remote_configuration.dart';
import '../../../data/model/remote_config/sticker_collection.dart';
import '../../../resource/icon_constants.dart';
import '../../../resource/localization/lkey.dart';
import '../../../resource/style/app_theme_ext.dart';
import '../../editor/model/image_data.dart';

class StickerCollectionScreen extends StatefulWidget {
  const StickerCollectionScreen({super.key});

  @override
  State<StickerCollectionScreen> createState() => _StickerCollectionScreenState();
}

class _StickerCollectionScreenState extends AppScreenState<StickerCollectionScreen> {
  static const double childAspectRatio = 1.0;
  static const double axisSpacing = 8;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final list = RemoteConfigService.instance
        .get<List<dynamic>>(RemoteConfiguration.stickerCollections)
        .map((e) => StickerCollection.fromJson(e as Map<String, dynamic>))
        .toList();

    final padding = MediaQuery.paddingOf(context);
    return Material(
      color: Colors.transparent,
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: kTen, sigmaY: kTen),
        child: Column(
          children: [
            SizedBox(height: padding.top),
            Container(
              padding: kPaddingHorizontal8,
              height: kToolbarHeight,
              child: Row(
                children: <Widget>[
                  InkWell(
                    onTap: () => Navigator.pop(context),
                    child: CoreImage(
                      IconConstants.ic_arrow_left,
                      width: 24,
                      color: theme.themeColor.white,
                    ),
                  ),
                  const Spacer(),
                  Text.rich(
                    TextSpan(
                      text: context.tr(LKey.design_by),
                      style: theme.themeText.bodyText2.copyWith(
                        color: theme.themeColor.neutral500,
                      ),
                      children: [
                        TextSpan(
                          text: ' Freepik',
                          style: theme.themeText.bodyText2.copyWith(
                            color: Colors.blue,
                            fontWeight: FontWeight.bold,
                          ),
                          recognizer: TapGestureRecognizer()..onTap = () {},
                        ),
                        const WidgetSpan(child: kBox8),
                      ],
                    ),
                    textAlign: TextAlign.end,
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView.builder(
                itemCount: list.length,
                itemBuilder: (context, index) {
                  final collection = list[index];

                  return Column(
                    children: [
                      Text(
                        collection.label,
                        style: theme.textTheme.titleLarge,
                      ),
                      kBox16,
                      SizedBox(
                        height: _calculateHeight(collection.stickers.length),
                        child: GridView.count(
                          physics: const NeverScrollableScrollPhysics(),
                          padding: kPaddingAll12,
                          crossAxisCount: 4,
                          mainAxisSpacing: axisSpacing,
                          crossAxisSpacing: axisSpacing,
                          childAspectRatio: childAspectRatio,
                          children: collection.stickers.map((e) {
                            return InkWell(
                              onTap: () {
                                context.pop(e);
                              },
                              child: CoreImage(
                                e,
                                borderRadius: kBorderRadius12,
                                onLoaded: (value) {
                                  if (value != null) {
                                    PreloadImageObject.instance.loadByUIImage(e, value.image);
                                  }
                                },
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                      kBox16,
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // caculate heigth for gridview
  double _calculateHeight(int count) {
    final sized = MediaQuery.sizeOf(context);

    double size = sized.width - kPaddingAll12.horizontal;
    size -= (4 - 1) * axisSpacing;
    size /= 4;
    size /= childAspectRatio;

    final row = (count / 4).ceil();
    return size * row + (row - 1) * axisSpacing + kPaddingAll12.vertical;
  }
}
