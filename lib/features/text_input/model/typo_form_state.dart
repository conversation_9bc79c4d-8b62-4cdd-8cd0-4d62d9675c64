// Flutter project by quanghuuxx (<EMAIL>)

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../../data/data.dart';

class TypoFormState extends Equatable {
  static final TypoFormState defaultState = TypoFormState(
    textAlign: TextAlign.center,
    color: const Color(0xffd4d4d8),
    fontEnum: TextFontEnum.inter,
  );

  final TextAlign textAlign;
  final Color color;
  final TextFontEnum fontEnum;

  TypoFormState({
    required this.textAlign,
    required this.color,
    required this.fontEnum,
  });

  TypoFormState copyWith({
    TextAlign? textAlign,
    Color? color,
    TextFontEnum? fontEnum,
  }) {
    return TypoFormState(
      textAlign: textAlign ?? this.textAlign,
      color: color ?? this.color,
      fontEnum: fontEnum ?? this.fontEnum,
    );
  }

  @override
  List<Object> get props => [textAlign, color, fontEnum];
}
