// Flutter project by quanghuuxx (<EMAIL>)

import '../../../resource/icon_constants.dart';
import '../../../resource/localization/lkey.dart';

enum TypoFormType {
  font,
  color,
  adjust;

  String get lkey {
    return switch (this) {
      font => LKey.text_opt_font,
      color => LKey.text_opt_color,
      adjust => LKey.text_opt_adjustment,
    };
  }

  String get icon {
    return switch (this) {
      font => IconConstants.ic_font,
      color => IconConstants.ic_brush,
      adjust => throw UnimplementedError(),
    };
  }
}
