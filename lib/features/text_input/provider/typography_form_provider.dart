// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:ui';

import 'package:core/core.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../data/model/enum/text_font_enum.dart';
import '../model/text_input_intial.dart';
import '../model/typo_form_state.dart';

part 'typography_form_provider.g.dart';

@riverpod
class TypographyForm extends _$TypographyForm {
  @override
  TypoFormState build() {
    final params = NavigatorObsService.instance.currentRoute.arguments as TextInputParams?;
    return params?.style ?? TypoFormState.defaultState;
  }

  void update({
    TextAlign? textAlign,
    Color? color,
    TextFontEnum? fontEnum,
  }) {
    state = state.copyWith(
      textAlign: textAlign,
      color: color,
      fontEnum: fontEnum,
    );
  }
}
