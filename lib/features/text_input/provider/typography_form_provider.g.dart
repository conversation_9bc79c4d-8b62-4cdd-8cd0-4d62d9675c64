// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'typography_form_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$typographyFormHash() => r'7d6aa8041bc493a40a418a6fad4bb941b5067858';

/// See also [TypographyForm].
@ProviderFor(TypographyForm)
final typographyFormProvider =
    AutoDisposeNotifierProvider<TypographyForm, TypoFormState>.internal(
  TypographyForm.new,
  name: r'typographyFormProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$typographyFormHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TypographyForm = AutoDisposeNotifier<TypoFormState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
