// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:ui';

import 'package:core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../component/widget/inherited_consumer.dart';
import '../../../resource/localization/lkey.dart';
import '../../../resource/style/app_theme_ext.dart';
import '../model/text_input_intial.dart';
import '../provider/typography_form_provider.dart';
import 'typography_form_field.dart';

class TextInputScreen extends StatefulWidget {
  const TextInputScreen({super.key, required this.initial});

  final TextInputParams? initial;

  @override
  State<TextInputScreen> createState() => _TextInputScreenState();
}

class _TextInputScreenState extends AppScreenState<TextInputScreen> {
  late final TextEditingController controller = TextEditingController(text: widget.initial?.content);

  final FocusNode focusNode = FocusNode();

  @override
  void onViewCreated() {
    super.onViewCreated();

    focusNode.requestFocus();
  }

  @override
  Widget build(BuildContext context) {
    final padding = MediaQuery.paddingOf(context);
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
      ),
      child: Material(
        color: Colors.transparent,
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 24, sigmaY: 24),
          child: Column(
            children: [
              SizedBox(height: padding.top),
              SizedBox(
                height: kToolbarHeight,
                child: NavigationToolbar(
                  leading: IconButton(
                    icon: const Icon(
                      Icons.close_rounded,
                      size: kThirtyTwo,
                    ),
                    color: theme.themeColor.white,
                    onPressed: () => Navigator.pop(context),
                  ),
                  trailing: Builder(
                    builder: (context) {
                      final str = context.listenable(controller).text.trim();
                      return IconButton(
                        icon: const Icon(
                          Icons.done_rounded,
                          size: kThirtyTwo,
                        ),
                        color: theme.themeColor.white,
                        disabledColor: theme.themeColor.neutral600,
                        onPressed: str.isEmpty && widget.initial?.content.isNotEmpty == true
                            ? null
                            : () {
                                focusNode.unfocus();
                                final state = context.read(typographyFormProvider);
                                Navigator.pop(
                                  context,
                                  TextInputParams(
                                    content: str.isNotEmpty ? str : context.tr(LKey.element_text_guideline),
                                    style: state,
                                  ),
                                );
                              },
                      );
                    },
                  ),
                ),
              ),
              Expanded(
                child: Padding(
                  padding: kPaddingAll16,
                  child: Builder(
                    builder: (ctx) {
                      final state = ctx.watch(typographyFormProvider);
                      return Center(
                        child: TextField(
                          focusNode: focusNode,
                          controller: controller,
                          maxLines: null,
                          textAlign: state.textAlign,
                          style: state.fontEnum.builder(
                            color: state.color,
                          ),
                          cursorColor: theme.themeColor.neutral400,
                          decoration: InputDecoration.collapsed(
                            hintText: context.tr(LKey.text_input_hint),
                            hintStyle: state.fontEnum.builder(
                              color: Colors.white54,
                            ),
                            fillColor: Colors.transparent,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
              kBox8,
              const TypographyFormField(),
              kBox4,
              SizedBox(height: MediaQuery.viewInsetsOf(context).bottom),
              kBox16,
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    controller.dispose();
    focusNode.dispose();
    super.dispose();
  }
}
