// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../../component/widget/inherited_consumer.dart';
import '../../../data/model/enum/text_font_enum.dart';
import '../../../resource/icon_constants.dart';
import '../../../resource/style/app_theme_ext.dart';
import '../../../resource/style/color/app_theme_color.dart';
import '../../editor/view/widget/sub_bottom_bar/color_select_bottom_bar.dart';
import '../model/typo_form_type.dart';
import '../provider/typography_form_provider.dart';

class TypographyFormField extends StatefulWidget {
  const TypographyFormField({super.key});

  @override
  State<TypographyFormField> createState() => _TypographyFormFieldState();
}

class _TypographyFormFieldState extends State<TypographyFormField> {
  final PageController controller = PageController();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          height: 48,
          child: PageView.builder(
            physics: const NeverScrollableScrollPhysics(),
            itemCount: TypoFormType.values.length,
            controller: controller,
            itemBuilder: (context, index) {
              final value = TypoFormType.values[index];
              switch (value) {
                case TypoFormType.font:
                  return const _TypoFonts();
                case TypoFormType.color:
                  return const _TypoColors();
                default:
                  return kBox0;
              }
            },
          ),
        ),
        kBox12,
        Pagebar(
          controller: controller,
          itemCount: TypoFormType.values.length,
          itemBuilder: (context, index, selected) {
            final value = TypoFormType.values[index];
            switch (value) {
              case TypoFormType.font:
              case TypoFormType.color:
                return InkWell(
                  onTap: () {
                    controller.jumpToPage(index);
                  },
                  child: Container(
                    padding: kPaddingAll8,
                    decoration: BoxDecoration(
                      color: selected ? Colors.black45 : null,
                      borderRadius: kBorderRadius8,
                    ),
                    child: CoreImage(
                      value.icon,
                      color: theme.themeColor.white,
                      width: kTwentyFour,
                      height: kTwentyFour,
                    ),
                  ),
                );
              case TypoFormType.adjust:
                return InkWell(
                  onTap: () {
                    final align = _nextTextAlign(context.read(typographyFormProvider).textAlign);
                    context.read(typographyFormProvider.notifier).update(textAlign: align);
                  },
                  child: Builder(
                    builder: (context) {
                      final textAlign = context.watch(typographyFormProvider).textAlign;
                      return CoreImage(
                        switch (textAlign) {
                          TextAlign.left => IconConstants.ic_textalign_left,
                          TextAlign.right => IconConstants.ic_textalign_right,
                          _ => IconConstants.ic_textalign_center,
                        },
                        color: theme.themeColor.white,
                        width: kTwentyFour,
                        height: kTwentyFour,
                      );
                    },
                  ),
                );
            }
          },
        ),
      ],
    );
  }

  TextAlign _nextTextAlign(TextAlign textAlign) {
    switch (textAlign) {
      case TextAlign.left:
        return TextAlign.center;
      case TextAlign.right:
        return TextAlign.left;
      case TextAlign.center:
        return TextAlign.right;
      default:
        return TextAlign.center;
    }
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }
}

class _TypoFonts extends StatelessWidget {
  const _TypoFonts();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final shadow = Shadow(
      color: theme.themeColor.neutral800,
      offset: Offset.zero,
      blurRadius: 4,
    );

    final state = context.watch(typographyFormProvider);
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: kPaddingHorizontal8,
      child: Row(
        children: TextFontEnum.values.expandIndexed(
          (i, value) sync* {
            if (i > 0) {
              yield kBox8;
            }

            final isSelected = value == state.fontEnum;
            yield Builder(
              builder: (context) {
                if (isSelected) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    Scrollable.ensureVisible(context, duration: kAnimationDuration, alignment: 0.5);
                  });
                }

                return InkWell(
                  onTap: () {
                    context.read(typographyFormProvider.notifier).update(fontEnum: value);
                  },
                  child: Container(
                    padding: kPaddingAll8,
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.black45 : null,
                      borderRadius: kBorderRadius8,
                    ),
                    child: Text(
                      value.family.replaceAll('_', ' '),
                      textAlign: TextAlign.center,
                      style: value.builder(
                        color: theme.themeColor.white,
                        shadows: isSelected ? null : [shadow],
                      ),
                    ),
                  ),
                );
              },
            );
          },
        ).toList(),
      ),
    );
  }
}

class _TypoColors extends StatefulWidget {
  static const double itemVisiableMobile = 9.5;
  static const double itemVisiableTablet = 14.5;

  const _TypoColors();

  @override
  State<_TypoColors> createState() => _TypoColorsState();
}

class _TypoColorsState extends State<_TypoColors> {
  ScrollController? controller;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final itemCount = ColorSelectBottomBar.kColors.length;
        final itemVisiable = isTablet(context) ? _TypoColors.itemVisiableTablet : _TypoColors.itemVisiableMobile;
        final maxWidth = constraints.maxWidth - kPaddingHorizontal8.left;
        final itemWidth = (maxWidth - kEight * itemVisiable.toInt()) / itemVisiable;

        _initScrollController(itemWidth, itemVisiable, maxWidth);

        return ListView.separated(
          scrollDirection: Axis.horizontal,
          padding: kPaddingHorizontal8,
          controller: controller,
          itemCount: itemCount,
          separatorBuilder: (context, index) => kBox8,
          itemBuilder: (context, index) {
            final color = ColorSelectBottomBar.kColors[index];
            final isSelected = context.watch(typographyFormProvider).color == color;

            return InkWell(
              onTap: () {
                context.read(typographyFormProvider.notifier).update(color: color);
              },
              child: Container(
                width: itemWidth,
                height: itemWidth,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                  border: isSelected
                      ? Border.all(
                          color: highlightColor(index, Theme.of(context).themeColor),
                          width: 3,
                        )
                      : null,
                ),
                child: SizedBox.square(dimension: itemWidth),
              ),
            );
          },
        );
      },
    );
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }

  Color highlightColor(int index, AppThemeColor theme) {
    switch (index) {
      case 4:
      case 5:
      case >= 9 && <= 15:
      case >= 25 && <= 27:
        return theme.neutral900;
      default:
        return theme.neutral50;
    }
  }

  void _initScrollController(double itemWidth, double itemVisiable, double maxWidth) {
    if (controller != null) return;

    final state = context.read(typographyFormProvider);
    final index = ColorSelectBottomBar.kColors.indexOf(state.color);
    if (index == -1) {
      controller = ScrollController();
      return;
    }

    final maxExtentCount = ColorSelectBottomBar.kColors.length - itemVisiable;
    final maxExtent = (maxExtentCount) * itemWidth + (maxExtentCount.toInt()) * kEight;

    double offset = (itemWidth * index + (index * kEight));
    if (offset > maxExtent) {
      offset = maxExtent + kPaddingHorizontal8.right;
    } else {
      offset -= (maxWidth / 2) + (itemWidth / 2) - (itemVisiable / 2).toInt() * kEight;
    }

    if (offset < 0) {
      offset = 0;
    }

    controller = ScrollController(initialScrollOffset: offset);
  }
}
