{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:1543252694969374555", "lastPropertyId": "18:5353459754254945194", "name": "PicTemplateEntity", "properties": [{"id": "1:6084362355691800471", "name": "id", "type": 6, "flags": 129}, {"id": "2:6669099726936010521", "name": "originImg", "type": 9}, {"id": "3:7458936358328498693", "name": "opacity", "type": 8}, {"id": "4:2115067212980758895", "name": "ratio", "type": 8}, {"id": "5:2348301437387334109", "name": "rotation", "type": 8}, {"id": "7:2463458839544369439", "name": "snapshot", "type": 9}, {"id": "12:116225578002804070", "name": "nameFlipEnum", "type": 9}, {"id": "13:8365098303000947154", "name": "jsonImageBounds", "type": 9}, {"id": "14:5028156904816775105", "name": "jsonBlur", "type": 9}, {"id": "15:3280719014802991277", "name": "json<PERSON><PERSON><PERSON>", "type": 9}, {"id": "16:4769298762242856109", "name": "jsonElements", "type": 30}, {"id": "17:4913489968767337361", "name": "saveAt", "type": 6}, {"id": "18:5353459754254945194", "name": "saveStatus", "type": 9}], "relations": []}], "lastEntityId": "1:1543252694969374555", "lastIndexId": "0:0", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [], "retiredIndexUids": [], "retiredPropertyUids": [7248839515514870486, 2549166685160768747, 3763763026038162146, 115400859661808850, 4474326109682176349], "retiredRelationUids": [], "version": 1}