// GENERATED CODE - DO NOT MODIFY BY HAND
// This code was generated by ObjectBox. To update it run the generator again
// with `dart run build_runner build`.
// See also https://docs.objectbox.io/getting-started#generate-objectbox-code

// ignore_for_file: camel_case_types, depend_on_referenced_packages
// coverage:ignore-file

import 'dart:typed_data';

import 'package:flat_buffers/flat_buffers.dart' as fb;
import 'package:objectbox/internal.dart'
    as obx_int; // generated code can access "internal" functionality
import 'package:objectbox/objectbox.dart' as obx;
import 'package:objectbox_flutter_libs/objectbox_flutter_libs.dart';

import 'data/entity/pic_template_entity.dart';

export 'package:objectbox/objectbox.dart'; // so that callers only have to import this file

final _entities = <obx_int.ModelEntity>[
  obx_int.ModelEntity(
      id: const obx_int.IdUid(1, 1543252694969374555),
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON>Entity',
      lastPropertyId: const obx_int.IdUid(18, 5353459754254945194),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 6084362355691800471),
            name: 'id',
            type: 6,
            flags: 129),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 6669099726936010521),
            name: 'originImg',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 7458936358328498693),
            name: 'opacity',
            type: 8,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 2115067212980758895),
            name: 'ratio',
            type: 8,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 2348301437387334109),
            name: 'rotation',
            type: 8,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(7, 2463458839544369439),
            name: 'snapshot',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(12, 116225578002804070),
            name: 'nameFlipEnum',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(13, 8365098303000947154),
            name: 'jsonImageBounds',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(14, 5028156904816775105),
            name: 'jsonBlur',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(15, 3280719014802991277),
            name: 'jsonFilter',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(16, 4769298762242856109),
            name: 'jsonElements',
            type: 30,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(17, 4913489968767337361),
            name: 'saveAt',
            type: 6,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(18, 5353459754254945194),
            name: 'saveStatus',
            type: 9,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[])
];

/// Shortcut for [obx.Store.new] that passes [getObjectBoxModel] and for Flutter
/// apps by default a [directory] using `defaultStoreDirectory()` from the
/// ObjectBox Flutter library.
///
/// Note: for desktop apps it is recommended to specify a unique [directory].
///
/// See [obx.Store.new] for an explanation of all parameters.
///
/// For Flutter apps, also calls `loadObjectBoxLibraryAndroidCompat()` from
/// the ObjectBox Flutter library to fix loading the native ObjectBox library
/// on Android 6 and older.
Future<obx.Store> openStore(
    {String? directory,
    int? maxDBSizeInKB,
    int? maxDataSizeInKB,
    int? fileMode,
    int? maxReaders,
    bool queriesCaseSensitiveDefault = true,
    String? macosApplicationGroup}) async {
  await loadObjectBoxLibraryAndroidCompat();
  return obx.Store(getObjectBoxModel(),
      directory: directory ?? (await defaultStoreDirectory()).path,
      maxDBSizeInKB: maxDBSizeInKB,
      maxDataSizeInKB: maxDataSizeInKB,
      fileMode: fileMode,
      maxReaders: maxReaders,
      queriesCaseSensitiveDefault: queriesCaseSensitiveDefault,
      macosApplicationGroup: macosApplicationGroup);
}

/// Returns the ObjectBox model definition for this project for use with
/// [obx.Store.new].
obx_int.ModelDefinition getObjectBoxModel() {
  final model = obx_int.ModelInfo(
      entities: _entities,
      lastEntityId: const obx_int.IdUid(1, 1543252694969374555),
      lastIndexId: const obx_int.IdUid(0, 0),
      lastRelationId: const obx_int.IdUid(0, 0),
      lastSequenceId: const obx_int.IdUid(0, 0),
      retiredEntityUids: const [],
      retiredIndexUids: const [],
      retiredPropertyUids: const [
        7248839515514870486,
        2549166685160768747,
        3763763026038162146,
        115400859661808850,
        4474326109682176349
      ],
      retiredRelationUids: const [],
      modelVersion: 5,
      modelVersionParserMinimum: 5,
      version: 1);

  final bindings = <Type, obx_int.EntityDefinition>{
    PicTemplateEntity: obx_int.EntityDefinition<PicTemplateEntity>(
        model: _entities[0],
        toOneRelations: (PicTemplateEntity object) => [],
        toManyRelations: (PicTemplateEntity object) => {},
        getId: (PicTemplateEntity object) => object.id,
        setId: (PicTemplateEntity object, int id) {
          object.id = id;
        },
        objectToFB: (PicTemplateEntity object, fb.Builder fbb) {
          final originImgOffset = fbb.writeString(object.originImg);
          final snapshotOffset = object.snapshot == null
              ? null
              : fbb.writeString(object.snapshot!);
          final nameFlipEnumOffset = object.nameFlipEnum == null
              ? null
              : fbb.writeString(object.nameFlipEnum!);
          final jsonImageBoundsOffset = fbb.writeString(object.jsonImageBounds);
          final jsonBlurOffset = object.jsonBlur == null
              ? null
              : fbb.writeString(object.jsonBlur!);
          final jsonFilterOffset = fbb.writeString(object.jsonFilter);
          final jsonElementsOffset = fbb.writeList(
              object.jsonElements.map(fbb.writeString).toList(growable: false));
          final saveStatusOffset = object.saveStatus == null
              ? null
              : fbb.writeString(object.saveStatus!);
          fbb.startTable(19);
          fbb.addInt64(0, object.id);
          fbb.addOffset(1, originImgOffset);
          fbb.addFloat64(2, object.opacity);
          fbb.addFloat64(3, object.ratio);
          fbb.addFloat64(4, object.rotation);
          fbb.addOffset(6, snapshotOffset);
          fbb.addOffset(11, nameFlipEnumOffset);
          fbb.addOffset(12, jsonImageBoundsOffset);
          fbb.addOffset(13, jsonBlurOffset);
          fbb.addOffset(14, jsonFilterOffset);
          fbb.addOffset(15, jsonElementsOffset);
          fbb.addInt64(16, object.saveAt);
          fbb.addOffset(17, saveStatusOffset);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final idParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);
          final saveAtParam =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 36);
          final saveStatusParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 38);
          final originImgParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 6, '');
          final opacityParam =
              const fb.Float64Reader().vTableGet(buffer, rootOffset, 8, 0);
          final ratioParam = const fb.Float64Reader()
              .vTableGetNullable(buffer, rootOffset, 10);
          final rotationParam =
              const fb.Float64Reader().vTableGet(buffer, rootOffset, 12, 0);
          final nameFlipEnumParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 26);
          final snapshotParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 16);
          final jsonImageBoundsParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGet(buffer, rootOffset, 28, '');
          final jsonBlurParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 30);
          final jsonFilterParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 32, '');
          final jsonElementsParam = const fb.ListReader<String>(
                  fb.StringReader(asciiOptimization: true),
                  lazy: false)
              .vTableGet(buffer, rootOffset, 34, []);
          final object = PicTemplateEntity(
              id: idParam,
              saveAt: saveAtParam,
              saveStatus: saveStatusParam,
              originImg: originImgParam,
              opacity: opacityParam,
              ratio: ratioParam,
              rotation: rotationParam,
              nameFlipEnum: nameFlipEnumParam,
              snapshot: snapshotParam,
              jsonImageBounds: jsonImageBoundsParam,
              jsonBlur: jsonBlurParam,
              jsonFilter: jsonFilterParam,
              jsonElements: jsonElementsParam);

          return object;
        })
  };

  return obx_int.ModelDefinition(model, bindings);
}

/// [PicTemplateEntity] entity fields to define ObjectBox queries.
class PicTemplateEntity_ {
  /// See [PicTemplateEntity.id].
  static final id =
      obx.QueryIntegerProperty<PicTemplateEntity>(_entities[0].properties[0]);

  /// See [PicTemplateEntity.originImg].
  static final originImg =
      obx.QueryStringProperty<PicTemplateEntity>(_entities[0].properties[1]);

  /// See [PicTemplateEntity.opacity].
  static final opacity =
      obx.QueryDoubleProperty<PicTemplateEntity>(_entities[0].properties[2]);

  /// See [PicTemplateEntity.ratio].
  static final ratio =
      obx.QueryDoubleProperty<PicTemplateEntity>(_entities[0].properties[3]);

  /// See [PicTemplateEntity.rotation].
  static final rotation =
      obx.QueryDoubleProperty<PicTemplateEntity>(_entities[0].properties[4]);

  /// See [PicTemplateEntity.snapshot].
  static final snapshot =
      obx.QueryStringProperty<PicTemplateEntity>(_entities[0].properties[5]);

  /// See [PicTemplateEntity.nameFlipEnum].
  static final nameFlipEnum =
      obx.QueryStringProperty<PicTemplateEntity>(_entities[0].properties[6]);

  /// See [PicTemplateEntity.jsonImageBounds].
  static final jsonImageBounds =
      obx.QueryStringProperty<PicTemplateEntity>(_entities[0].properties[7]);

  /// See [PicTemplateEntity.jsonBlur].
  static final jsonBlur =
      obx.QueryStringProperty<PicTemplateEntity>(_entities[0].properties[8]);

  /// See [PicTemplateEntity.jsonFilter].
  static final jsonFilter =
      obx.QueryStringProperty<PicTemplateEntity>(_entities[0].properties[9]);

  /// See [PicTemplateEntity.jsonElements].
  static final jsonElements = obx.QueryStringVectorProperty<PicTemplateEntity>(
      _entities[0].properties[10]);

  /// See [PicTemplateEntity.saveAt].
  static final saveAt =
      obx.QueryIntegerProperty<PicTemplateEntity>(_entities[0].properties[11]);

  /// See [PicTemplateEntity.saveStatus].
  static final saveStatus =
      obx.QueryStringProperty<PicTemplateEntity>(_entities[0].properties[12]);
}
