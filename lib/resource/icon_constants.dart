// Flutter project by quanghuu.xx (<EMAIL>)

abstract class IconConstants {
  static const ic_adjustment = 'assets/icons/ic_adjustment.png';
  static const ic_blur = 'assets/icons/ic_blur.png';
  static const ic_flip_vertical = 'assets/icons/ic_flip_vertical.png';
  static const ic_shadow = 'assets/icons/ic_shadow.png';
  static const ic_crop = 'assets/icons/ic_crop.png';
  static const ic_vibrance = 'assets/icons/ic_vibrance.png';
  static const ic_exposure = 'assets/icons/ic_exposure.png';
  static const ic_arrow_left = 'assets/icons/ic_arrow_left.png';
  static const ic_pencil = 'assets/icons/ic_pencil.png';
  static const replace_img = 'assets/icons/replace_img.png';
  static const ic_textalign_right = 'assets/icons/ic_textalign_right.png';
  static const ic_style = 'assets/icons/ic_style.png';
  static const ic_opacity = 'assets/icons/ic_opacity.png';
  static const ic_sticker = 'assets/icons/ic_sticker.png';
  static const ic_flip_horizontal = 'assets/icons/ic_flip_horizontal.png';
  static const ic_bend = 'assets/icons/ic_bend.png';
  static const ic_bezier = 'assets/icons/ic_bezier.png';
  static const ic_ratio = 'assets/icons/ic_ratio.png';
  static const ic_sketch = 'assets/icons/ic_sketch.png';
  static const ic_color = 'assets/icons/ic_color.png';
  static const ic_temperature = 'assets/icons/ic_temperature.png';
  static const ic_filter = 'assets/icons/ic_filter.png';
  static const ic_share = 'assets/icons/ic_share.png';
  static const ic_gradient = 'assets/icons/ic_gradient.png';
  static const ic_sticker_circle = 'assets/icons/ic_sticker_circle.png';
  static const ic_rotate = 'assets/icons/ic_rotate.png';
  static const ic_text = 'assets/icons/ic_text.png';
  static const ic_tint = 'assets/icons/ic_tint.png';
  static const ic_save = 'assets/icons/ic_save.png';
  static const ic_gallery_add = 'assets/icons/ic_gallery_add.png';
  static const ic_brightness = 'assets/icons/ic_brightness.png';
  static const ic_textalign_center = 'assets/icons/ic_textalign_center.png';
  static const ic_shapes = 'assets/icons/ic_shapes.png';
  static const ic_layers = 'assets/icons/ic_layers.png';
  static const ic_contrast = 'assets/icons/ic_contrast.png';
  static const ic_highlight = 'assets/icons/ic_highlight.png';
  static const ic_font = 'assets/icons/ic_font.png';
  static const ic_stroke = 'assets/icons/ic_stroke.png';
  static const ic_brush = 'assets/icons/ic_brush.png';
  static const ic_transform = 'assets/icons/ic_transform.png';
  static const ic_saturation = 'assets/icons/ic_saturation.png';
  static const ic_gallery = 'assets/icons/ic_gallery.png';
  static const ic_textalign_left = 'assets/icons/ic_textalign_left.png';
}