// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:async';
import 'dart:ui';

import 'package:core/core.dart';

class AppLocalizations {
  static const Locale defaultLocale = Locale('en');

  // ignore: library_private_types_in_public_api
  static final _AppLocalizationDeletgate delegate = _AppLocalizationDeletgate();
}

class _AppLocalizationDeletgate extends AppLocalizationDelegate {
  static const List<Locale> _supportedLocales = [
    Locale('en'),
    Locale('vi'),
  ];

  @override
  bool isSupported(Locale locale) {
    return supportedLocales.any((e) => e.languageCode == locale.languageCode);
  }

  @override
  FutureOr<List<String>> assets(Locale locale) {
    return [
      'assets/l10n/${locale.languageCode}_lang.json',
      'assets/l10n/${locale.languageCode}_error_lang.json',
    ];
  }

  @override
  List<Locale> get supportedLocales => _supportedLocales;
}
