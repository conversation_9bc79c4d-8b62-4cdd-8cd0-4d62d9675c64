// This file is generated by l10n_generate(created by quanghuu.xx).
//
// Last modified: 2025-05-01T08:31:15.135915

// ignore_for_file: constant_identifier_names
abstract class LKey {
  static const String hello_world = 'hello_world';
  static const String no_more_data = 'no_more_data';
  static const String open_settings = 'open_settings';
  static const String dont_display_again = 'dont_display_again';
  static const String common_ok = 'common_ok';
  static const String common_continue = 'common_continue';
  static const String common_cancel = 'common_cancel';
  static const String common_confirm = 'common_confirm';
  static const String common_skip = 'common_skip';
  static const String common_close = 'common_close';
  static const String common_recent = 'common_recent';
  static const String common_album = 'common_album';
  static const String common_origin = 'common_origin';
  static const String common_crop = 'common_crop';
  static const String common_rotate = 'common_rotate';
  static const String common_blur = 'common_blur';
  static const String common_opacity = 'common_opacity';
  static const String common_flip_horizontal = 'common_flip_horizontal';
  static const String common_flip_vertical = 'common_flip_vertical';
  static const String common_bend = 'common_bend';
  static const String common_shadow = 'common_shadow';
  static const String common_stroke = 'common_stroke';
  static const String common_distance = 'common_distance';
  static const String common_angle = 'common_angle';
  static const String common_rotation = 'common_rotation';
  static const String common_color = 'common_color';
  static const String common_shape = 'common_shape';
  static const String common_add_new = 'common_add_new';
  static const String common_replace = 'common_replace';
  static const String common_create = 'common_create';
  static const String common_collection = 'common_collection';
  static const String common_ratios_popular = 'common_ratios_popular';
  static const String nothing_to_show = 'nothing_to_show';
  static const String design_by = 'design_by';
  static const String suggest_create_template_to_show_hint = 'suggest_create_template_to_show_hint';
  static const String image_picker_description = 'image_picker_description';
  static const String require_permssion_to_pick_img = 'require_permssion_to_pick_img';
  static const String edit_sketch_bottom = 'edit_sketch_bottom';
  static const String edit_ratio_bottom = 'edit_ratio_bottom';
  static const String edit_filter_bottom = 'edit_filter_bottom';
  static const String edit_transform_bottom = 'edit_transform_bottom';
  static const String edit_text_bottom = 'edit_text_bottom';
  static const String edit_sticker_bottom = 'edit_sticker_bottom';
  static const String edit_mask_bottom = 'edit_mask_bottom';
  static const String filter_opt_brightness = 'filter_opt_brightness';
  static const String filter_opt_contrast = 'filter_opt_contrast';
  static const String filter_opt_saturation = 'filter_opt_saturation';
  static const String filter_opt_exposure = 'filter_opt_exposure';
  static const String filter_opt_vibrance = 'filter_opt_vibrance';
  static const String filter_opt_temperature = 'filter_opt_temperature';
  static const String filter_opt_tint = 'filter_opt_tint';
  static const String filter_opt_shadow = 'filter_opt_shadow';
  static const String filter_opt_highlight = 'filter_opt_highlight';
  static const String filter_opt_rbg = 'filter_opt_rbg';
  static const String blur_sigma_x = 'blur_sigma_x';
  static const String blur_sigma_y = 'blur_sigma_y';
  static const String rotation_tool_tip = 'rotation_tool_tip';
  static const String text_opt_add = 'text_opt_add';
  static const String text_opt_adjustment = 'text_opt_adjustment';
  static const String text_opt_color = 'text_opt_color';
  static const String text_opt_style = 'text_opt_style';
  static const String text_opt_font = 'text_opt_font';
  static const String text_opt_gradient = 'text_opt_gradient';
  static const String text_input_hint = 'text_input_hint';
  static const String text_stroke_width = 'text_stroke_width';
  static const String element_text_guideline = 'element_text_guideline';
  static const String iap_restore = 'iap_restore';
  static const String iap_go_premium = 'iap_go_premium';
  static const String iap_go_premium_desc = 'iap_go_premium_desc';
  static const String iap_privlige_1 = 'iap_privlige_1';
  static const String iap_privlige_2 = 'iap_privlige_2';
  static const String iap_privlige_3 = 'iap_privlige_3';
  static const String iap_free_trial = 'iap_free_trial';
  static const String iap_free_trial_decs = 'iap_free_trial_decs';
  static const String iap_subscribe = 'iap_subscribe';
  static const String iap_subscribed = 'iap_subscribed';
  static const String iap_upgraded_to_premium = 'iap_upgraded_to_premium';
  static const String iap_upgraded_to_premium_sub_1 = 'iap_upgraded_to_premium_sub_1';
  static const String iap_upgraded_to_premium_sub_2 = 'iap_upgraded_to_premium_sub_2';
  static const String common_weekly = 'common_weekly';
  static const String common_monthly = 'common_monthly';
  static const String common_yearly = 'common_yearly';
  static const String common_num_week = 'common_num_week';
  static const String common_num_month = 'common_num_month';
  static const String common_num_year = 'common_num_year';
  static const String common_premium = 'common_premium';
  static const String common_exit = 'common_exit';
  static const String common_save = 'common_save';
  static const String common_watch_ad = 'common_watch_ad';
  static const String common_upgrade = 'common_upgrade';
  static const String common_tems_of_service = 'common_tems_of_service';
  static const String common_privacy_policy = 'common_privacy_policy';
  static const String photo_access_limited = 'photo_access_limited';
  static const String photo_access_change = 'photo_access_change';
  static const String photo_more_choise = 'photo_more_choise';
  static const String editor_back_alert = 'editor_back_alert';
  static const String editor_not_availiable_export_slot = 'editor_not_availiable_export_slot';
  static const String ERUNOW = 'ERUNOW';
  static const String ConfigurationError = 'ConfigurationError';
  static const String ConfigurationError_desc = 'ConfigurationError_desc';
  static const String PurchaseNotAllowedError = 'PurchaseNotAllowedError';
  static const String PurchaseNotAllowedError_desc = 'PurchaseNotAllowedError_desc';
  static const String PurchaseCancelledError = 'PurchaseCancelledError';
  static const String PurchaseCancelledError_desc = 'PurchaseCancelledError_desc';
}