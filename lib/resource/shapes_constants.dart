// Flutter project by quanghuuxx (<EMAIL>)

abstract class ShapesConstants {
  static const star_1 = 'assets/shapes/star_1.png';
  static const ovalla = 'assets/shapes/ovalla.png';
  static const star_2 = 'assets/shapes/star_2.png';
  static const clover = 'assets/shapes/clover.png';
  static const rrect = 'assets/shapes/rrect.png';
  static const pattern_2 = 'assets/shapes/pattern_2.png';
  static const pattern_3 = 'assets/shapes/pattern_3.png';
  static const heart_1 = 'assets/shapes/heart_1.png';
  static const abstract = 'assets/shapes/abstract.png';
  static const could = 'assets/shapes/could.png';
  static const puzzle = 'assets/shapes/puzzle.png';
  static const isometric = 'assets/shapes/isometric.png';
  static const circle = 'assets/shapes/circle.png';
  static const polygon = 'assets/shapes/polygon.png';
  static const pattern = 'assets/shapes/pattern.png';
  static const spades = 'assets/shapes/spades.png';
  static const triangle = 'assets/shapes/triangle.png';
  static const rect = 'assets/shapes/rect.png';
  static const heart = 'assets/shapes/heart.png';
  static const pentagon = 'assets/shapes/pentagon.png';
  static const glyph = 'assets/shapes/glyph.png';
  static const lotus = 'assets/shapes/lotus.png';
  static const flower = 'assets/shapes/flower.png';
  static const octagon = 'assets/shapes/octagon.png';
  static const diamond = 'assets/shapes/diamond.png';
  static const star = 'assets/shapes/star.png';
  static const abstract_2 = 'assets/shapes/abstract_2.png';
  static const hexagonal = 'assets/shapes/hexagonal.png';
  static const mandala = 'assets/shapes/mandala.png';
}