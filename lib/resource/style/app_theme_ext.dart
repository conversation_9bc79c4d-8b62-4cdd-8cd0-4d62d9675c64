// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';

import 'package:flutter/material.dart';

import 'color/app_theme_color.dart';
import 'decoration/app_theme_decoration.dart';
import 'text/app_theme_text.dart';

extension AppThemeExt on ThemeData {
  AppThemeColor get themeColor {
    return appThemeColor<AppThemeColor>();
  }

  AppThemeText get themeText {
    return appThemeText<AppThemeText>()!;
  }

  AppThemeDecoration get themeDecoration {
    return appThemeDecoration<AppThemeDecoration>()!;
  }
}
