// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:ui';

import 'package:core/core.dart';
import 'package:flutter/src/material/theme_data.dart';
import 'package:flutter/src/painting/gradient.dart';

abstract class AppThemeColor extends IAppThemeColor {
  @override
  Color get black => const Color(0xff1B1C1D);

  @override
  ThemeExtension<IAppThemeColor> copyWith() {
    return this;
  }

  @override
  // TODO: implement g1
  Gradient? get g1 => throw UnimplementedError();

  @override
  // TODO: implement g2
  Gradient? get g2 => throw UnimplementedError();

  @override
  // TODO: implement g3
  Gradient? get g3 => throw UnimplementedError();

  @override
  // TODO: implement g4
  Gradient? get g4 => throw UnimplementedError();

  @override
  // TODO: implement g5
  Gradient? get g5 => throw UnimplementedError();

  @override
  // TODO: implement g6
  Gradient? get g6 => throw UnimplementedError();

  @override
  Color get neutral100 => const Color(0xfff4f4f7);

  @override
  Color get neutral200 => const Color(0xffe9e9ec);

  @override
  Color get neutral300 => const Color(0xffdfdfe2);

  @override
  Color get neutral400 => const Color(0xffd4d4d8);

  @override
  Color get neutral50 => const Color(0xfffbfbfe);

  @override
  Color get neutral500 => const Color(0xffcacace);

  @override
  Color get neutral600 => const Color(0xffbfbfc3);

  @override
  Color get neutral700 => const Color(0xffa0a0a3);

  @override
  Color get neutral800 => const Color(0xff6e6e71);

  @override
  Color get neutral900 => const Color(0xff2e2e30);

  @override
  Color get hint => neutral400;

  @override
  ThemeExtension<IAppThemeColor> lerp(
    covariant ThemeExtension<IAppThemeColor>? other,
    double t,
  ) {
    return this;
  }

  @override
  Color get neutralE => const Color(0xffED2828);

  @override
  Color get neutralM => const Color(0xffF3CF1E);

  @override
  Color get neutralS => const Color(0xff0AC92A);

  @override
  Color get onPrimary => white;

  @override
  Color get onSecondary => white;

  @override
  Color get primary => primary400;

  @override
  Color get primary100 => const Color(0xfffbc9bd);

  @override
  Color get primary200 => const Color(0xfff8a693);

  @override
  Color get primary300 => const Color(0xfff68469);

  @override
  Color get primary400 => const Color(0xfff56949);

  @override
  Color get primary50 => const Color(0xfff9e8e7);

  @override
  Color get primary500 => const Color(0xfff44d2c);

  @override
  Color get primary600 => const Color(0xffe94828);

  @override
  Color get primary700 => const Color(0xffdb4124);

  @override
  Color get primary800 => const Color(0xffcd3b20);

  @override
  Color get primary900 => const Color(0xffb42e17);

  @override
  Color get secondary => secondary600;

  @override
  Color get secondary100 => const Color(0xffddc0e9);

  @override
  Color get secondary200 => const Color(0xffc797dc);

  @override
  Color get secondary300 => const Color(0xffb16fcd);

  @override
  Color get secondary400 => const Color(0xffa051c1);

  @override
  Color get secondary50 => const Color(0xfff1e6f6);

  @override
  Color get secondary500 => const Color(0xff9038b6);

  @override
  Color get secondary600 => const Color(0xff8234af);

  @override
  Color get secondary700 => const Color(0xff6f2fa6);

  @override
  Color get secondary800 => const Color(0xff5d2a9d);

  @override
  Color get secondary900 => const Color(0xff3c228d);

  @override
  Object get type => AppThemeColor;

  @override
  Color get white => const Color(0xffffffff);

  Color get container;

  Color get onContainer;
}
