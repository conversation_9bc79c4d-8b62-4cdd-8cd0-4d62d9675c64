// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:ui';

import 'app_theme_color.dart';

class DarkColor extends AppThemeColor {
  DarkColor._();

  static final DarkColor instance = DarkColor._();

  @override
  Color get background => const Color(0xff1E1E1E);

  @override
  Color get container => const Color(0xFF40403E);

  @override
  Color get onBackground => const Color(0xffFFFFFF);

  @override
  Color get onContainer => const Color(0xffFFFFFF);

  @override
  Color get onSurface => const Color(0xffFFFFFF);

  @override
  Color get surface => const Color(0xFF2C2C2C);

  @override
  Color get trench => neutral300;

  @override
  Color get disabled => const Color(0xFF6E6E6D);
}
