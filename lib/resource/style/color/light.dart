// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:ui';

import 'app_theme_color.dart';

class LightColor extends AppThemeColor {
  LightColor._();

  static final LightColor instance = LightColor._();

  @override
  Color get background => const Color(0xFFF4F3F9);

  @override
  Color get onBackground => const Color(0xff1B1C1D);

  @override
  Color get onSurface => const Color(0xff1B1C1D);

  @override
  Color get surface => const Color(0xffFFFFFF);

  @override
  Color get trench => neutral600;

  @override
  Color get container => const Color(0xffFAFAFA);

  @override
  Color get onContainer => const Color(0xff1B1C1D);

  @override
  Color get disabled => neutral300;
}
