// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../color/app_theme_color.dart';

class AppThemeDecoration extends IAppThemeDecoration {
  final List<BoxShadow> boxShadow;

  factory AppThemeDecoration.withThemeColor(AppThemeColor themeColor) {
    return AppThemeDecoration._(
      boxShadow: [
        BoxShadow(
          color: const Color(0xFF000000).withValues(alpha: .4),
          offset: const Offset(0, 1),
          blurRadius: 1.5,
        ),
      ],
    );
  }

  AppThemeDecoration._({required this.boxShadow});

  @override
  ThemeExtension<IAppThemeDecoration> copyWith() {
    return this;
  }

  @override
  ThemeExtension<IAppThemeDecoration> lerp(covariant ThemeExtension<IAppThemeDecoration>? other, double t) {
    return this;
  }

  @override
  Object get type => AppThemeDecoration;
}
