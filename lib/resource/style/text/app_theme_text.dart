// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../color/app_theme_color.dart';

typedef TextStyleBuilder = TextStyle Function({
  Color? color,
  Color? backgroundColor,
  double? fontSize,
  FontWeight? fontWeight,
  TextDecoration? decoration,
  double? letterSpacing,
  double? wordSpacing,
  double? height,
  Paint? foreground,
  Paint? background,
  List<Shadow>? shadows,
  TextBaseline? textBaseline,
});

class AppThemeText extends IAppThemeText {
  static TextStyle style({
    required double fontSize,
    final String? fontFamily,
    final FontWeight? fontWeight,
    final Color? color,
    final TextDecoration? decoration,
    final double? letterSpacing,
    final double? lineHeight,
  }) {
    final height = lineHeight != null ? lineHeight / fontSize : null;
    return AppTypography.getTextStyle(
      fontFamily: fontFamily ?? 'Baloo2',
      color: color,
      fontSize: fontSize,
      fontWeight: fontWeight,
      decoration: decoration,
      letterSpacing: letterSpacing,
      height: height,
    );
  }

  @override
  final TextStyle headline1;
  @override
  final TextStyle headline2;
  @override
  final TextStyle headline3;
  @override
  final TextStyle headline4;
  @override
  final TextStyle headline5;
  @override
  final TextStyle headline6;
  @override
  final TextStyle bodyText0;
  @override
  final TextStyle bodyText1;
  @override
  final TextStyle bodyText2;
  @override
  final TextStyle caption;
  @override
  final TextStyle button;
  @override
  final TextStyle overline;

  AppThemeText._({
    required this.headline1,
    required this.headline2,
    required this.headline3,
    required this.headline4,
    required this.headline5,
    required this.headline6,
    required this.bodyText0,
    required this.bodyText1,
    required this.bodyText2,
    required this.caption,
    required this.button,
    required this.overline,
  });

  @override
  ThemeExtension<IAppThemeText> lerp(
    covariant ThemeExtension<IAppThemeText>? other,
    double t,
  ) {
    return this;
  }

  @override
  Object get type => AppThemeText;

  factory AppThemeText.withThemeColor(AppThemeColor themeColor) {
    return AppThemeText._(
      headline1: AppThemeText.style(
        color: themeColor.onSurface,
        fontSize: 60,
        lineHeight: 72.6,
        letterSpacing: -0.02,
      ),
      headline2: AppThemeText.style(
        color: themeColor.onSurface,
        fontSize: 48,
        lineHeight: 58.1,
        letterSpacing: -0.02,
        fontWeight: FontWeight.bold,
      ),
      headline3: AppThemeText.style(
        color: themeColor.onSurface,
        fontSize: 36,
        lineHeight: 43.6,
        letterSpacing: -0.02,
        fontWeight: FontWeight.w400,
      ),
      headline4: AppThemeText.style(
        color: themeColor.onSurface,
        fontSize: 30,
        lineHeight: 36.3,
        letterSpacing: -0.02,
        fontWeight: FontWeight.w400,
      ),
      headline5: AppThemeText.style(
        color: themeColor.onSurface,
        fontSize: 26,
        lineHeight: 31.5,
        fontWeight: FontWeight.w400,
      ),
      headline6: AppThemeText.style(
        color: themeColor.onSurface,
        fontSize: 20,
        lineHeight: 24.2,
        fontWeight: FontWeight.w400,
      ),
      bodyText0: AppThemeText.style(
        color: themeColor.onSurface,
        fontSize: 16,
        lineHeight: 19.4,
        fontWeight: FontWeight.normal,
      ),
      bodyText1: AppThemeText.style(
        color: themeColor.onSurface,
        fontSize: 14,
        lineHeight: 16.9,
        fontWeight: FontWeight.normal,
      ),
      bodyText2: AppThemeText.style(
        color: themeColor.onSurface,
        fontSize: 12,
        lineHeight: 14.5,
        fontWeight: FontWeight.normal,
      ),
      caption: AppThemeText.style(
        color: themeColor.onSurface,
        fontSize: 10,
        lineHeight: 12.1,
      ),
      button: AppThemeText.style(
        color: themeColor.onSurface,
        fontSize: 14,
        lineHeight: 16.9,
        fontWeight: FontWeight.bold,
      ),
      overline: AppThemeText.style(
        color: themeColor.onSurface,
        fontSize: 8,
        lineHeight: 9.7,
      ),
    );
  }

  @override
  ThemeExtension<IAppThemeText> copyWith() {
    return this;
  }
}
