// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/navigator.dart';
import 'package:it/it.dart';

import '../../component/dependency_injector/di.dart';
import '../../features/editor/providers/floating_bar_viewmodel.dart';
import '../../features/editor/view/editor_screen.dart';
import '../routes.dart';

class EditorRouteBuilder extends AppPageRouteBuilder {
  @override
  WidgetBuilder create(RouteSettings settings) {
    return (context) {
      return ViewModel.create(
        create: () => it.get<FloatingBarViewModel>(),
        child: EditorScreen(),
      );
    };
  }

  @override
  String get name => Routes.editor;
}
