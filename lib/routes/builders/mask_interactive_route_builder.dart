// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/widgets.dart';

import '../../features/interactive/model/interactive_args.dart';
import '../../features/interactive/view/mask_interactive_screen.dart';
import '../routes.dart';

class MaskInteractiveRouteBuilder extends PageTransitionRouteBuilder {
  @override
  String get name => Routes.interativeMask;

  @override
  RoutePageBuilder create(RouteSettings settings) {
    final args = settings.arguments as MaskInteractiveArgs;
    return (context, animation, secondaryAnimation) {
      return MaskInteractiveScreen(args: args);
    };
  }

  @override
  Duration get transitionDuration => kAnimationDuration;

  @override
  Duration get reverseTransitionDuration => kAnimationDuration;

  @override
  Widget transitionsBuilder(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return ScaleTransition(
      scale: animation,
      child: child,
    );
  }
}
