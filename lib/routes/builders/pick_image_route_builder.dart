// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/navigator.dart';

import '../../features/image_picker/view/image_picker_bottomsheet.dart';
import '../routes.dart';

class PickImageRouteBuilder extends ModalBottomSheetRouteBuilder {
  @override
  double get scrollControlDisabledMaxHeightRatio => .8;

  @override
  WidgetBuilder create(RouteSettings settings) {
    return (context) {
      return ImagePickerBottomSheet(pickCount: settings.arguments as int? ?? 1);
    };
  }

  @override
  String get name => Routes.pickImage;
}
