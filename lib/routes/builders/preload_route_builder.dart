// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/widgets.dart';

import '../../features/preload/model/preload_arguments.dart';
import '../../features/preload/view/preload_screen.dart';
import '../routes.dart';

class PreloadRouteBuilder extends AppPageRouteBuilder {
  @override
  String get name => Routes.preload;

  @override
  WidgetBuilder create(RouteSettings settings) {
    final argument = settings.arguments as PreloadArguments;
    return (context) {
      return PreloadScreen(arguments: argument);
    };
  }
}
