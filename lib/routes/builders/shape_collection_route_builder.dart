import 'package:core/route/app_route_builder.dart';
import 'package:flutter/material.dart';

import '../../features/shape_collection/shape_collection.dart';
import '../routes.dart';

class ShapeCollectionRouteBuilder extends ModalBottomSheetRouteBuilder {
  @override
  double get scrollControlDisabledMaxHeightRatio => .8;

  @override
  WidgetBuilder create(RouteSettings settings) {
    return (context) {
      return const ShapeCollection();
    };
  }

  @override
  String get name => Routes.shape;
}
