// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../features/sticker_collection/view/sticker_collection.dart';
import '../routes.dart';

class StickerCollectionRouteBuilder extends PageTransitionRouteBuilder {
  @override
  RoutePageBuilder create(RouteSettings settings) {
    return (context, animation, secondaryAnimation) {
      return const StickerCollectionScreen();
    };
  }

  @override
  String get name => Routes.sticker;

  @override
  Color? get barrierColor => Colors.black54;

  @override
  bool get opaque => false;

  @override
  Duration get transitionDuration => kAnimationDuration;

  @override
  Duration get reverseTransitionDuration => kAnimationDuration;

  @override
  Widget transitionsBuilder(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return SlideTransition(
      position: animation.drive(
        Tween(begin: const Offset(0, 1), end: const Offset(0, 0)),
      ),
      child: child,
    );
  }
}
