// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/core.dart';
import 'package:flutter/material.dart';

import '../../features/text_input/model/text_input_intial.dart';
import '../../features/text_input/view/text_input_screen.dart';
import '../routes.dart';

class TextInputRouteBuilder extends PageTransitionRouteBuilder {
  @override
  RoutePageBuilder create(RouteSettings settings) {
    final initial = settings.arguments as TextInputParams?;
    return (context, animation, secondaryAnimation) {
      return TextInputScreen(initial: initial);
    };
  }

  @override
  String get name => Routes.textInput;

  @override
  Color? get barrierColor => Colors.black54;

  @override
  bool get opaque => false;

  @override
  Duration get transitionDuration => kAnimationDuration;

  @override
  Duration get reverseTransitionDuration => kAnimationDuration;

  @override
  Widget transitionsBuilder(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return ScaleTransition(scale: animation, child: child);
  }
}
