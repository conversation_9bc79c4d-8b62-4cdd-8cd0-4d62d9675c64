// Flutter project by quanghuuxx (<EMAIL>)

import 'package:core/route/app_route.dart';

import 'builders/dev_route_builder.dart';
import 'builders/editor_route_builder.dart';
import 'builders/home_route_builder.dart';
import 'builders/in_app_purchases_builder.dart';
import 'builders/mask_interactive_route_builder.dart';
import 'builders/pick_image_route_builder.dart';
import 'builders/preload_route_builder.dart';
import 'builders/shape_collection_route_builder.dart';
import 'builders/splash_route_builder.dart';
import 'builders/sticker_collection_route_builder.dart';
import 'builders/text_input_route_builder.dart';

class Routes extends AppRoute {
  static const String splash = '/';
  static const String home = '/home';
  static const String dev = '/dev';
  static const String pickImage = '/pick-image';
  static const String preload = '/preload';
  static const String editor = '/editor';
  static const String textInput = '/text-input';
  static const String sticker = '/sticker-collection';
  static const String shape = '/shape-collection';
  static const String interativeMask = '/interactive-mask';
  static const String inAppPurchases = '/in-app-purchases';

  Routes._();

  static final Routes instance = Routes._();

  factory Routes() {
    return instance;
  }

  void init() {
    instance
      ..registerRoute(SplashRouteBuilder())
      ..registerRoute(HomeRouteBuilder())
      ..registerRoute(DevRouteBuilder())
      ..registerRoute(PickImageRouteBuilder())
      ..registerRoute(PreloadRouteBuilder())
      ..registerRoute(EditorRouteBuilder())
      ..registerRoute(TextInputRouteBuilder())
      ..registerRoute(StickerCollectionRouteBuilder())
      ..registerRoute(ShapeCollectionRouteBuilder())
      ..registerRoute(MaskInteractiveRouteBuilder())
      ..registerRoute(InAppPurchasesRouteBuilder());
  }
}
