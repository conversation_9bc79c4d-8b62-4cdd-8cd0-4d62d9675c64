include: package:flutter_lints/flutter.yaml

# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options

linter:
  rules:
    # disable
    avoid_void_async: false
    avoid_print: false
    unawaited_futures: false
    no_leading_underscores_for_local_identifiers: false
    depend_on_referenced_packages: false
    prefer_const_constructors_in_immutables: false
    prefer_generic_function_type_aliases: false
    prefer_function_declarations_over_variables: false
    unnecessary_string_escapes: false
    use_key_in_widget_constructors: false
    constant_identifier_names: false
    implementation_imports: false
    one_member_abstracts: false
    overridden_fields: false
    avoid_renaming_method_parameters: false
    library_private_types_in_public_api: false
    unnecessary_getters_setters: false

    # enable
    prefer_single_quotes: true
    avoid_relative_lib_imports: true
    cancel_subscriptions: true
    close_sinks: true
    cascade_invocations: true
    unnecessary_overrides: true
    unnecessary_await_in_return: true
    always_declare_return_types: true
    avoid_annotating_with_dynamic: true
    avoid_function_literals_in_foreach_calls: true
    file_names: true
    library_prefixes: true
    prefer_null_aware_method_calls: true
    recursive_getters: true
    require_trailing_commas: true
    unnecessary_late: true
    use_string_buffers: true
    use_super_parameters: true
    sized_box_for_whitespace: true
    sized_box_shrink_expand: true
    cast_nullable_to_non_nullable: true
    prefer_relative_imports: true
    directives_ordering: true
    use_build_context_synchronously: true