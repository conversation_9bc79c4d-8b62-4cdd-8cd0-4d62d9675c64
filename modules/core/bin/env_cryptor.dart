import 'dart:io';
import 'package:encrypt/encrypt.dart';
import 'package:path/path.dart' as p;

void main(List<String> args) {
  if (args.length < 5) {
    stderr.writeln('Please provide arguments.');
    return;
  }

  String? mode, input, cyptoKey, iv;
  for (var i = 0; i < args.length; i++) {
    final arg = args[i];

    if (i == 0) {
      mode = arg;
      continue;
    }

    if (arg == '-i') {
      input = args[i + 1];
    } else if (arg == '-c') {
      cyptoKey = args[i + 1];
    } else if (arg == '-iv') {
      iv = args[i + 1];
    }
  }

  if (mode != 'en' && mode != 'de' || (mode == 'de' && iv == null)) {
    stderr.writeln('Invalid mode.');
    return;
  }

  if (input == null || cyptoKey == null) {
    stderr.writeln('Invalid provide arguments.');
    return;
  }

  final file = File(input);
  if (!file.existsSync()) {
    stderr.writeln('File input does not exist.');
    return;
  }

  if (mode == 'en') {
    _encrypt(file, cyptoKey);
  } else {
    _decrypt(file, cyptoKey, iv!);
  }
}

void _encrypt(final File file, final String cyptoKey) {
  final extention = p.extension(file.path);
  if (extention == '.enc') {
    stderr.writeln('Cant not encrypt file already encrypted.');
    return;
  }

  final iv = IV.fromLength(8);
  stdout
    ..writeln('---------------------')
    ..writeln('Save this iv to decrypt file.')
    ..writeln(iv.base64)
    ..writeln('---------------------');

  final encrypter = Encrypter(Salsa20(Key.fromUtf8(cyptoKey)));
  final encrypted = encrypter.encrypt(file.readAsStringSync(), iv: iv);

  final dir = p.dirname(file.path);
  final baseName = p.basenameWithoutExtension(file.path);
  final outputFile = File('$dir/$baseName.enc');
  if (outputFile.existsSync()) {
    outputFile.deleteSync();
  }
  outputFile.writeAsBytesSync(encrypted.bytes);
  stdout.writeln('Encrypt file generated at: ${outputFile.path}');
}

void _decrypt(
  final File file,
  final String cyptoKey,
  final String ivBase64,
) {
  final extention = p.extension(file.path);
  if (extention != '.enc') {
    stderr.writeln('Cant not decrypt file not encrypted.');
    return;
  }

  final key = Key.fromUtf8(cyptoKey);
  final iv = IV.fromBase64(ivBase64);
  final encrypter = Encrypter(Salsa20(key));
  final encrypted = file.readAsBytesSync();
  final decryted = encrypter.decrypt(Encrypted(encrypted), iv: iv);

  final dir = p.dirname(file.path);
  final baseName = p.basenameWithoutExtension(file.path);
  final outputFile = File('$dir/$baseName.env');
  if (outputFile.existsSync()) {
    outputFile.deleteSync();
  }
  outputFile.writeAsString(decryted);
  stdout.writeln('Decrypt file generated at: ${outputFile.path}');
}
