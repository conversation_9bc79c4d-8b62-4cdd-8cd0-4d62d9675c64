// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:io';
import 'package:path/path.dart' as p;

// dart run core:generate_assets -i assets/icons -o lib/resource/icons_constants.dart
void main(List<String> args) {
  if (args.length < 4) {
    stdout.writeln('Please provide arguments.');
    return;
  }

  String? input, output;
  for (var i = 0; i < args.length; i++) {
    final arg = args[i];
    if (arg == '-i') {
      input = args[i + 1];
    } else if (arg == '-o') {
      output = args[i + 1];
    }
  }

  if (input == null || output == null) {
    stdout.writeln('Invalid provide arguments.');
    return;
  }

  final dir = Directory(input);
  final files = dir.listSync(recursive: true, followLinks: false);

  final StringBuffer buffer = StringBuffer();

  for (final file in files) {
    final path = file.path;
    if (buffer.isNotEmpty) {
      buffer.write('\n');
    }
    buffer.write('  static const ${p.basenameWithoutExtension(path)} = \'${p.relative(path)}\';');
  }

  final fileOutput = File(output);

  if (fileOutput.existsSync()) {
    fileOutput.delete();
  }

  fileOutput
    ..createSync(recursive: true)
    ..writeAsStringSync('''
// Flutter project by quanghuuxx (<EMAIL>)

abstract class ${_camelCase(p.basenameWithoutExtension(output))} {
${buffer.toString()}
}''');
}

String _camelCase(String value) {
  return value.split('_').map((e) => '${e[0].toUpperCase()}${e.substring(1)}').join();
}
