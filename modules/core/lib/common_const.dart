// Flutter project by quanghuuxx (<EMAIL>)

// quanghuuxx (<EMAIL>)
// ------
// Copyright 2023 quanghuuxx, Ltd. All rights reserved.

import 'package:flutter/material.dart';

const String kDevErrorCode = 'ERRDEV';

const double kZero = 0.0;
const double kOne = 1.0;
const double kTwo = 2.0;
const double kThree = 3.0;
const double kFour = 4.0;
const double kFive = 5.0;
const double kSix = 6.0;
const double kSeven = 7.0;
const double kEight = 8.0;
const double kNine = 9.0;
const double kTen = 10.0;
const double kEleven = 11.0;
const double kTwelve = 12.0;
const double kThirteen = 13.0;
const double kFourteen = 14.0;
const double kFifteen = 15.0;
const double kSixteen = 16.0;
const double kSeventeen = 17.0;
const double kEighteen = 18.0;
const double kNineteen = 19.0;
const double kTwenty = 20.0;
const double kTwentyFour = 24.0;
const double kThirty = 30.0;
const double kThirtyTwo = 32.0;
const double kThirtySix = 36.0;
const double kForty = 40.0;
const double kFortyTwo = 42.0;
const double kFortyEight = 48.0;
const double kFifty = 50.0;
const double kSixty = 60.0;
const double kSixtyFour = 64.0;
const double kSeventy = 70.0;
const double kEighty = 80.0;
const double kNinety = 90.0;
const double kOnehundred = 100.0;

const double kSizeIcon = 24.0;
const double kSizeDivider = 16;

const int kOneLine = 1;
const int kTwoLine = 2;
const int kThreeLine = 3;
const int kFourLine = 4;

const int k128Length = 128;
const int k256Length = 256;
const int k512Length = 512;

const Duration kAnimationFastDuration = Duration(milliseconds: 120);
const Duration kAnimationDuration = Duration(milliseconds: 200);
const Duration kAnimationDurationMedium = Duration(milliseconds: 400);
const Duration kAnimationDurationSlow = Duration(milliseconds: 600);

const SizedBox kBox0 = SizedBox.square(dimension: kZero);
const SizedBox kBox4 = SizedBox.square(dimension: kFour);
const SizedBox kBox8 = SizedBox.square(dimension: kEight);
const SizedBox kBox12 = SizedBox.square(dimension: kTwelve);
const SizedBox kBox16 = SizedBox.square(dimension: kSixteen);
const SizedBox kBox24 = SizedBox.square(dimension: kTwentyFour);
const SizedBox kBox32 = SizedBox.square(dimension: kThirtyTwo);

const EdgeInsets kPaddingAll4 = EdgeInsets.all(4.0);
const EdgeInsets kPaddingAll8 = EdgeInsets.all(8.0);
const EdgeInsets kPaddingAll12 = EdgeInsets.all(12.0);
const EdgeInsets kPaddingAll16 = EdgeInsets.all(16.0);
const EdgeInsets kPaddingAll24 = EdgeInsets.all(24.0);

const EdgeInsets kPaddingVertical4 = EdgeInsets.symmetric(vertical: 4.0);
const EdgeInsets kPaddingVertical8 = EdgeInsets.symmetric(vertical: 8.0);
const EdgeInsets kPaddingVertical12 = EdgeInsets.symmetric(vertical: 12.0);
const EdgeInsets kPaddingVertical16 = EdgeInsets.symmetric(vertical: 16.0);
const EdgeInsets kPaddingVertical24 = EdgeInsets.symmetric(vertical: 24.0);

const EdgeInsets kPaddingHorizontal4 = EdgeInsets.symmetric(horizontal: 4.0);
const EdgeInsets kPaddingHorizontal8 = EdgeInsets.symmetric(horizontal: 8.0);
const EdgeInsets kPaddingHorizontal12 = EdgeInsets.symmetric(horizontal: 12.0);
const EdgeInsets kPaddingHorizontal16 = EdgeInsets.symmetric(horizontal: 16.0);
const EdgeInsets kPaddingHorizontal24 = EdgeInsets.symmetric(horizontal: 24.0);

const BorderRadius kBorderRadius4 = BorderRadius.all(kRadius4);
const BorderRadius kBorderRadius8 = BorderRadius.all(kRadius8);
const BorderRadius kBorderRadius12 = BorderRadius.all(kRadius12);
const BorderRadius kBorderRadius16 = BorderRadius.all(kRadius16);
const BorderRadius kBorderRadius24 = BorderRadius.all(kRadius24);

const Radius kRadius4 = Radius.circular(4);
const Radius kRadius8 = Radius.circular(8);
const Radius kRadius12 = Radius.circular(12);
const Radius kRadius16 = Radius.circular(16);
const Radius kRadius24 = Radius.circular(24);
