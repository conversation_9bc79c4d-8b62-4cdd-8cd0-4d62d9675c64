// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:collection';

import 'package:flutter/widgets.dart';

class InheritedListenable extends InheritedWidget {
  const InheritedListenable({super.key, required super.child});

  @override
  InheritedElement createElement() => _InheritProviderElement(this);

  @override
  bool updateShouldNotify(covariant InheritedListenable oldWidget) => false;

  @protected
  bool isSupportedAspect(Object aspect) => true;
}

class _InheritProviderElement extends InheritedElement {
  _InheritProviderElement(InheritedListenable super.widget);

  @override
  void updateDependencies(Element dependent, Object? aspect) {
    assert(aspect != null && aspect is Listenable);

    final hash = getDependencies(dependent) as HashSet<Listenable>?;

    if (hash == null) {
      setDependencies(dependent, HashSet<Listenable>()..add(aspect! as Listenable));
      (aspect as Listenable).addListener(dependent.markNeedsBuild);
    } else if (hash.add(aspect! as Listenable)) {
      (aspect as Listenable).addListener(dependent.markNeedsBuild);
    }
  }

  @override
  void removeDependent(Element dependent) {
    final hash = getDependencies(dependent)! as HashSet<Listenable>;
    for (final Listenable listenable in hash) {
      listenable.removeListener(dependent.markNeedsBuild);
    }

    super.removeDependent(dependent);
  }
}

extension BuildContextInheritExt on BuildContext {
  T listenable<T extends Listenable>(final T listenable) {
    final inherit = dependOnInheritedWidgetOfExactType<InheritedListenable>(
      aspect: listenable,
    );

    assert(inherit != null, 'Can`t find InheritedListenable in scope of $this');
    return listenable;
  }
}
