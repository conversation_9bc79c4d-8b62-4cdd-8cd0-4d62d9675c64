// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:async';
import 'dart:developer' as dev;

import 'package:flutter/foundation.dart';

class Console {
  Console._();

  static bool _kDevMode = kDebugMode;

  static void enable(bool value) {
    _kDevMode = value;
  }

  static void log(
    Object? log, {
    DateTime? time,
    int? sequenceNumber,
    int level = 0,
    String name = '',
    Zone? zone,
    Object? error,
    StackTrace? stackTrace,
  }) {
    if (!_kDevMode) return;
    dev.log(
      log.toString(),
      time: time,
      sequenceNumber: sequenceNumber,
      level: level,
      name: name,
      zone: zone,
      error: error,
      stackTrace: stackTrace,
    );
  }

  static void print(String message) {
    if (!_kDevMode) return;
    print(message);
  }
}
