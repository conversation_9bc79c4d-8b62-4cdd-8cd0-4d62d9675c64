// Flutter project by quanghuuxx (<EMAIL>)

// Saturday, 1st October 2022 02:28 PM
// quanghuuxx (<EMAIL>)
// -----
// Copyright 2022 quanghuuxx, Ltd. All rights reserved.

import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';

import '../console.dart';
import '../localization/app_localization_delegate.dart';

extension BuildContextExt on BuildContext {
  String tr(String key, {List<Object>? params}) {
    return S.of(this).translate(key, params: params);
  }

  bool hideKeyboard() {
    final has = MediaQuery.of(this).viewInsets.bottom != 0;
    if (has) SystemChannels.textInput.invokeMethod('TextInput.hide');
    return has;
  }

  T stateOf<T extends State<StatefulWidget>>() {
    final state = findAncestorStateOfType<T>();
    if (state == null) {
      throw Exception('Can`t find state $T in scope of $this');
    }
    return state;
  }

  T widgetOf<T extends Widget>() {
    final widget = findAncestorWidgetOfExactType<T>();
    if (widget == null) {
      throw Exception('Can`t find state $T in scope of $this');
    }
    return widget;
  }

  RouteSettings? get currentRouter => ModalRoute.of(this)?.settings;

  Future<T?> pushReplacementNamed<T, TO>(
    String routeName, {
    TO? result,
    Object? arguments,
    bool popModalRoute = true,
  }) {
    final nav = Navigator.of(this);
    if (popModalRoute) {
      nav.popUntil((route) => route is PageRoute);
    }
    return nav.pushReplacementNamed<T, TO>(routeName, arguments: arguments);
  }

  Future<T?> pushNamed<T>(
    String routeName, {
    Object? arguments,
    bool popModalRoute = true,
  }) {
    final nav = Navigator.of(this);
    if (popModalRoute) {
      nav.popUntil((route) => route is PageRoute);
    }
    return nav.pushNamed<T>(routeName, arguments: arguments);
  }

  void popPage<T>([T? result]) {
    final nav = Navigator.of(this)..popUntil((route) => route is PageRoute);
    return nav.pop(result);
  }

  void pop<T>([T? result]) => Navigator.of(this).pop<T>(result);

  void safety(ValueChanged<BuildContext> callback) {
    if (mounted) {
      callback(this);
    } else {
      Console.log(
        'Context $runtimeType not mounted',
        name: 'BuildContextExt',
        stackTrace: StackTrace.current,
      );
    }
  }
}
