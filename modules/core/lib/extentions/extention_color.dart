// Flutter project by quanghuuxx (<EMAIL>)

// Sunday, 7th August 2022 10:38 AM
// quanghuuxx (<EMAIL>)
// -----
// Copyright 2022 quanghuuxx, Ltd. All rights reserved.

import 'dart:ui';

extension ColorExt on Color {
  String toHex() {
    final hex = toARGB32().toRadixString(16);
    return '#$hex';
  }

  String toRGBA() {
    // ignore: deprecated_member_use
    return 'rgba($red, $green, $blue, $alpha)';
  }
}

extension StringExt on String {
  Color toColor() {
    String hex = replaceAll('#', '');
    // Nếu chuỗi không có alpha (AA), thêm giá trị mặc định là FF (hoàn toàn không trong suốt)
    if (hex.length == 6) {
      hex = 'FF$hex';
    }
    // Chuyển đổi chuỗi hex thành giá trị int
    return Color(int.parse(hex, radix: 16));
  }
}
