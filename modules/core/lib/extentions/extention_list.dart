// Wednesday, 6th September 2023 10:35 PM
// quanghuuxx (<EMAIL>)
// -----
// Copyright 2023 quanghuuxx, Ltd. All rights reserved.

extension ListExt<T> on List<T> {
  void replace(T old, T replace) {
    final _idx = indexOf(old);
    if (_idx != -1) {
      removeAt(_idx);
      insert(_idx, replace);
    }
  }

  void replaceWhere(bool Function(T) test, T Function(T) replace) {
    final _idx = indexWhere((e) => test(e));
    if (_idx != -1) {
      final old = this[_idx];
      removeAt(_idx);
      insert(_idx, replace(old));
    }
  }

  List<S> separated<S>(
    S Function(int index, T value) genarate, {
    required S Function(int index) separated,
  }) {
    final _list = <S>[];
    final count = length;

    int i = 0;
    while (i < count) {
      final e = this[i];
      if (i++ < count) {
        _list.add(separated(i - 1));
      }
      _list.add(genarate(i, e));
    }

    return _list;
  }

  List<S> customIndexed<S>(
    List<int> customs, {
    required S Function(int index, T value) genarate,
    required S Function(int index) custom,
  }) {
    final _list = <S>[];
    if (length == 0) return _list;

    customs.sort();

    final count = length + customs.length;

    int customized = 0;
    for (var i = 0; i < count; i++) {
      if (customs.contains(i)) {
        ++customized;
        _list.add(custom(i));
      } else {
        _list.add(genarate(i, this[i - customized]));
      }
    }

    return _list;
  }
}
