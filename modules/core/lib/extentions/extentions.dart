// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:async';
import 'dart:ui';

import '../core.dart';

extension Ext<T extends Object?> on T {
  T log({
    DateTime? time,
    int? sequenceNumber,
    int level = 0,
    String? tag,
    String name = '/log',
    Zone? zone,
    Object? error,
    StackTrace? stackTrace,
  }) {
    Console.log(
      (tag?.isNotEmpty == true ? ' /$tag/ ' : '') + toString(),
      name: name,
      time: time,
      sequenceNumber: sequenceNumber,
      level: level,
      zone: zone,
      error: error,
      stackTrace: stackTrace,
    );

    return this;
  }

  R when<R>(R Function(T) fun) => fun(this);
}

extension ImageExt on Image {
  Size get size => Size(width.toDouble(), height.toDouble());
}
