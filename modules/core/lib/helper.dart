// Flutter project by quanghuuxx (<EMAIL>)

// Thursday, 17th August 2023 10:09 PM
// quanghuuxx (<EMAIL>)
// -----
// Copyright 2023 quanghuuxx, Ltd. All rights reserved.

import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:rxdart/rxdart.dart';
import 'package:uuid/uuid.dart';

import 'core.dart';

const _uuid = Uuid();

String get forcedBuild => _uuid.v1();

String get uuid => _uuid.v4();

bool isLocalUrl(String url) {
  return url.isNotEmpty && (url.startsWith('/') || url.startsWith('file://') || url.substring(1).startsWith(':\\'));
}

bool isTablet(BuildContext context) {
  return sizeDevice(context).shortestSide > 600;
}

Size sizeDevice(BuildContext context) {
  final view = View.of(context);
  return view.physicalSize / view.devicePixelRatio;
}

ViewPadding paddingDevice(BuildContext context) {
  return View.of(context).padding;
}

void runImmediatelyOrAfterFrame(VoidCallback callback) {
  if (SchedulerBinding.instance.schedulerPhase == SchedulerPhase.persistentCallbacks) {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      callback.call();
    });
  } else {
    callback.call();
  }
}

bool? intToBool(int? field) {
  if (field == null) return null;
  return field == 1;
}

int? boolToInt(bool? field) {
  if (field == null) return null;
  return field ? 1 : 0;
}

AppVersionStatus compareAppVersion(String current, String other) {
  if (current.isEmpty || other.isEmpty) return AppVersionStatus.none;

  /// 1.2309.02 => [1, 2309, 02]
  final currents = current.split('.').map((e) => int.parse(e)).toList();
  final others = other.split('.').map((e) => int.parse(e)).toList();

  if (currents.length != others.length) return AppVersionStatus.none;

  if (currents[0] < others[0]) {
    return AppVersionStatus.force_upgrade;
  }

  if (currents[1] < others[1] || currents[1] == others[1] && currents[2] < others[2]) {
    return AppVersionStatus.recommend_upgrade;
  }

  if (currents[0] > others[0] || currents[1] > others[1] || currents[2] > others[2]) {
    return AppVersionStatus.upgraded;
  }

  return AppVersionStatus.none;
}

/// Dùng cho bloc event cần thực thi tuần tự
_Transformer<T> sequential<T>() {
  return (events, mapper) => events.asyncExpand(mapper);
}

/// Dùng cho bloc event chỉ nhận event mới nhất, bỏ qua các event cũ hơn
_Transformer<T> restartable<T>() {
  return (events, mapper) => events.switchMap(mapper);
}

_Transformer<T> debounceTime<T>(final Duration duration) {
  return (events, mapper) => events.debounceTime(duration).switchMap(mapper);
}

typedef _Mapper<Event> = Stream<Event> Function(Event event);

typedef _Transformer<Event> = Stream<Event> Function(
  Stream<Event> events,
  _Mapper<Event> mapper,
);
