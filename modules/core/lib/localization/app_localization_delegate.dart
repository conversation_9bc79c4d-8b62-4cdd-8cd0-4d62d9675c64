// Sunday, 28th May 2023 09:33 PM
// quanghuuxx (<EMAIL>)
// -----
// Copyright 2023 quanghuuxx, Ltd. All rights reserved.

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import 'localizer_loader.dart';

abstract class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  final S _l10n = S();

  /// trả về danh sách các asset file json,
  /// dùng để load localizations
  FutureOr<List<String>> assets(Locale locale);

  List<Locale> get supportedLocales;

  @override
  Future<S> load(Locale locale) async {
    await _l10n._loader.load(locale: locale, paths: await assets(locale));
    Intl.defaultLocale = locale.toString();
    return _l10n;
  }

  @override
  bool shouldReload(covariant LocalizationsDelegate<S> old) {
    return false;
  }

  @override
  Type get type => S;
}

class S {
  final LocalizationLoader _loader = LocalizationLoader();

  static S of(BuildContext context) {
    return Localizations.of<S>(context, S)!;
  }

  String translate(String key, {List<Object>? params}) {
    return _loader.read(key, params ?? []);
  }
}
