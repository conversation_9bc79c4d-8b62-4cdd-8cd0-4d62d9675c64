// Flutter project by quanghuuxx (<EMAIL>)

// Sunday, 28th May 2023 05:44 PM
// quanghuuxx (<EMAIL>)
// -----
// Copyright 2023 quanghuuxx, Ltd. All rights reserved.

import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../console.dart';

class LocalizationLoader {
  final _sentences = <String, String>{};

  Future<void> load({
    required Locale locale,
    required List<String> paths,
  }) async {
    _sentences.clear();

    for (var path in paths) {
      await _loadSingle(locale, path);
    }
  }

  Future<void> _loadSingle(
    Locale locale,
    String path,
  ) async {
    if (path.endsWith('/')) {
      path = path.substring(0, path.length - 1);
    }

    final raw = await rootBundle.loadString(path);

    Map<String, dynamic> _result;
    try {
      _result = json.decode(raw);
    } catch (e) {
      _result = {};
      Console.log('Can`t decode file path $path', name: 'LocalizationLoader');
      return;
    }

    for (var entry in _result.entries) {
      if (_sentences.containsKey(entry.key)) {
        Console.log('Duplicated key ${entry.key}', name: 'LocalizationLoader');
      }
      _sentences[entry.key] = entry.value;
    }
  }

  String read(String key, List<Object> arguments) {
    if (!_sentences.containsKey(key)) {
      return key;
    }
    var value = _sentences[key]!;
    if (value.contains('%s')) {
      return _replaceArguments(key, value, arguments);
    }

    return value;
  }

  String _replaceArguments(String key, String value, List<Object> arguments) {
    final regExp = RegExp(r'(\%s\d?)');
    final matchers = regExp.allMatches(value);
    var argsCount = 0;

    try {
      for (var matcher in matchers) {
        for (var i = 1; i <= matcher.groupCount; i++) {
          final finded = matcher.group(i);
          if (finded == null) {
            continue;
          }

          if (finded == '%s') {
            value = value.replaceFirst('%s', arguments[argsCount].toString());
            argsCount++;
            continue;
          }

          var extractedId = int.tryParse(finded.replaceFirst('%s', ''));
          if (extractedId == null) {
            continue;
          }

          if (extractedId >= arguments.length) {
            continue;
          }

          value = value.replaceFirst(finded, arguments[extractedId].toString());
        }
      }
    } catch (e) {
      log(
        'Missing ${matchers.length} params for value of key \'$key\' :((',
        name: 'LocalizationLoader',
      );
    }

    return value;
  }
}
