// Sunday, 13th August 2023 05:03 PM
// quanghuuxx (<EMAIL>)
// -----
// Copyright 2023 quanghuuxx, Ltd. All rights reserved.

import 'package:flutter/material.dart';

mixin ScaffoldToastMixin {
  bool _isSnackbarActive = false;

  void toast(
    BuildContext context, {
    required Widget content,
    double? width,
    double elevation = 0.0,
    BorderRadius borderRadius = BorderRadius.zero,
    Duration duration = const Duration(milliseconds: 1800),
    SnackBarBehavior behavior = SnackBarBehavior.fixed,
    Color? backgroundColor,
  }) {
    if (!_isSnackbarActive) {
      _isSnackbarActive = true;
      ScaffoldMessenger.of(context)
        ..removeCurrentSnackBar()
        ..showSnackBar(
          SnackBar(
            width: width,
            duration: duration,
            behavior: behavior,
            backgroundColor: backgroundColor,
            elevation: elevation,
            shape: RoundedRectangleBorder(
              borderRadius: borderRadius,
            ),
            content: content,
          ),
        ).closed.then((_) => _isSnackbarActive = false);
    }
  }

  void dissmiss(BuildContext context) {
    ScaffoldMessenger.of(context).removeCurrentSnackBar();
  }
}
