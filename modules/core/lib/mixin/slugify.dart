// Copyright 2023 QSoft, Ltd. All rights reserved.

final _dupeSpaceRegExp = RegExp(r'\s{2,}');

extension SlugifyExt on String {
  /// Converts [text] to a slug [String] separated by the [delimiter].
  String slugify({String delimiter = '-', bool lowercase = true}) {
    // Trim leading and trailing whitespace.
    var slug = trim();

    // Make the text lowercase (optional).
    if (lowercase) {
      slug = slug.toLowerCase();
    }

    // Substitute characters for their latin equivalent.
    _replacements.forEach((k, v) => slug = slug.replaceAll(k, v));

    slug = slug
        // Condense whitespaces to 1 space.
        .replaceAll(_dupeSpaceRegExp, ' ')
        // Replace space with the delimiter.
        .replaceAll(' ', delimiter);

    return slug;
  }
}

const _replacements = {
  'o': 'o',
  'ò': 'o',
  'ó': 'o',
  'ỏ': 'o',
  'õ': 'o',
  'ọ': 'o',
  'ơ': 'o',
  'ờ': 'o',
  'ớ': 'o',
  'ở': 'o',
  'ỡ': 'o',
  'ợ': 'o',
  'ô': 'o',
  'ồ': 'o',
  'ố': 'o',
  'ổ': 'o',
  'ỗ': 'o',
  'ộ': 'o',
  'a': 'a',
  'à': 'a',
  'á': 'a',
  'ả': 'a',
  'ã': 'a',
  'ạ': 'a',
  'ă': 'a',
  'ắ': 'a',
  'ằ': 'a',
  'ẳ': 'a',
  'ẵ': 'a',
  'ặ': 'a',
  'â': 'a',
  'ầ': 'a',
  'ấ': 'a',
  'ẩ': 'a',
  'ẫ': 'a',
  'ậ': 'a',
  'đ': 'd',
  'Đ': 'd',
  'e': 'e',
  'è': 'e',
  'é': 'e',
  'ẻ': 'e',
  'ẽ': 'e',
  'ẹ': 'e',
  'ê': 'e',
  'ề': 'e',
  'ế': 'e',
  'ể': 'e',
  'ễ': 'e',
  'ệ': 'e',
  'i': 'i',
  'ì': 'i',
  'í': 'i',
  'ỉ': 'i',
  'ĩ': 'i',
  'ị': 'i',
  'ư': 'u',
  'ừ': 'u',
  'ứ': 'u',
  'ử': 'u',
  'ữ': 'u',
  'ự': 'u',
  'u': 'u',
  'ù': 'u',
  'ú': 'u',
  'ủ': 'u',
  'ũ': 'u',
  'ụ': 'u',
  'y': 'y',
  'ỳ': 'y',
  'ý': 'y',
  'ỷ': 'y',
  'ỹ': 'y',
  'ỵ': 'y',
};
