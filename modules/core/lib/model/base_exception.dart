// Wednesday, 6th September 2023 09:43 PM
// quanghuuxx (<EMAIL>)
// -----
// Copyright 2023 quanghuuxx, Ltd. All rights reserved.

import 'package:sqflite/sqflite.dart';

class BaseException {
  static const String unknowCode = 'ERUNOW';
  static const String noInternetCode = 'NOITER';
  static const String dbExceptionCode = 'EDBEXC';

  final String code;
  final String? description;

  BaseException({
    required this.code,
    this.description,
  });

  factory BaseException.from(Object other) {
    if (other is BaseException) {
      return other;
    } else if (other is DatabaseException) {
      return BaseException(code: BaseException.dbExceptionCode, description: other.toString());
    }

    return BaseException(code: BaseException.unknowCode, description: other.toString());
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is BaseException && other.code == code && other.description == description;
  }

  @override
  int get hashCode => code.hashCode ^ description.hashCode;

  @override
  String toString() => '''BaseException(
    code: $code, 
    description: $description,
  )''';
}
