// Flutter project by quanghuuxx (<EMAIL>)

// Saturday, 1st July 2023 06:04 PM
// quanghuuxx (<EMAIL>)
// -----
// Copyright 2023 quanghuuxx, Ltd. All rights reserved.

import 'dart:async';

import 'package:flutter/material.dart';

class DebounceValue<T> {
  T _value;
  T? _oldValue;
  final Duration _duration;

  Timer? _timer;
  final List<ValueChanged<T>> _onUpdaters = [];

  DebounceValue(
    T value, {
    required Duration duration,
  })  : _value = value,
        _duration = duration;

  T get value => _value;
  T? get oldValue => _oldValue;

  bool get debouncing => _timer != null && _timer!.isActive;

  void setValueWithOutDebound(T other, {bool shouldNotify = false}) {
    if (_value == other) return;
    _oldValue = _value;
    _value = other;

    if (shouldNotify) {
      for (var element in _onUpdaters) {
        element.call(other);
      }
    }
  }

  set value(T other) {
    if (_value == other) return;

    cancel();
    _timer = Timer(_duration, () {
      _oldValue = _value;
      _value = other;
      for (final element in _onUpdaters) {
        element.call(other);
      }
      _timer = null;
    });
  }

  void cancel() {
    _timer?.cancel();
    _timer = null;
  }

  void dispose() {
    _timer?.cancel();
    _timer = null;
    _onUpdaters.clear();
  }

  /// return func cancel, call this fun to cancel listener
  void addListener(ValueChanged<T> onUpdated) {
    _onUpdaters.add(onUpdated);
  }

  void removeListener(ValueChanged<T> onUpdated) {
    _onUpdaters.remove(onUpdated);
  }
}
