// Flutter project by quanghuuxx (<EMAIL>)

// ignore_for_file: prefer_single_quotes

import 'package:encrypt/encrypt.dart';
import 'package:flutter/services.dart';

/// To generate [cyptoKey] secure random keys,
/// run the following commands:
///
/// 'dart pub global activate encrypt'  # Activate the encrypt package
/// 'secure-random'                   # Generate a secure random key look like this: CBoaDQIQAgceGg8dFAkMDBEOECEZCxgMBiAUFQwKFhg=
///
/// [iv] will be generated automatically when run script encrypt (read README.md), keep safe it, use it when decrypt
class Env {
  final String path;
  final String? cyptoKey;
  final String? iv;

  Env({
    required this.path,
    this.cyptoKey,
    this.iv,
  });

  final Map<String, String> _envMap = {};

  Future<void> load() async {
    String doc = '';

    if (cyptoKey != null && iv != null) {
      final key = Key.fromUtf8(cyptoKey!);
      final iv = IV.fromBase64(this.iv!);
      final encrypter = Encrypter(Salsa20(key));
      final encrypted = await rootBundle.load(path);
      doc = encrypter.decrypt(
        Encrypted(encrypted.buffer.asUint8List()),
        iv: iv,
      );
    } else {
      doc = await rootBundle.loadString(path);
    }

    final List<String> lines = doc.split('\n');
    final envEntries = const _Parser().parse(lines);
    _envMap.addAll(envEntries);
  }

  String get(String name, {String? fallback}) {
    final value = _maybeGet(name, fallback: fallback);
    if (value == null) {
      throw AssertionError('$name variable not found. A non-null fallback is required for missing entries');
    }
    return value;
  }

  /// Load the enviroment variable value as an [int]
  ///
  /// If variable with [name] does not exist then [fallback] will be used.
  /// However if also no [fallback] is supplied an error will occur.
  ///
  /// Furthermore an [FormatException] will be thrown if the variable with [name]
  /// exists but can not be parsed as an [int].
  int getInt(String name, {int? fallback}) {
    final value = _maybeGet(name);
    assert(value != null || fallback != null, 'A non-null fallback is required for missing entries');
    return value != null ? int.parse(value) : fallback!;
  }

  /// Load the enviroment variable value as a [double]
  ///
  /// If variable with [name] does not exist then [fallback] will be used.
  /// However if also no [fallback] is supplied an error will occur.
  ///
  /// Furthermore an [FormatException] will be thrown if the variable with [name]
  /// exists but can not be parsed as a [double].
  double getDouble(String name, {double? fallback}) {
    final value = _maybeGet(name);
    assert(value != null || fallback != null, 'A non-null fallback is required for missing entries');
    return value != null ? double.parse(value) : fallback!;
  }

  /// Load the enviroment variable value as a [bool]
  ///
  /// If variable with [name] does not exist then [fallback] will be used.
  /// However if also no [fallback] is supplied an error will occur.
  ///
  /// Furthermore an [FormatException] will be thrown if the variable with [name]
  /// exists but can not be parsed as a [bool].
  bool getBool(String name, {bool? fallback}) {
    final value = _maybeGet(name);
    assert(value != null || fallback != null, 'A non-null fallback is required for missing entries');
    if (value != null) {
      if (['true', '1'].contains(value.toLowerCase())) {
        return true;
      } else if (['false', '0'].contains(value.toLowerCase())) {
        return false;
      } else {
        throw const FormatException('Could not parse as a bool');
      }
    }

    return fallback!;
  }

  String? _maybeGet(String name, {String? fallback}) => _envMap[name] ?? fallback;
}

/// Creates key-value pairs from strings formatted as environment
/// variable definitions.
class _Parser {
  static const _singleQuot = "'";
  static final _leadingExport = RegExp(r'''^ *export ?''');
  static final _comment = RegExp(r'''#[^'"]*$''');
  static final _commentWithQuotes = RegExp(r'''#.*$''');
  static final _surroundQuotes = RegExp(r'''^(["'])(.*?[^\\])\1''');
  static final _bashVar = RegExp(r'''(\\)?(\$)(?:{)?([a-zA-Z_][\w]*)+(?:})?''');

  /// [_Parser] methods are pure functions.
  const _Parser();

  /// Creates a [Map](dart:core).
  /// Duplicate keys are silently discarded.
  Map<String, String> parse(Iterable<String> lines) {
    var out = <String, String>{};
    for (var line in lines) {
      var kv = parseOne(line, env: out);
      if (kv.isEmpty) continue;
      out.putIfAbsent(kv.keys.single, () => kv.values.single);
    }
    return out;
  }

  /// Parses a single line into a key-value pair.
  Map<String, String> parseOne(String line, {Map<String, String> env = const {}}) {
    var stripped = strip(line);
    if (!_isValid(stripped)) return {};

    var idx = stripped.indexOf('=');
    var lhs = stripped.substring(0, idx);
    var k = swallow(lhs);
    if (k.isEmpty) return {};

    var rhs = stripped.substring(idx + 1, stripped.length).trim();
    var quotChar = surroundingQuote(rhs);
    var v = unquote(rhs);
    if (quotChar == _singleQuot) {
      v = v.replaceAll("\\'", "'");
      return {k: v};
    }
    if (quotChar == '"') {
      v = v.replaceAll('\\"', '"').replaceAll('\\n', '\n');
    }
    final interpolatedValue = interpolate(v, env).replaceAll("\\\$", "\$");
    return {k: interpolatedValue};
  }

  /// Substitutes $bash_vars in [val] with values from [env].
  String interpolate(String val, Map<String, String?> env) => val.replaceAllMapped(_bashVar, (m) {
        if ((m.group(1) ?? "") == "\\") {
          return m.input.substring(m.start, m.end);
        } else {
          var k = m.group(3)!;
          if (!_has(env, k)) return '';
          return env[k]!;
        }
      });

  /// If [val] is wrapped in single or double quotes, returns the quote character.
  /// Otherwise, returns the empty string.

  String surroundingQuote(String val) {
    if (!_surroundQuotes.hasMatch(val)) return '';
    return _surroundQuotes.firstMatch(val)!.group(1)!;
  }

  /// Removes quotes (single or double) surrounding a value.
  String unquote(String val) {
    if (!_surroundQuotes.hasMatch(val)) {
      return strip(val, includeQuotes: true).trim();
    }
    return _surroundQuotes.firstMatch(val)!.group(2)!;
  }

  /// Strips comments (trailing or whole-line).
  String strip(String line, {bool includeQuotes = false}) =>
      line.replaceAll(includeQuotes ? _commentWithQuotes : _comment, '').trim();

  /// Omits 'export' keyword.
  String swallow(String line) => line.replaceAll(_leadingExport, '').trim();

  bool _isValid(String s) => s.isNotEmpty && s.contains('=');

  /// [ null ] is a valid value in a Dart map, but the env var representation is empty string, not the string 'null'
  bool _has(Map<String, String?> map, String key) => map.containsKey(key) && map[key] != null;
}
