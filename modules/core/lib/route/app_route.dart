// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/material.dart';

import 'app_route_builder.dart';

abstract class AppRoute {
  static final Map<String, AppRouteBuilder> _routers = {};

  void registerRoute(AppRouteBuilder builder) {
    _routers[builder.name] = builder;
  }

  Route<dynamic> onGenerateRoute(RouteSettings settings) {
    final route = _routers[settings.name];
    if (route == null) {
      throw Exception('Not found route with name ${settings.name}');
    }

    return route.builder(settings);
  }
}
