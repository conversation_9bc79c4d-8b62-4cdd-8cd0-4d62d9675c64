// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

abstract class AppRouteBuilder {
  abstract final String name;

  Route<T> builder<T>(RouteSettings settings);
}

abstract class AppPageRouteBuilder implements AppRouteBuilder {
  WidgetBuilder create(RouteSettings settings);

  @override
  Route<T> builder<T>(RouteSettings settings) {
    if (Platform.isAndroid) {
      return MaterialPageRoute<T>(
        settings: settings,
        builder: create(settings),
      );
    } else {
      return CupertinoPageRoute<T>(
        settings: settings,
        builder: create(settings),
      );
    }
  }
}

abstract class PageTransitionRouteBuilder implements AppRouteBuilder {
  static const Duration kDefaultTransitionDuration = Duration(milliseconds: 300);

  Duration get transitionDuration => kDefaultTransitionDuration;

  Duration get reverseTransitionDuration => kDefaultTransitionDuration;

  bool get opaque => true;

  bool get barrierDismissible => false;

  Color? get barrierColor => null;

  String? get barrierLabel => null;

  bool get maintainState => true;

  bool get fullscreenDialog => false;

  bool get allowSnapshotting => true;

  RoutePageBuilder create(RouteSettings settings);

  Widget transitionsBuilder(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  );

  @override
  Route<T> builder<T>(RouteSettings settings) {
    return PageRouteBuilder<T>(
      settings: settings,
      transitionDuration: transitionDuration,
      reverseTransitionDuration: reverseTransitionDuration,
      opaque: opaque,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor,
      barrierLabel: barrierLabel,
      maintainState: maintainState,
      fullscreenDialog: fullscreenDialog,
      allowSnapshotting: allowSnapshotting,
      transitionsBuilder: transitionsBuilder,
      pageBuilder: create(settings),
    );
  }
}

abstract class ModalBottomSheetRouteBuilder implements AppRouteBuilder {
  static const kDefaultScrollControlDisabledMaxHeightRatio = 9.0 / 16.0;

  AnimationStyle? get sheetAnimationStyle => null;

  BoxConstraints? get constraints => null;

  bool get isScrollControlled => false;

  ShapeBorder? get shape => null;

  Color? get backgroundColor => null;

  Color? get barrierColor => null;

  String? get barrierLabel => null;

  double? get elevation => null;

  double get scrollControlDisabledMaxHeightRatio => kDefaultScrollControlDisabledMaxHeightRatio;

  bool get isDismissible => true;

  bool get enableDrag => true;

  WidgetBuilder create(RouteSettings settings);

  @override
  Route<T> builder<T>(RouteSettings settings) {
    return ModalBottomSheetRoute<T>(
      builder: create(settings),
      settings: settings,
      isScrollControlled: isScrollControlled,
      constraints: constraints,
      scrollControlDisabledMaxHeightRatio: scrollControlDisabledMaxHeightRatio,
      shape: shape,
      modalBarrierColor: barrierColor,
      barrierLabel: barrierLabel,
      elevation: elevation,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      backgroundColor: backgroundColor,
      sheetAnimationStyle: sheetAnimationStyle,
    );
  }
}
