// Sunday, 27th August 2023 05:50 PM
// quanghuuxx (<EMAIL>)
// -----
// Copyright 2023 quanghuuxx, Ltd. All rights reserved.

import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:rxdart/rxdart.dart';

import 'alert_builder.dart';
import 'model/alert_priority.dart';

part './model/alert_info.dart';

abstract interface class IAlertService {
  final AlertBuilder builder;

  IAlertService({
    required this.builder,
  });

  Future<T?> add<T>(AlertInfo info);

  void closeCurrentAlert([bool Function(AlertInfo? info)? test]);

  void clearAndCloseAlerts();

  Future<void> close();
}

abstract class AlertService extends IAlertService {
  AlertService({required super.builder});

  final BehaviorSubject<AlertInfo?> stream = BehaviorSubject<AlertInfo?>();
  final List<AlertInfo> _alerts = <AlertInfo>[];

  AlertInfo? _currentAlert;

  @override
  Future<T?> add<T>(AlertInfo info) {
    if (_currentAlert == null) {
      return _showing(info);
    } else {
      final completer = Completer<T>();
      _alerts
        ..add(info.._completer = completer)
        ..sort((a, b) => a.piority.index.compareTo(b.piority.index));

      if (_currentAlert!.piority.index < info.piority.index) {
        closeCurrentAlert();
      }

      return completer.future;
    }
  }

  Future<T?> _showing<T>(AlertInfo info) async {
    stream.add(_currentAlert = info);

    final T? result = await builder.build(info);

    info._completer?.complete(result);

    if (_alerts.isNotEmpty) {
      _showing(_alerts.removeLast());
    } else {
      stream.add(_currentAlert = null);
    }

    return result;
  }

  @override
  void closeCurrentAlert([bool Function(AlertInfo? info)? test]) async {
    if (_currentAlert == null || test?.call(_currentAlert) == false) return;

    return builder.closeCurrentAlert();
  }

  @override
  void clearAndCloseAlerts() async {
    _alerts.clear();
    closeCurrentAlert();
  }

  @override
  Future<void> close() {
    return stream.close();
  }
}
