// Sunday, 27th August 2023 05:56 PM
// quanghuuxx (<EMAIL>)
// -----
// Copyright 2023 quanghuuxx, Ltd. All rights reserved.

part of '../alert_service.dart';

abstract class AlertInfo {
  final AlertPiority piority;
  final Widget Function(BuildContext ctx)? viewGenerator;

  // ignore: prefer_final_fields
  Completer? _completer;

  AlertInfo({
    required this.piority,
    this.viewGenerator,
  }) : _completer = null;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is AlertInfo && other.piority == piority && other.viewGenerator == viewGenerator;
  }

  @override
  int get hashCode => piority.hashCode ^ viewGenerator.hashCode;
}
