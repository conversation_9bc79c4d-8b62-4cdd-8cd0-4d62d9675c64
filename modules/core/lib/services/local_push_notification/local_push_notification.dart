// Flutter project by quanghu<PERSON>x (<EMAIL>)

// Friday, 29th July 2022 10:29 PM
// quanghuuxx (<EMAIL>)
// -----
// Copyright 2022 quanghuuxx, Ltd. All rights reserved.

import 'dart:async';
import 'dart:ui';

import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:timezone/data/latest_all.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

import '../../core.dart';

class LocalPushNotificationService {
  final FlutterLocalNotificationsPlugin _localNotificationsPlugin =
      FlutterLocalNotificationsPlugin();
  bool? _didInit;

  LocalPushNotificationService._internal() {
    tz.initializeTimeZones();
  }

  static final LocalPushNotificationService instance =
      LocalPushNotificationService._internal();

  factory LocalPushNotificationService() {
    return instance;
  }

  final StreamController<NotificationResponse?> didTapNotification =
      StreamController.broadcast();

  tz.Location? _location;

  FutureOr<bool?> _init() async {
    if (_didInit == true) return true;
    const initializationSettingsAndroid =
        AndroidInitializationSettings('@drawable/ic_notification_w');

    const initializationSettingsIOS = DarwinInitializationSettings();
    const initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    _didInit = await _localNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (payload) => didTapNotification.sink
          .add(payload..log(tag: 'onSelectNotification')),
    );
    return _didInit;
  }

  void show(
    int id,
    String title,
    String body,
    String? payload,
  ) async {
    if (await _init() == false) return;

    _localNotificationsPlugin.show(
      id,
      title,
      body,
      _getNotificationDetails(),
      payload: payload,
    );
  }

  void schedule(
    int id,
    String title, {
    String? body,
    String? payload,
    required DateTime dateTime,
    DateTimeComponents? dateTimeComponents,
    String? channelId,
    String? channelName,
    String? channelDescription,
    Priority? priority,
  }) async {
    if (await _init() == false) return;

    final date = tz.TZDateTime.from(dateTime, await localLocation());

    _localNotificationsPlugin.zonedSchedule(
      id,
      title,
      body,
      date,
      _getNotificationDetails(
        channelId: channelId,
        channelName: channelName,
        channelDescription: channelDescription,
        priority: priority,
      ),
      payload: payload,
      androidScheduleMode: AndroidScheduleMode.inexactAllowWhileIdle,
      matchDateTimeComponents: dateTimeComponents,
    );
  }

  Future<void> didNotificationLaunchApp() async {
    final notificationAppLaunchDetails =
        await _localNotificationsPlugin.getNotificationAppLaunchDetails();
    if (notificationAppLaunchDetails?.didNotificationLaunchApp == true) {
      didTapNotification
          .add(notificationAppLaunchDetails?.notificationResponse);
    }
  }

  Future<void> configureLocalTimeZone() async {
    final local = await localLocation();
    tz.setLocalLocation(local);
  }

  FutureOr<tz.Location> localLocation() async {
    if (_location != null) {
      return _location!;
    }

    final String currentTimeZone = await FlutterTimezone.getLocalTimezone();
    return _location = tz.getLocation(currentTimeZone);
  }

  NotificationDetails _getNotificationDetails({
    String? channelId,
    String? channelName,
    String? channelDescription,
    Priority? priority,
  }) {
    return NotificationDetails(
      android: AndroidNotificationDetails(
        channelId ?? 'work_m',
        channelName ?? 'Work-m notification',
        channelDescription: channelDescription,
        importance: Importance.max,
        priority: priority ?? Priority.max,
        icon: '@drawable/ic_notification_w' '',
        color: const Color(0xff2e64ec),
        sound: const RawResourceAndroidNotificationSound('notification_sound'),
      ),
      iOS: const DarwinNotificationDetails(
        sound: 'notification_sound.m4r',
        presentAlert: true,
      ),
    );
  }

  Future<List<PendingNotificationRequest>> pendingNotificationRequests() {
    return _localNotificationsPlugin.pendingNotificationRequests();
  }

  Future<void> cancelNotification(int id) {
    return _localNotificationsPlugin.cancel(id);
  }
}
