// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/material.dart';

import '../../core.dart';

class NavigatorObsService extends RouteObserver<PageRoute> {
  NavigatorObsService._internal();

  static final NavigatorObsService instance = NavigatorObsService._internal();
  static final GlobalKey<NavigatorState> key = GlobalKey<NavigatorState>();

  BuildContext get context {
    final ctx = key.currentState?.context;
    if (ctx == null) {
      throw Exception('NavigatorObsService.key not yet assigned to MaterialApp.navigatorKey');
    }
    return ctx;
  }

  Route? _lastRoute;
  Route? _currentRoute;

  Route get lastRoute => _lastRoute!;
  Route get currentRoute => _currentRoute!;

  @override
  void didPop(Route route, Route? previousRoute) {
    _trackingRoute(previousRoute, route, didRoute: 'didPop');
    super.didPop(route, previousRoute);
  }

  @override
  void didPush(Route route, Route? previousRoute) {
    _trackingRoute(route, previousRoute, didRoute: 'didPush');
    super.didPush(route, previousRoute);
  }

  @override
  void didReplace({Route? newRoute, Route? oldRoute}) {
    _trackingRoute(newRoute, oldRoute, didRoute: 'didReplace');
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
  }

  void _trackingRoute(
    Route? current,
    Route? previous, {
    required String didRoute,
  }) {
    if (current?.hasName == true && previous?.hasName == true) {
      _currentRoute = current;
      _lastRoute = previous;

      Console.log(
        '$didRoute ${lastRoute.name} -> ${currentRoute.name}',
        name: 'NavigatorObsService',
      );
    } else if (current?.hasName == true) {
      _currentRoute = current;
      if (didRoute != 'didPop') {
        Console.log(
          'Warring direct route !!! $didRoute [${previous.runtimeType}] -> [${currentRoute.runtimeType}]${currentRoute.name}',
          name: 'NavigatorObsService',
        );
      }
    }
  }
}

extension RouteExt on Route {
  String get name {
    return settings.name ?? '';
  }

  Object? get arguments {
    return settings.arguments;
  }

  bool get hasName {
    return settings.name?.isNotEmpty == true;
  }
}
