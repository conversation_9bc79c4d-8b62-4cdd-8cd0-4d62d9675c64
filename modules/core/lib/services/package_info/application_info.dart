// Flutter project by quanghuuxx (<EMAIL>)

import 'package:package_info_plus/package_info_plus.dart';

class ApplicationInfo {
  final String version;
  final String packageName;
  final String buildNumber;
  final String appName;

  ApplicationInfo._({
    required this.version,
    required this.packageName,
    required this.buildNumber,
    required this.appName,
  });

  factory ApplicationInfo.fromPlatform(PackageInfo info) {
    return ApplicationInfo._(
      version: info.version,
      packageName: info.packageName,
      buildNumber: info.buildNumber,
      appName: info.appName,
    );
  }
}
