// Flutter project by quanghuuxx (<EMAIL>)

import 'package:device_info_plus/device_info_plus.dart';

import '../../core.dart';

class DeviceInfo {
  final String version;
  final String name;
  final bool isPhysicalDevice;

  DeviceInfo._({required this.version, required this.name, required this.isPhysicalDevice});

  factory DeviceInfo.fromDevice(BaseDeviceInfo packageInfo) {
    if (packageInfo is AndroidDeviceInfo) {
      return DeviceInfo._(
        version: packageInfo.version.release,
        name: packageInfo.model,
        isPhysicalDevice: packageInfo.isPhysicalDevice,
      );
    } else if (packageInfo is IosDeviceInfo) {
      return DeviceInfo._(
        version: packageInfo.systemVersion,
        name: packageInfo.utsname.machine,
        isPhysicalDevice: packageInfo.isPhysicalDevice,
      );
    }

    throw BaseException(code: kDevErrorCode, description: 'DeviceInfo not supported');
  }
}
