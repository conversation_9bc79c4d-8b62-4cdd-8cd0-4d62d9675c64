// Flutter project by quanghuuxx (<EMAIL>)

import 'package:device_info_plus/device_info_plus.dart' as device_info;
import 'package:package_info_plus/package_info_plus.dart' as package_info;

import '../../core.dart';

class PackageInfo {
  PackageInfo._();

  static final PackageInfo instance = PackageInfo._();

  bool _didInit = false;

  late final ApplicationInfo application;
  late final DeviceInfo device;

  Future<void> initialization() async {
    if (_didInit) {
      return;
    }

    _didInit = true;
    try {
      final platform = await package_info.PackageInfo.fromPlatform();
      application = ApplicationInfo.fromPlatform(platform);

      final deviced = await device_info.DeviceInfoPlugin().deviceInfo;
      device = DeviceInfo.fromDevice(deviced);
    } catch (e) {
      _didInit = false;
      throw BaseException.from(e);
    }
  }
}
