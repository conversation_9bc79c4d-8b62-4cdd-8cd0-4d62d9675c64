// Flutter project by quanghu<PERSON><PERSON> (<EMAIL>)
// Saturday, 17th December 2022 11:22 AM
// quanghuuxx (<EMAIL>)
// -----
// Copyright 2022 quanghuuxx, Ltd. All rights reserved.

import 'dart:async';
import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

class SharedPreferencesService {
  bool? _didInitialed;
  late SharedPreferences _preferences;

  SharedPreferencesService._internal();

  static final SharedPreferencesService instance = SharedPreferencesService._internal();

  FutureOr<void> initialization() async {
    if (_didInitialed == true) return;
    _preferences = await SharedPreferences.getInstance();
    _didInitialed = true;
  }

  Future<bool> setString(String key, String value) async {
    await initialization();
    return _preferences.setString(key, value);
  }

  Future<bool> setInt(String key, int value) async {
    await initialization();
    return _preferences.setInt(key, value);
  }

  Future<bool> setBool(String key, bool value) async {
    await initialization();
    return _preferences.setBool(key, value);
  }

  Future<bool> setMap(String key, Map<String, dynamic> value) async {
    await initialization();
    return _preferences.setString(key, jsonEncode(value));
  }

  Set<String> getKeys() {
    return _preferences.getKeys();
  }

  String? getString(String key) {
    return _preferences.getString(key);
  }

  int? getInt(String key) {
    return _preferences.getInt(key);
  }

  bool? getBool(String key) {
    return _preferences.getBool(key);
  }

  Map<String, dynamic>? getMap(String key) {
    final json = _preferences.getString(key);
    if (json == null) return null;
    return jsonDecode(json);
  }
}
