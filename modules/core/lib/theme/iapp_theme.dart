// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../common_const.dart';
import 'iapp_theme_color.dart';
import 'iapp_theme_decoration.dart';
import 'iapp_theme_text.dart';

extension IAppThemeExt on ThemeData {
  ThemeData withAppTheme({
    required Brightness brightness,
    required IAppThemeColor themeColor,
    IAppThemeText? themeText,
    IAppThemeDecoration? themeDecoration,
  }) {
    return copyWith(
      extensions: [
        themeColor,
        if (themeText != null) themeText,
        if (themeDecoration != null) themeDecoration,
      ],
      brightness: brightness,
      primaryColor: themeColor.primary,
      unselectedWidgetColor: themeColor.neutral400,
      hintColor: themeColor.hint,
      dividerColor: themeColor.neutral200,
      shadowColor: themeColor.neutral300,
      highlightColor: Colors.white12,
      splashColor: Colors.white24,
      scaffoldBackgroundColor: themeColor.background,
      focusColor: Colors.transparent,
      colorScheme: ColorScheme(
        brightness: brightness,
        primary: primaryColor,
        onPrimary: themeColor.onPrimary,
        secondary: themeColor.secondary,
        onSecondary: themeColor.onSecondary,
        error: themeColor.neutralE,
        onError: themeColor.white,
        surface: themeColor.surface,
        onSurface: themeColor.onSurface,
        surfaceTint: themeColor.background,
      ),
      textTheme: TextTheme(
        displayLarge: themeText?.headline1,
        displayMedium: themeText?.headline2,
        displaySmall: themeText?.headline3,
        headlineMedium: themeText?.headline4,
        headlineSmall: themeText?.headline5,
        titleLarge: themeText?.headline6,
        titleMedium: themeText?.headline6,
        titleSmall: themeText?.headline6,
        bodyLarge: themeText?.bodyText0,
        bodyMedium: themeText?.bodyText1,
        bodySmall: themeText?.bodyText2,
        labelMedium: themeText?.caption,
        labelSmall: themeText?.overline,
        labelLarge: themeText?.button,
      ),
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.all<Color>(themeColor.primary),
      ),
      tabBarTheme: TabBarTheme(
        labelStyle: themeText?.bodyText1.copyWith(
          fontWeight: FontWeight.bold,
        ),
        indicatorColor: themeColor.secondary,
        unselectedLabelColor: themeColor.neutral400,
        unselectedLabelStyle: themeText?.bodyText1.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
      appBarTheme: AppBarTheme(
        systemOverlayStyle: systemOverlayStyleForBrightness(themeColor.background),
        backgroundColor: themeColor.background,
        actionsIconTheme: IconThemeData(
          color: themeColor.primary,
        ),
      ),
      buttonTheme: ButtonThemeData(
        buttonColor: themeColor.primary,
        splashColor: themeColor.neutral50,
        disabledColor: themeColor.neutral400,
        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      iconTheme: IconThemeData(
        color: themeColor.onPrimary,
      ),
      chipTheme: ChipThemeData(
        backgroundColor: themeColor.neutral200,
        selectedColor: themeColor.secondary,
        disabledColor: themeColor.neutral300,
        secondarySelectedColor: themeColor.neutral300,
        surfaceTintColor: Colors.transparent,
        showCheckmark: false,
        iconTheme: iconTheme,
      ),
      bottomSheetTheme: BottomSheetThemeData(
        backgroundColor: themeColor.surface,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: kRadius12,
            topRight: kRadius12,
          ),
        ),
      ),
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: themeColor.secondary,
        circularTrackColor: themeColor.secondary100,
      ),

      /// [PrimaryButton] theme
      filledButtonTheme: FilledButtonThemeData(
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.disabled)) {
              return themeColor.neutral400;
            }
            return themeColor.primary;
          }),
          foregroundColor: WidgetStatePropertyAll(themeColor.white),
        ),
      ),

      /// [SecondaryButton] theme
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: ButtonStyle(
          foregroundColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.disabled)) {
              return themeColor.neutral400;
            }
            return themeColor.primary;
          }),
          side: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.disabled)) {
              return BorderSide(color: themeColor.neutral400, width: 1.5);
            }
            return BorderSide(color: themeColor.primary, width: 1.5);
          }),
          overlayColor: WidgetStatePropertyAll(themeColor.primary200),
          backgroundColor: const WidgetStatePropertyAll(Colors.transparent),
        ),
      ),

      /// [GhostButton] theme
      textButtonTheme: TextButtonThemeData(
        style: ButtonStyle(
          foregroundColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.disabled)) {
              return themeColor.neutral400;
            }
            return themeColor.primary;
          }),
          overlayColor: WidgetStatePropertyAll(themeColor.primary200),
          backgroundColor: const WidgetStatePropertyAll(Colors.transparent),
        ),
      ),
      sliderTheme: SliderThemeData(
        trackHeight: kThree,
        rangeTrackShape: const RoundedRectRangeSliderTrackShape(),
        overlayShape: const RoundSliderThumbShape(enabledThumbRadius: 1),
        activeTrackColor: themeColor.primary,
        inactiveTrackColor: themeColor.neutral400,
        thumbColor: themeColor.primary,
        showValueIndicator: ShowValueIndicator.onlyForContinuous,
        valueIndicatorColor: themeColor.primary400,
        valueIndicatorShape: const PaddleSliderValueIndicatorShape(),
        thumbShape: const RoundSliderThumbShape(enabledThumbRadius: kEight),
      ),
      iconButtonTheme: IconButtonThemeData(
        style: ButtonStyle(
          foregroundColor: WidgetStatePropertyAll(themeColor.onSurface),
          overlayColor: WidgetStatePropertyAll(themeColor.neutral300),
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
      ),

      // switchTheme: SwitchThemeData(
      // thumbColor: MaterialStateProperty.resolveWith(
      //   (Set<MaterialState> states) {
      //     if (states.contains(MaterialState.selected)) {
      //       return themeColor.primary;
      //     }
      //     return themeColor.onSurface;
      //   },
      // ),
      // trackColor: MaterialStateProperty.resolveWith(
      //   (Set<MaterialState> states) {
      //     if (states.contains(MaterialState.selected)) {
      //       return themeColor.primary200;
      //     }
      //     return themeColor.hint;
      //   },
      // ),
      // ),
    );
  }

  /// using with component AnnotatedRegion<SystemUiOverlayStyle> to change status bar color
  SystemUiOverlayStyle systemOverlayStyleForBrightness(Color backgroundColor) {
    final brightness = ThemeData.estimateBrightnessForColor(backgroundColor);
    final SystemUiOverlayStyle style =
        brightness == Brightness.dark ? SystemUiOverlayStyle.light : SystemUiOverlayStyle.dark;
    return SystemUiOverlayStyle(
      statusBarColor: backgroundColor,
      statusBarBrightness: style.statusBarBrightness,
      statusBarIconBrightness: style.statusBarIconBrightness,
      systemStatusBarContrastEnforced: style.systemStatusBarContrastEnforced,
    );
  }

  T byBrightness<T>({
    required T light,
    required T dark,
  }) {
    return brightness == Brightness.light ? light : dark;
  }

  T appThemeColor<T extends IAppThemeColor>() {
    return extension<T>()!;
  }

  T? appThemeText<T extends IAppThemeText>() {
    return extension<T>();
  }

  T? appThemeDecoration<T extends IAppThemeDecoration>() {
    return extension<T>();
  }
}

extension BrightnessExt on Brightness {
  Brightness get opposite => this == Brightness.dark ? Brightness.light : Brightness.dark;
}
