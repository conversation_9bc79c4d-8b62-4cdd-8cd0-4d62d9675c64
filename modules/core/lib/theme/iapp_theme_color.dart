// Flutter project by quanghuux<PERSON> (<EMAIL>)

// ignore_for_file: non_constant_identifier_names

import 'package:flutter/material.dart';

abstract class IAppThemeColor implements ThemeExtension<IAppThemeColor> {
  Color get primary;
  Color get onPrimary;
  Color get primary50;
  Color get primary100;
  Color get primary200;
  Color get primary300;
  Color get primary400;
  Color get primary500;
  Color get primary600;
  Color get primary700;
  Color get primary800;
  Color get primary900;

  Color get secondary;
  Color get onSecondary;
  Color get secondary50;
  Color get secondary100;
  Color get secondary200;
  Color get secondary300;
  Color get secondary400;
  Color get secondary500;
  Color get secondary600;
  Color get secondary700;
  Color get secondary800;
  Color get secondary900;

  Color get neutral50;
  Color get neutral100;
  Color get neutral200;
  Color get neutral300;
  Color get neutral400;
  Color get neutral500;
  Color get neutral600;
  Color get neutral700;
  Color get neutral800;
  Color get neutral900;

  Color get surface;
  Color get onSurface;

  Color get background;
  Color get onBackground;

  Color get white;
  Color get black;
  Color get hint;
  Color get disabled;

  /// (rã<PERSON>, khe) màu background của c<PERSON><PERSON> đ<PERSON>, khối progress
  /// ex:
  /// - CircularProgressIndicator.backgroundColor
  /// - LinearProgressIndicator.backgroundColor
  Color get trench;

  Color get neutralS;
  Color get neutralE;
  Color get neutralM;

  Gradient? get g1;
  Gradient? get g2;
  Gradient? get g3;
  Gradient? get g4;
  Gradient? get g5;
  Gradient? get g6;
}
