// Flutter project by quang<PERSON><PERSON><PERSON> (<EMAIL>)

// Saturday, 27th May 2023 09:49 AM
// quanghuuxx (<EMAIL>)
// -----
// Copyright 2023 quanghuuxx, Ltd. All rights reserved.

import 'package:flutter/material.dart';

abstract class IAppThemeText implements ThemeExtension<IAppThemeText> {
  TextStyle get headline1;
  TextStyle get headline2;
  TextStyle get headline3;
  TextStyle get headline4;
  TextStyle get headline5;
  TextStyle get headline6;

  TextStyle get bodyText0;
  TextStyle get bodyText1;
  TextStyle get bodyText2;

  TextStyle get caption;
  TextStyle get button;
  TextStyle get overline;
}
