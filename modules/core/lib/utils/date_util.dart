// Flutter project by quanghuuxx (<EMAIL>)

// Saturday, 8th July 2023 05:39 PM
// quanghuuxx (<EMAIL>)
// -----
// Copyright 2023 quanghuuxx, Ltd. All rights reserved.

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class DateUtil {
  static String fullInfoOfDatePattern = 'HH:mm dd/MM/yyyy';
  static String fullHourPattern = 'HH:mm:ss';
  static String hourAndMinutePattern = 'HH:mm';
  static String fullDayPattern = 'dd/MM/yyyy';
  static String hourWithDayPattern = 'HH:mm dd/MM';

  // TODO: implement get diffTime from server
  static Duration diffTime = Duration.zero;

  /// get ngày giờ hiện tại của device - [diffTime], dạng timestamp (dừng ở đơn vị giây).
  static int get timestamp => DateTime.now().subtract(diffTime).millisecondsSinceEpoch ~/ 1000;

  /// get ngày giờ hiện tại của device - [diffTime], nhưng chỉ lấy đến đơn vị phút.
  static DateTime get now {
    final n = DateTime.now().subtract(diffTime);
    return DateTime(n.year, n.month, n.day, n.hour, n.minute);
  }

  static String format(
    String pattern, {
    String? locale,
    required DateTime date,
  }) {
    DateFormat? _formater = DateFormat(pattern, locale);
    final format = _formater.format(date);
    _formater = null;
    return format;
  }

  /// hour24:minute:second
  static String hm(DateTime date) {
    DateFormat? _formater = DateFormat.Hm();
    final format = _formater.format(date);
    _formater = null;
    return format;
  }

  /// hour12:minute:second
  static String jms(DateTime date) {
    DateFormat? _formater = DateFormat.jms();
    final format = _formater.format(date);
    _formater = null;
    return format;
  }

  static String yMMMd(DateTime date, {String? locale, bool addJM = true}) {
    DateFormat? _formater = DateFormat.yMMMd(locale);
    if (addJM) {
      _formater = _formater.add_jm();
    }
    final format = _formater.format(date);
    _formater = null;
    return format;
  }

  static String yMMMMEEEEd(DateTime date, {String? locale}) {
    DateFormat? _formater = DateFormat.yMMMMEEEEd(locale);
    final format = _formater.format(date);
    _formater = null;
    return format;
  }

  static String yMMMM(DateTime date, {String? locale}) {
    DateFormat? _formater = DateFormat.yMMMM(locale);
    final format = _formater.format(date);
    _formater = null;
    return format;
  }

  /// return number day of [month]
  static int daysOfMonth({required int year, required int month}) {
    return DateUtils.getDaysInMonth(year, month);
  }

  /// this is last day of [month]
  static DateTime lastDayOfMonth({required int year, required int month}) {
    final day = daysOfMonth(year: year, month: month);
    return DateTime(year, month, day);
  }
}
