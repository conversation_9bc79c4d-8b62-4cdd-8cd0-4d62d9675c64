// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/cupertino.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/scheduler.dart';

import '../helper.dart';

class HelperUtil {
  static Future<ui.Image> decodeUIImage(
    Uint8List bytes, {
    int? targetWidth,
    int? targetHeight,
  }) async {
    final codec = await ui.instantiateImageCodec(
      bytes,
      targetWidth: targetWidth,
      targetHeight: targetHeight,
    );
    final frame = await codec.getNextFrame();
    return frame.image;
  }

  static Future<ui.Image> getUIImage(
    ImageProvider provider, {
    ImageConfiguration configuration = const ImageConfiguration(),
  }) async {
    final ImageStream stream = provider.resolve(configuration);
    final Completer<ui.Image> completer = Completer();

    ImageStreamListener? listener;
    listener = ImageStreamListener(
      (ImageInfo info, bool synchronousCall) {
        completer.complete(info.image);

        SchedulerBinding.instance.addPostFrameCallback(
          (Duration timeStamp) {
            stream.removeListener(listener!);
          },
          debugLabel: 'getUIImage.removeListener',
        );
      },
      onError: completer.completeError,
    );

    stream.addListener(listener);

    return completer.future;
  }

  static ImageProvider getImageProvider(String source) {
    if (source.startsWith('http')) {
      return NetworkImage(source);
    } else if (isLocalUrl(source)) {
      return FileImage(File(source));
    } else {
      return AssetImage(source);
    }
  }
}
