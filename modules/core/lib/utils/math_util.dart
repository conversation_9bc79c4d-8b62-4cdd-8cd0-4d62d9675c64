// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:math';
import 'dart:ui';

class MathUtil {
  /// Tính tọa độ điểm sau khi xoay
  /// [point] là điểm ban đầu (x, y)
  /// [angle] là góc xoay (đơn vị radian)
  /// [center] là điểm tâm xoay (cx, cy), mặc định là gốc tọa độ (0, 0)
  /// Note:
  /// Cũng có thể dùng hàm rotated3(Vec3(point.dx, point.dy, 0)) của 1 Matrix4 đã được xoay góc [angle]
  /// sẽ cho kết quả Offset(vec.x, vec.y) tương tự , với [center] luôn là Offset.zero
  static Offset rotatePoint(
    Offset point,
    double angle, {
    Offset center = const Offset(0, 0),
  }) {
    // Dịch chuyển điểm về gốc tọa độ
    double translatedX = point.dx - center.dx;
    double translatedY = point.dy - center.dy;

    final sinA = sin(angle);
    final cosA = cos(angle);

    // Áp dụng công thức xoay
    double rotatedX = translatedX * cosA - translatedY * sinA;
    double rotatedY = translatedX * sinA + translatedY * cosA;

    // Dịch ngược lại điểm về vị trí ban đầu
    rotatedX += center.dx;
    rotatedY += center.dy;

    return Offset(rotatedX, rotatedY);
  }

  /// Chuyển đổi góc từ radian sang độ
  static double radians2Degrees(double radians) {
    return radians * 180 / pi;
  }

  /// Chuyển đổi góc từ độ sang radian
  static double degrees2Radians(double degrees) {
    return degrees * pi / 180;
  }
}
