// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:math';

import 'package:flutter/rendering.dart';
import 'package:vector_math/vector_math_64.dart';

class Matrix4Util {
  /// <PERSON><PERSON><PERSON> tính toán góc xoay Z từ Matrix4 (đơn vị: radian)
  static double getRotationZ(Matrix4 matrix) {
    // Trích xuất các giá trị cosine và sine từ ma trận
    final double cosTheta = matrix.entry(0, 0);
    final double sinTheta = matrix.entry(1, 0);

    // Sử dụng atan2 để tính góc xoay từ sine và cosine
    return atan2(sinTheta, cosTheta); // Kết quả là góc xoay tính theo radian
  }

  /// Lấy thông số tịnh tiến (translate) từ Matrix4
  static Offset getTranslation(Matrix4 matrix) {
    final double translateX = matrix.entry(0, 3);
    final double translateY = matrix.entry(1, 3);
    return Offset(translateX, translateY);
  }

  /// L<PERSON>y thông số scale từ Matrix4
  static Vector3 getScale(Matrix4 matrix) {
    final double scaleX = matrix.getColumn(0).xyz.length; // Độ dài vector cột 0
    final double scaleY = matrix.getColumn(1).xyz.length; // Độ dài vector cột 1
    final double scaleZ = matrix.getColumn(2).xyz.length; // Độ dài vector cót 2
    return Vector3(scaleX, scaleY, scaleZ);
  }

  static Matrix4 rotationZ(double radians, {Offset origin = Offset.zero}) {
    return Matrix4.identity()
      ..translate(origin.dx, origin.dy)
      ..rotateZ(radians)
      ..translate(-origin.dx, -origin.dy);
  }

  static Matrix4 scale({
    double scaleX = 0,
    double scaleY = 0,
    Offset origin = Offset.zero,
  }) {
    return Matrix4.identity()
      ..translate(origin.dx, origin.dy)
      ..scale(scaleX, scaleY)
      ..translate(-origin.dx, -origin.dy);
  }
}
