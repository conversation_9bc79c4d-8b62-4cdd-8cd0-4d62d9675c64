// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:io';

import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';

class PathUtil {
  static late final Directory _tempDir, _documentsDir, _downloadsDir;

  static Future<void> initial() async {
    _tempDir = await getTemporaryDirectory();
    _documentsDir = await getApplicationDocumentsDirectory();
    _downloadsDir = await getApplicationSupportDirectory();
  }

  static String get tempPath => _tempDir.path;
  static String get documentsPath => _documentsDir.path;
  static String get downloadsPath => _downloadsDir.path;

  static String basename(String path) {
    return p.basename(path);
  }

  static String extension(String path, [int level = 1]) {
    return p.extension(path, level);
  }

  static String recognizer(String path) {
    return extension(path).substring(1);
  }

  static String join(
    String part1, [
    String? part2,
    String? part3,
    String? part4,
    String? part5,
    String? part6,
    String? part7,
    String? part8,
    String? part9,
    String? part10,
  ]) {
    return p.join(part1, part2, part3, part4, part5, part6, part7, part8, part9, part10);
  }
}
