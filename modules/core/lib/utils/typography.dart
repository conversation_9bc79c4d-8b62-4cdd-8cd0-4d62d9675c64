// Flutter project by quanghuux<PERSON> (<EMAIL>)

import 'dart:async';
import 'dart:io';

import 'package:flutter/painting.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' show Client;
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';

import '../core.dart';

class AppTypography {
  /// Mapping from font weight types to the 'weight' part of the Google Fonts API
  /// specific filename.
  static const _fontWeightToFilenameWeightParts = {
    FontWeight.w100: 'Thin',
    FontWeight.w200: 'ExtraLight',
    FontWeight.w300: 'Light',
    FontWeight.w400: 'Regular',
    FontWeight.w500: 'Medium',
    FontWeight.w600: 'SemiBold',
    FontWeight.w700: 'Bold',
    FontWeight.w800: 'ExtraBold',
    FontWeight.w900: 'Black',
  };

  static final RegExp assetsPathRegex = RegExp(r'^(?:[a-zA-Z0-9_\-\/]+)\.(ttf|otf)$');

  static final Map<String, List<FontVariant>> _fontVariants = {};

  static final Set<String> _loadedFonts = {};

  static final Set<Future<void>> _pendingFontFutures = {};

  /// Registers fonts to be loaded.
  /// [fontVariants] is a map from font family name to a list of font variants.
  /// With key as font family name, value is a list of font variant is a [FontVariant].
  /// ```dart
  /// PSTypography.registerFonts({
  ///   'Roboto': [
  ///     PSFontVariant(
  ///       fontWeight: FontWeight.w400,
  ///       fontStyle: FontStyle.normal,
  ///       uri: 'https://...../Roboto-Regular.ttf', // can is network path
  ///     ),
  ///     PSFontVariant(
  ///       fontWeight: FontWeight.w400,
  ///       fontStyle: FontStyle.italic,
  ///       uri: 'assets/fonts/Roboto/Roboto-Italic.ttf', // can is asset path
  ///     ),
  ///     PSFontVariant(
  ///       fontWeight: FontWeight.w700,
  ///       fontStyle: FontStyle.normal,
  ///       uri: 'https://...../Roboto-Bold.ttf',
  ///     ),
  ///   ],
  /// });
  /// ```
  ///
  /// And then use it like this:
  /// ```dart
  /// PSTextStyle(
  ///   fontFamily: 'Roboto',
  ///   ...
  /// )
  /// ```
  static void registerFonts(Map<String, List<FontVariant>> fontVariants) {
    AppTypography._fontVariants.addAll(fontVariants);
  }

  static Future<void> awaitPendingLoadFonts([List? _]) async {
    await Future.wait(AppTypography._pendingFontFutures);
  }

  static TextStyle getTextStyle({
    FontWeight? fontWeight,
    double? fontSize,
    double? height,
    String? fontFamily,
    FontStyle? fontStyle,
    double? letterSpacing,
    Color? color,
    Color? backgroundColor,
    TextDecoration? decoration,
    double? wordSpacing,
    Paint? foreground,
    Paint? background,
    List<Shadow>? shadows,
    TextBaseline? textBaseline,
  }) {
    if (!_fontVariants.containsKey(fontFamily)) {
      return TextStyle(
        fontWeight: fontWeight,
        fontSize: fontSize,
        height: height,
        color: color,
        fontFamily: fontFamily,
        fontStyle: fontStyle,
        letterSpacing: letterSpacing?.px,
        decoration: decoration,
        backgroundColor: backgroundColor,
        wordSpacing: wordSpacing,
        foreground: foreground,
        background: background,
        shadows: shadows,
        textBaseline: textBaseline,
      );
    }

    final matchVariant = _closestMatchVariant(
      target: VariantCompare(
        fontWeight: fontWeight ?? FontWeight.w400,
        fontStyle: fontStyle ?? FontStyle.normal,
      ),
      variants: _fontVariants[fontFamily]!,
    );

    final fontFamilyWithVariant = buildFontFamilyWithVariant(fontFamily!, matchVariant);
    if (_loadedFonts.add(fontFamilyWithVariant)) {
      final loadFuture = _loadFontIfNecessary(
        fontFamily: fontFamilyWithVariant,
        variant: matchVariant,
      );

      _pendingFontFutures.add(
        loadFuture..then((_) => _pendingFontFutures.remove(loadFuture)),
      );
    }

    return TextStyle(
      fontFamily: fontFamilyWithVariant,
      fontFamilyFallback: [fontFamily],
      fontWeight: matchVariant.fontWeight,
      fontStyle: matchVariant.fontStyle,
      height: height,
      fontSize: fontSize,
      letterSpacing: letterSpacing?.px,
      decoration: decoration,
      color: color,
      backgroundColor: backgroundColor,
      wordSpacing: wordSpacing,
      foreground: foreground,
      background: background,
      shadows: shadows,
      textBaseline: textBaseline,
    );
  }

  static FontVariant _closestMatchVariant({
    required VariantCompare target,
    required List<FontVariant> variants,
  }) {
    int? bestScore;
    late FontVariant bestMatch;
    for (final variant in variants) {
      final score = target.computeMatch(variant.toVariantCompare());
      if (bestScore == null || score < bestScore) {
        bestScore = score;
        bestMatch = variant;
      }
    }
    return bestMatch;
  }

  static Future<void> _loadFontIfNecessary({
    required String fontFamily,
    required FontVariant variant,
  }) async {
    try {
      final loader = FontLoader(fontFamily);

      ByteData? bytesData;
      if (assetsPathRegex.hasMatch(variant.uri)) {
        bytesData = await rootBundle.load(variant.uri);
        loader.addFont(Future.value(bytesData));
        return loader.load();
      }

      bytesData = await tryLoadFontFromDisk(fontFamily, p.extension(variant.uri));
      if (bytesData != null) {
        loader.addFont(Future.value(bytesData));
        return loader.load();
      }

      loader.addFont(_fetchFontFromUrl(variant.uri, fontFamily));
      await loader.load();
    } catch (e, s) {
      _loadedFonts.remove(fontFamily);
      Console.log(
        'Failed to load font: $fontFamily!!',
        name: 'AppTypography',
        error: e,
        stackTrace: s,
      );
    }
  }

  static String buildFontFamilyWithVariant(String fontFamily, FontVariant variant) {
    final weightPart = _fontWeightToFilenameWeightParts[variant.fontWeight]!.toLowerCase();
    return '$fontFamily-${variant.fontStyle.name}-$weightPart';
  }

  static Future<ByteData?> tryLoadFontFromDisk(
    String fontFamily,
    String extFile,
  ) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$fontFamily$extFile');
    if (file.existsSync()) {
      final bytes = await file.readAsBytes();
      return ByteData.view(bytes.buffer);
    }
    return null;
  }

  static Future<ByteData> _fetchFontFromUrl(String url, String fontFamily) async {
    final response = await Client().get(Uri.parse(url));
    if (response.statusCode == 200) {
      final bytes = response.bodyBytes;
      unawaited(_saveFontFileToDisk(fontFamily, url, bytes));
      return ByteData.view(bytes.buffer);
    }
    throw Exception('Failed to load font from url: $url with status code: ${response.statusCode}');
  }

  static Future<void> _saveFontFileToDisk(String fontFamily, String url, List<int> bytes) async {
    final directory = await getApplicationDocumentsDirectory();
    final savePath = '${directory.path}/$fontFamily${p.extension(url)}';

    final file = File(savePath);
    if (file.existsSync()) {
      file.deleteSync();
    }
    file.writeAsBytes(bytes);
  }
}

class FontVariant {
  final FontWeight fontWeight;
  final FontStyle fontStyle;

  /// [uri] can be is
  /// a url to download font, it is must be a http or https url,
  /// or asset path in your flutter project
  final String uri;

  const FontVariant({
    required this.fontWeight,
    required this.fontStyle,
    required this.uri,
  });

  VariantCompare toVariantCompare() => VariantCompare(
        fontWeight: fontWeight,
        fontStyle: fontStyle,
      );
}

class VariantCompare {
  final FontWeight fontWeight;
  final FontStyle fontStyle;

  VariantCompare({required this.fontWeight, required this.fontStyle});

  int computeMatch(VariantCompare b) {
    if (this == b) {
      return 0;
    }
    int score = (fontWeight.index - b.fontWeight.index).abs();
    if (fontStyle != b.fontStyle) {
      score += 2;
    }
    return score;
  }
}

extension on double {
  double get px => this * 0.16;
}
