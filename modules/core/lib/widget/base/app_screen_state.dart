// Flutter project by quanghuux<PERSON> (<EMAIL>)

// Friday, 26th May 2023 10:59 PM
// quanghuuxx (<EMAIL>)
// -----
// Copyright 2023 quanghuuxx, Ltd. All rights reserved.

import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../core.dart';

abstract class AppScreenState<T extends StatefulWidget> extends State<T> with ScreenStateMixin {}

mixin ScreenStateMixin<T extends StatefulWidget> on State<T> implements WidgetsBindingObserver, RouteAware {
  /// set after this first called or state updated
  ThemeData? _theme;
  ThemeData get theme {
    return _theme ??= Theme.of(context);
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((duration) {
      if (mounted) {
        onViewCreated();
      }
    });
  }

  @mustCallSuper
  void onViewCreated() {
    final router = NavigatorObsService.instance.currentRoute;
    if (router is PageRoute) {
      NavigatorObsService.instance.subscribe(this, router);
    }
  }

  void onResumed() {}

  void onPaused() {}

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // re-get theme data
    _theme = Theme.of(context);
  }

  @override
  void setState(VoidCallback fn) {
    if (!mounted) return;
    super.setState(fn);
  }

  @override
  void dispose() {
    _theme = null;
    WidgetsBinding.instance.removeObserver(this);
    NavigatorObsService.instance.unsubscribe(this);
    super.dispose();
  }

  @override
  void didPopNext() {}

  @override
  void didPush() {}

  @override
  void didPop() {}

  @override
  void didPushNext() {}

  @override
  Future<AppExitResponse> didRequestAppExit() async {
    return AppExitResponse.exit;
  }

  @override
  void handleCancelBackGesture() {}

  @override
  void handleCommitBackGesture() {}

  @override
  void handleUpdateBackGestureProgress(PredictiveBackEvent backEvent) {}

  @override
  Future<bool> didPopRoute() => Future<bool>.value(false);

  @override
  bool handleStartBackGesture(PredictiveBackEvent backEvent) => false;

  @override
  void didChangeMetrics() {}

  @override
  Future<bool> didPushRoute(String route) => Future<bool>.value(false);

  @override
  Future<bool> didPushRouteInformation(RouteInformation routeInformation) {
    return Future<bool>.value(false);
  }

  @override
  void didChangeTextScaleFactor() {}

  @override
  void didChangePlatformBrightness() {}

  @override
  void didChangeLocales(List<Locale>? locales) {}

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.resumed:
        onResumed();
        break;
      case AppLifecycleState.paused:
        onPaused();
        break;
      default:
    }
  }

  @override
  void didChangeViewFocus(ViewFocusEvent event) {}

  @override
  void didHaveMemoryPressure() {}

  @override
  void didChangeAccessibilityFeatures() {}
}
