// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/material.dart';

abstract class BaseButton extends StatelessWidget {
  const BaseButton({
    super.key,
    required this.text,
    this.style,
    this.onPressed,
    this.icon,
    this.iconAlignment = IconAlignment.start,
    this.padding = const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
    this.height,
    this.width,
    this.shape,
    this.maxLines = 1,
  });

  final String text;
  final TextStyle? style;
  final VoidCallback? onPressed;
  final Widget? icon;
  final IconAlignment iconAlignment;
  final EdgeInsets? padding;
  final double? height;
  final double? width;
  final OutlinedBorder? shape;
  final int maxLines;

  Size? get sized {
    if (width != null && height != null) {
      return Size(width!, height!);
    } else if (width != null) {
      return Size.fromWidth(width!);
    } else if (height != null) {
      return Size.fromHeight(height!);
    }

    return null;
  }
}
