// Flutter project by quanghu<PERSON><PERSON> (<EMAIL>)

import 'package:flutter/material.dart';

import 'base_button.dart';

class GhostButton extends BaseButton {
  const GhostButton({
    super.key,
    required super.text,
    super.style,
    super.onPressed,
    super.padding,
    super.icon,
    super.iconAlignment,
    super.height,
    super.width,
    super.shape,
    this.color,
    this.disabledColor,
  });

  final Color? color, disabledColor;

  @override
  Widget build(BuildContext context) {
    return TextButton.icon(
      onPressed: onPressed,
      style: TextButton.styleFrom(
        foregroundColor: color,
        disabledForegroundColor: disabledColor,
        fixedSize: sized,
        textStyle: style,
        padding: padding,
        shape: shape,
      ),
      label: Text(
        text,
        maxLines: maxLines,
        overflow: TextOverflow.ellipsis,
      ),
      icon: icon,
      iconAlignment: iconAlignment,
    );
  }
}
