// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/material.dart';

import 'base_button.dart';

class PrimaryButton extends BaseButton {
  const PrimaryButton({
    super.key,
    required super.text,
    super.style,
    super.onPressed,
    super.icon,
    super.padding,
    super.iconAlignment,
    super.height,
    super.width,
    super.shape,
    super.maxLines,
    this.color,
    this.disabledColor,
  });

  final Color? color, disabledColor;

  @override
  Widget build(BuildContext context) {
    return FilledButton.icon(
      onPressed: onPressed,
      label: Text(
        text,
        maxLines: maxLines,
        overflow: TextOverflow.ellipsis,
      ),
      style: FilledButton.styleFrom(
        backgroundColor: color,
        disabledBackgroundColor: disabledColor,
        fixedSize: sized,
        textStyle: style,
        padding: padding,
        shape: shape,
      ),
      icon: icon,
      iconAlignment: iconAlignment,
    );
  }
}
