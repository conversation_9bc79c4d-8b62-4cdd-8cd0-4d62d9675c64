// Flutter project by quang<PERSON><PERSON><PERSON> (<EMAIL>)

import 'package:flutter/material.dart';

import 'base_button.dart';

class SecondaryButton extends BaseButton {
  const SecondaryButton({
    super.key,
    required super.text,
    super.style,
    super.onPressed,
    super.padding,
    super.icon,
    super.iconAlignment,
    super.height,
    super.width,
    super.shape,
    super.maxLines,
  });

  @override
  Widget build(BuildContext context) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        fixedSize: sized,
        textStyle: style,
        padding: padding,
        shape: shape,
      ),
      label: Text(
        text,
        maxLines: maxLines,
        overflow: TextOverflow.ellipsis,
      ),
      icon: icon,
      iconAlignment: iconAlignment,
    );
  }
}
