// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/material.dart';

abstract class StateBase<T extends StatefulWidget> extends State<T> with BaseStateMixin {}

mixin BaseStateMixin<T extends StatefulWidget> on State<T> {
  ThemeData? _theme;
  ThemeData get theme {
    return _theme ??= Theme.of(context);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _theme = Theme.of(context);
  }

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((duration) {
      if (mounted) {
        onViewCreated();
      }
    });
  }

  void onViewCreated() {}

  @override
  void setState(VoidCallback fn) {
    if (!mounted) return;
    super.setState(fn);
  }

  @override
  void dispose() {
    _theme = null;
    super.dispose();
  }
}
