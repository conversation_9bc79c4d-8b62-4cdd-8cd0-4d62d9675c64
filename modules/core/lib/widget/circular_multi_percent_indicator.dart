// Sunday, 4th June 2023 09:30 PM
// quanghuuxx (<EMAIL>)
// -----
// Copyright 2023 quanghuuxx, Ltd. All rights reserved.

import 'dart:math';

import 'package:flutter/material.dart';

class CircularMultiPercentIndicator extends CustomPaint {
  CircularMultiPercentIndicator({
    super.key,
    super.child,
    required this.percents,
    required this.radius,
    this.strokeWidth = 2,
    this.space = 4,
    this.backgroundColor,
    this.strokeCap = StrokeCap.round,
  }) {
    percents.removeWhere((e) => e.percent == 0.0);
  }

  final double radius;
  final double strokeWidth;
  final double space;
  final Color? backgroundColor;
  final StrokeCap strokeCap;
  final List<CircularPercent> percents;

  @override
  Widget? get child => SizedBox.square(
        dimension: radius,
        child: Center(child: super.child),
      );

  @override
  Size get size => Size.square(radius);

  @override
  CustomPainter? get painter => _CircularPercentIndicatorPanter(chart: this);
}

class _CircularPercentIndicatorPanter extends CustomPainter {
  final CircularMultiPercentIndicator chart;

  _CircularPercentIndicatorPanter({
    required this.chart,
  }) {
    spaceInRadians = chart.space * _onePercentInRadians;
    percents = chart.percents
        .map(
          (e) => _PainterData(
            Paint()
              ..color = e.color
              ..style = PaintingStyle.stroke
              ..strokeWidth = chart.strokeWidth
              ..strokeCap = chart.strokeCap,
            (e.percent - chart.space) * _onePercentInRadians,
          ),
        )
        .toList();
  }

  late final List<_PainterData> percents;
  late final double spaceInRadians;

  static const _onePercentInRadians = 0.062831853071796;

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Offset.zero & size;

    double total = chart.percents.fold<double>(0, (sum, e) => sum += e.percent);

    int count = chart.percents.length;
    bool paintMissing = false;
    if (total.ceil() < 100) {
      count++;
      paintMissing = true;
    }

    double startAngle = (-90 * pi / 180) + spaceInRadians / 2;

    for (var i = 0; i < count; i++) {
      if (paintMissing && i == count - 1) {
        canvas.drawPath(
          Path()
            ..addArc(
              rect,
              startAngle,
              (100 - total - chart.space) * _onePercentInRadians,
            ),
          Paint()
            ..color = chart.backgroundColor ?? Colors.grey
            ..style = PaintingStyle.stroke
            ..strokeWidth = chart.strokeWidth
            ..strokeCap = chart.strokeCap,
        );
        return;
      }

      final data = percents[i];

      canvas.drawPath(
        Path()..addArc(rect, startAngle, data.radians),
        data.paint,
      );

      startAngle += data.radians + spaceInRadians;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}

class CircularPercent {
  final Color color;
  final double percent;

  CircularPercent({
    required this.color,
    required double percent,
  })  : assert(
          percent >= 0 && percent <= 1.0,
          'percent must be >= 0.0 and <= 1.0',
        ),
        percent = percent * 100;
}

class _PainterData {
  const _PainterData(this.paint, this.radians);

  final Paint paint;
  final double radians;
}
