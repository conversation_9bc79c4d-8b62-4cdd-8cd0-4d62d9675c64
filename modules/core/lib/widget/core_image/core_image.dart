// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

import '../../core.dart';

class CoreImage extends StatefulWidget {
  const CoreImage(
    this.data, {
    super.key,
    this.width,
    this.height,
    this.fit = BoxFit.contain,
    this.color,
    this.onLoaded,
    this.shape = BoxShape.rectangle,
    this.border,
    this.borderRadius,
  });

  final dynamic data;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Color? color;
  final ValueChanged<ImageInfo?>? onLoaded;
  final BoxShape shape;
  final Border? border;
  final BorderRadius? borderRadius;

  @override
  State<CoreImage> createState() => _CoreImageState();
}

class _CoreImageState<T> extends State<CoreImage> with TickerProviderStateMixin {
  final DateTime _time = DateTime.now();

  late final AnimationController controller;
  late final Animation<double> animation, reverseAnimation;

  ui.Image? _image;

  _LoadState _state = _LoadState.loading;

  @override
  void initState() {
    super.initState();

    controller = AnimationController(vsync: this);
    animation = CurvedAnimation(parent: controller, curve: Curves.easeIn);
    reverseAnimation = Tween(begin: 1.0, end: 0.0).animate(CurvedAnimation(parent: controller, curve: Curves.easeOut));

    controller.addListener(() {
      if (reverseAnimation.value <= 0.0) {
        setState(() {
          _state = _LoadState.done;
        });
      }
    });

    setLoadType();
  }

  @override
  void didUpdateWidget(covariant CoreImage oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.data != widget.data) {
      setLoadType();
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget child = Stack(
      fit: StackFit.passthrough,
      children: [
        if (reverseAnimation.value > 0.0)
          FadeTransition(
            opacity: reverseAnimation,
            child: const Skeleton(),
          ),
        if (_state == _LoadState.error)
          FadeTransition(
            opacity: animation,
            child: SizedBox(
              width: widget.width,
              height: widget.height,
              child: const Placeholder(),
            ),
          )
        else
          RawImage(
            image: _image,
            opacity: animation,
            width: widget.width,
            height: widget.height,
            fit: widget.fit,
            color: widget.color,
            alignment: Alignment.center,
          ),
      ],
    );

    if (widget.shape == BoxShape.circle) {
      child = ClipOval(child: child);
    } else if (widget.borderRadius != null) {
      child = ClipRRect(
        borderRadius: widget.borderRadius!,
        child: child,
      );
    }

    if (widget.border != null) {
      child = DecoratedBox(
        decoration: BoxDecoration(
          shape: widget.shape,
          border: widget.border,
          borderRadius: widget.borderRadius,
        ),
        position: DecorationPosition.foreground,
        child: child,
      );
    }

    return child;
  }

  void setLoadType() {
    _LoadType _type = _LoadType.none;
    controller.reset();
    if (widget.data is String) {
      if ((widget.data as String).startsWith('http')) {
        _type = _LoadType.network;
      } else if (isLocalUrl(widget.data as String)) {
        _type = _LoadType.file;
      } else {
        _type = _LoadType.assets;
      }
    } else if (widget.data is Uint8List) {
      _type = _LoadType.uint8list;
    }

    ImageProvider<Object> provider;
    switch (_type) {
      case _LoadType.assets:
        provider = AssetImage(widget.data as String);
        break;
      case _LoadType.uint8list:
        provider = MemoryImage(widget.data as Uint8List);
        break;
      case _LoadType.file:
        provider = FileImage(File(widget.data as String));
        break;
      case _LoadType.network:
        provider = CachedNetworkImageProvider(widget.data as String);
        break;
      default:
        throw Exception('Unknown image type: ${widget.data.runtimeType}');
    }

    provider.resolve(ImageConfiguration.empty).addListener(
          ImageStreamListener(
            (info, _) {
              if (mounted) {
                setState(() {
                  _image = info.image;
                  _state = _LoadState.success;
                  widget.onLoaded?.call(info);

                  _loaded();
                });
              }
            },
            onError: (exception, stackTrace) {
              if (mounted) {
                setState(() {
                  _state = _LoadState.error;
                  Console.log(
                    'Error load image of ${_type == _LoadType.uint8list ? 'Uint8List data' : widget.data}',
                    error: exception,
                    stackTrace: stackTrace,
                    name: 'CoreImage',
                  );

                  _loaded();
                });
              }
            },
          ),
        );
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  void _loaded() {
    Duration duration = DateTime.now().difference(_time);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (duration > kAnimationDurationMedium) duration = kAnimationDurationMedium;
      if (mounted) controller.animateTo(1.0, duration: duration);
    });
  }
}

enum _LoadType {
  assets,
  network,
  file,
  uint8list,
  none,
}

enum _LoadState {
  success,
  done,
  error,
  loading,
}
