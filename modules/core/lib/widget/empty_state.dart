// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/material.dart';

import '../core.dart';

class EmptyState extends StatelessWidget {
  static const String emptyStateLottie = 'packages/core/assets/json/empty_state.json';

  const EmptyState({
    super.key,
    this.lottiePath,
    this.title,
  });

  final String? lottiePath;
  final Widget? title;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (_, constraints) {
        double? width = constraints.maxWidth * .75;
        BoxFit fit = BoxFit.fitWidth;
        double? height;
        if (width > constraints.maxHeight) {
          height = constraints.maxHeight * .75;
          fit = BoxFit.fitHeight;
          width = null;
        }

        final lottie = LottieWidget(
          lottiePath ?? emptyStateLottie,
          width: width,
          height: height,
          fit: fit,
        );

        if (title == null) return Center(child: lottie);
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            lottie,
            const SizedBox(height: kFifteen),
            title!,
          ],
        );
      },
    );
  }
}
