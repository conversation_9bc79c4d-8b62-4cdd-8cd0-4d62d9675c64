// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:math' show min;

import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

import '../core.dart';

class LoadingWidget extends StatelessWidget {
  static const String loadingStateLottie = 'packages/core/assets/json/loading_state.json';

  final Animation<double>? animationController;
  final ValueChanged<LottieComposition>? onLoaded;

  const LoadingWidget({
    super.key,
    this.lottiePath,
    this.sizeRatio = 0.25,
    this.animationController,
    this.onLoaded,
  });

  final String? lottiePath;
  final double sizeRatio;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (_, constraints) {
        double sized = min(constraints.maxWidth * sizeRatio, 104);

        return Center(
          child: LottieWidget(
            lottiePath ?? loadingStateLottie,
            width: sized,
            height: sized,
            renderCache: RenderCache.drawingCommands,
            controller: animationController,
            onLoaded: onLoaded,
          ),
        );
      },
    );
  }
}
