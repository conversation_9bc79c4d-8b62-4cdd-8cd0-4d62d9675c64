// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:io';
import 'dart:typed_data';

import 'package:lottie/lottie.dart';

import '../helper.dart';

class LottieWidget extends LottieBuilder {
  final dynamic source;
  final LottieImageProviderFactory? imageProviderFactory;
  final Map<String, String>? headers;

  LottieWidget(
    this.source, {
    super.key,
    super.width,
    super.height,
    super.fit,
    super.alignment,
    super.delegates,
    super.options,
    super.repeat,
    super.onLoaded,
    super.controller,
    super.renderCache,
    this.imageProviderFactory,
    this.headers,
  }) : super(
          lottie: _provider(
            source,
            imageProviderFactory: imageProviderFactory,
            headers: headers,
          ),
        );

  static LottieProvider _provider(
    source, {
    LottieImageProviderFactory? imageProviderFactory,
    Map<String, String>? headers,
  }) {
    if (source is String) {
      if (source.startsWith('http')) {
        return NetworkLottie(
          source,
          headers: headers,
          imageProviderFactory: imageProviderFactory,
        );
      } else if (isLocalUrl(source)) {
        return FileLottie(
          File(source),
          imageProviderFactory: imageProviderFactory,
        );
      } else {
        return AssetLottie(
          source,
          imageProviderFactory: imageProviderFactory,
        );
      }
    } else if (source is Uint8List) {
      return MemoryLottie(
        source,
        imageProviderFactory: imageProviderFactory,
      );
    } else {
      throw UnimplementedError();
    }
  }
}
