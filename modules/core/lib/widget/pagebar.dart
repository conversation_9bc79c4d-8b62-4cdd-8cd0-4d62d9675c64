// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/material.dart';

import '../core.dart';

class Pagebar extends StatefulWidget {
  const Pagebar({
    super.key,
    this.height = 36,
    required this.controller,
    required this.itemCount,
    required this.itemBuilder,
    this.spacing = kEight,
    this.padding = kPaddingHorizontal16,
  });

  final double height, spacing;
  final PageController controller;
  final int itemCount;
  final EdgeInsets padding;

  /// Recommend to use [ActionChip]
  final Widget Function(BuildContext context, int index, bool selected) itemBuilder;

  @override
  State<Pagebar> createState() => _PagebarState();
}

class _PagebarState extends State<Pagebar> {
  final ValueNotifier<int> selectedIndex = ValueNotifier<int>(0);

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onChanged);
  }

  @override
  Widget build(BuildContext context) {
    final separated = SizedBox(width: widget.spacing);
    return SizedBox(
      height: widget.height,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        padding: widget.padding,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            widget.itemCount,
            (index) => _ChipItem(
              index: index,
              builder: widget.itemBuilder,
            ),
          ).separated((i, e) => e, separated: (i) => separated),
        ),
      ),
    );
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onChanged);
    selectedIndex.dispose();
    super.dispose();
  }

  void _onChanged() {
    final paging = widget.controller.page;
    if (paging == null) {
      return;
    }

    selectedIndex.value = paging.round();
  }
}

class PageChip extends StatelessWidget {
  const PageChip({
    super.key,
    required this.label,
    required this.selected,
    this.onPressed,
  });

  final Widget label;
  final bool selected;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return ActionChip(
      label: label,
      labelStyle: theme.textTheme.labelLarge?.copyWith(
        color: selected ? theme.colorScheme.onSecondary : Colors.black87,
      ),
      backgroundColor: selected ? theme.colorScheme.secondary : theme.dividerColor,
      side: BorderSide.none,
      onPressed: onPressed,
    );
  }
}

class _ChipItem extends StatelessWidget {
  const _ChipItem({
    required this.index,
    required this.builder,
  });

  final int index;
  final Widget Function(BuildContext context, int index, bool selected) builder;

  @override
  Widget build(BuildContext context) {
    final state = context.stateOf<_PagebarState>();
    final selected = context.listenable(state.selectedIndex).value == index;
    if (selected) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Scrollable.ensureVisible(context, duration: kAnimationDuration, alignment: 0.5);
      });
    }

    return builder(context, index, selected);
  }
}
