// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

import '../core.dart';

class Skeleton extends StatefulWidget {
  const Skeleton({
    super.key,
    this.width,
    this.height,
    this.colors,
    this.stops = _SkeletonState.kStops,
    this.begin = _SkeletonState.kBegin,
    this.end = _SkeletonState.kEnd,
    this.child,
  });

  final double? width;
  final double? height;
  final List<Color>? colors;
  final List<double> stops;
  final Alignment begin;
  final Alignment end;
  final Widget? child;

  @override
  State<Skeleton> createState() => _SkeletonState();
}

class _SkeletonState extends State<Skeleton> with SingleTickerProviderStateMixin {
  static const kColors = [
    Color(0xFFEEEEEE),
    Color(0xFFF4F4F4),
    Color(0xFFEEEEEE),
  ];
  static const kColorsDark = [
    Color(0xFF585858),
    Color(0xFF676767),
    Color(0xFF585858),
  ];
  static const kStops = [0.1, 0.4, 0.6];
  static const Alignment kBegin = Alignment(-1.0, -0.6);
  static const Alignment kEnd = Alignment(1.0, 0.6);

  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController.unbounded(vsync: this)
      ..repeat(min: -0.5, max: 1.5, period: const Duration(milliseconds: 1800));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return AnimatedBuilder(
      animation: _controller,
      builder: (BuildContext context, Widget? child) => _Skeleton(
        gradient: LinearGradient(
          colors: widget.colors ?? theme.byBrightness(light: kColors, dark: kColorsDark),
          stops: widget.stops,
          begin: widget.begin,
          end: widget.end,
          transform: _SlidingGradientTransform(slidePercent: _controller.value),
        ),
        percent: _controller.value,
        // ignore: sized_box_for_whitespace
        child: Container(
          width: widget.width,
          height: widget.height,
          child: widget.child ?? const ColoredBox(color: Colors.black),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}

@immutable
class _Skeleton extends SingleChildRenderObjectWidget {
  final double percent;
  final Gradient gradient;

  _Skeleton({
    super.child,
    required this.percent,
    required this.gradient,
  });

  @override
  _SkeletonFilter createRenderObject(BuildContext context) {
    return _SkeletonFilter(percent, gradient, context);
  }

  @override
  void updateRenderObject(BuildContext context, _SkeletonFilter shimmer) {
    shimmer
      ..percent = percent
      ..gradient = gradient
      .._context = context;
  }
}

class _SkeletonFilter extends RenderProxyBox {
  Gradient _gradient;
  double _percent;
  BuildContext _context;

  _SkeletonFilter(this._percent, this._gradient, this._context);

  @override
  ShaderMaskLayer? get layer => super.layer as ShaderMaskLayer?;

  @override
  bool get alwaysNeedsCompositing => child != null;

  set percent(double newValue) {
    if (newValue == _percent) {
      return;
    }
    _percent = newValue;
    markNeedsPaint();
  }

  set gradient(Gradient newValue) {
    if (newValue == _gradient) {
      return;
    }
    _gradient = newValue;
    markNeedsPaint();
  }

  set context(BuildContext newValue) {
    if (newValue == _context) {
      return;
    }
    _context = newValue;
    markNeedsPaint();
  }

  bool isSized(BuildContext context) => (context.findRenderObject() as RenderBox?)?.hasSize ?? false;

  @override
  void paint(PaintingContext context, Offset offset) {
    if (child != null) {
      assert(needsCompositing);

      if (!isSized(_context)) return;

      final double width = _context.size!.width;
      final double height = _context.size!.height;

      layer ??= ShaderMaskLayer();
      layer!
        ..shader = _gradient.createShader(
          Rect.fromLTWH(
            -offset.dx,
            -offset.dy,
            width,
            height,
          ),
        )
        ..maskRect = offset & size
        ..blendMode = BlendMode.srcATop;
      context.pushLayer(layer!, super.paint, offset);
    } else {
      layer = null;
    }
  }
}

class _SlidingGradientTransform extends GradientTransform {
  const _SlidingGradientTransform({
    required this.slidePercent,
  });

  final double slidePercent;

  @override
  Matrix4? transform(Rect bounds, {TextDirection? textDirection}) {
    return Matrix4.translationValues(bounds.width * slidePercent, 0.0, 0.0);
  }
}
