// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/material.dart';

import '../core.dart';

final toast = _Toast();

class _Toast {
  _Toast();

  OverlayEntry? _overlayEntry;

  void dissmiss() {
    if (_overlayEntry?.mounted == true) _overlayEntry!.remove();
    _overlayEntry?.dispose();
    _overlayEntry = null;
  }

  void show(
    BuildContext context, {
    message,
    Alignment begin = const Alignment(0, -2),
    Alignment end = const Alignment(0, -0.95),
    Duration duration = const Duration(seconds: 3),
    VoidCallback? onTap,
  }) {
    assert(message != null, 'message must not be null');

    if (_overlayEntry != null) {
      dissmiss();
    }

    _overlayEntry = OverlayEntry(
      builder: (context) {
        final theme = Theme.of(context);
        return _ToastAnimated(
          begin: begin,
          end: end,
          duration: duration,
          onDismiss: dissmiss,
          child: Dismissible(
            key: <PERSON><PERSON>ey(message.hashCode),
            onDismissed: (direction) => dissmiss(),
            child: Material(
              color: Colors.transparent,
              surfaceTintColor: Colors.transparent,
              child: message is Widget
                  ? message
                  : InkWell(
                      onTap: () {
                        dissmiss();
                        onTap?.call();
                      },
                      child: Container(
                        margin: const EdgeInsets.all(16),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surface,
                          borderRadius: BorderRadius.circular(8),
                          border: Border(
                            left: BorderSide(
                              color: Theme.of(context).colorScheme.secondary,
                              width: 4,
                            ),
                          ),
                          boxShadow: const [
                            BoxShadow(
                              color: Colors.black26,
                              blurRadius: 4,
                            ),
                          ],
                        ),
                        child: Text(
                          '$message',
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                          style: theme.textTheme.bodyMedium,
                        ),
                      ),
                    ),
            ),
          ),
        );
      },
    );

    Overlay.of(context).insert(_overlayEntry!);
  }
}

class _ToastAnimated extends StatefulWidget {
  const _ToastAnimated({
    required this.child,
    required this.begin,
    required this.end,
    required this.duration,
    required this.onDismiss,
  });

  final Widget child;
  final Alignment begin;
  final Alignment end;
  final Duration duration;
  final VoidCallback onDismiss;

  @override
  State<_ToastAnimated> createState() => __ToastAnimatedState();
}

class __ToastAnimatedState extends State<_ToastAnimated> with SingleTickerProviderStateMixin {
  late final AnimationController _controller = AnimationController(
    vsync: this,
    duration: kAnimationDurationSlow,
  );

  late final Animation<AlignmentGeometry> _animation = Tween<AlignmentGeometry>(
    begin: widget.begin,
    end: widget.end,
  ).animate(_controller);

  @override
  void initState() {
    super.initState();

    _controller
      ..addListener(() => setState(() {}))
      ..addStatusListener((state) {
        if (state == AnimationStatus.completed) {
          Future.delayed(widget.duration, () {
            if (mounted) {
              _controller.reverse();
            }
          });
        } else if (state == AnimationStatus.dismissed) {
          widget.onDismiss();
        }
      });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _controller.forward();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: MediaQuery.of(context).padding,
      child: Align(
        alignment: _animation.value,
        child: widget.child,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
