// Flutter project by quanghuuxx (<EMAIL>)

// Saturday, 1st July 2023 04:32 PM
// quanghuuxx (<EMAIL>)
// -----
// Copyright 2023 quanghuuxx, Ltd. All rights reserved.

import 'dart:math';

import 'package:flutter/material.dart';

import '../core.dart';

class WheelScrollable extends StatefulWidget {
  const WheelScrollable({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    this.infinity = true,
    this.itemExtent = 50,
    this.diameterRatio = 1.8,
    this.initIndex = 0,
    required this.onSelected,
    this.perspective = 0.009,
    this.magnification = 1.3,
    this.overAndUnderCenterOpacity = 0.5,
  });

  final int itemCount, initIndex;
  final double itemExtent, //
      diameterRatio,
      perspective,
      magnification,
      overAndUnderCenterOpacity;
  final bool infinity;
  final Widget Function(BuildContext context, int index) itemBuilder;
  final bool Function(int index) onSelected;

  @override
  State<WheelScrollable> createState() => _WheelScrollableState();
}

class _WheelScrollableState extends State<WheelScrollable> {
  late final FixedExtentScrollController _controller;
  late final DebounceValue<int> _selectedItemChanged;

  late ListWheelChildDelegate _delegate;

  int _lastSelectedIndex = -1;

  bool _dragging = false, _scrolling = false;

  @override
  void initState() {
    super.initState();

    _selectedItemChanged = DebounceValue(
      _lastSelectedIndex = widget.initIndex,
      duration: const Duration(
        milliseconds: 500,
      ),
    )..addListener((value) {
        _scrolling = false;
        _onChanged(value);
      });

    _controller = FixedExtentScrollController(initialItem: widget.initIndex);

    if (widget.infinity) {
      _delegate = ListWheelChildLoopingListDelegate(
        children: List.generate(
          widget.itemCount,
          (index) => widget.itemBuilder.call(context, index),
        ),
      );
    } else {
      _delegate = ListWheelChildBuilderDelegate(
        builder: widget.itemBuilder,
        childCount: widget.itemCount,
      );
    }
  }

  @override
  void didUpdateWidget(covariant WheelScrollable oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.itemCount != widget.itemCount) {
      if (_selectedItemChanged.value > widget.itemCount - 1) {
        _controller.jumpToItem(widget.itemCount - 1);
      }

      if (widget.infinity) {
        _delegate = ListWheelChildLoopingListDelegate(
          children: List.generate(
            widget.itemCount,
            (index) => widget.itemBuilder.call(context, index),
          ),
        );
      } else {
        _delegate = ListWheelChildBuilderDelegate(
          builder: widget.itemBuilder,
          childCount: widget.itemCount,
        );
      }
    }

    if (oldWidget.initIndex != widget.initIndex) {
      _controller.jumpToItem(widget.initIndex);
    }
  }

  @override
  void dispose() {
    _controller.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerDown: (_) {
        _dragging = true;
      },
      onPointerUp: (_) {
        _dragging = false;
        _onChanged(_selectedItemChanged.value);
      },
      onPointerCancel: (_) {
        _dragging = false;
      },
      child: ListWheelScrollView.useDelegate(
        itemExtent: widget.itemExtent,
        controller: _controller,
        useMagnifier: true,
        magnification: widget.magnification,
        perspective: widget.perspective,
        diameterRatio: widget.diameterRatio,
        overAndUnderCenterOpacity: widget.overAndUnderCenterOpacity,
        physics: const FixedExtentScrollPhysics(),
        onSelectedItemChanged: (index) {
          _scrolling = true;
          _selectedItemChanged.value = index;
        },
        childDelegate: _delegate,
      ),
    );
  }

  void _onChanged(int index) {
    if (_dragging || _scrolling) return;

    if (widget.onSelected.call(index)) {
      _lastSelectedIndex = index;
    } else {
      _controller.animateToItem(
        _lastSelectedIndex,
        duration: Duration(
          milliseconds: min(
            (_lastSelectedIndex - index).abs() * 100,
            300,
          ),
        ),
        curve: Curves.bounceInOut,
      );
    }
  }
}
