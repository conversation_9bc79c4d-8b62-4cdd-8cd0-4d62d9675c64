// Flutter project by quanghuuxx (<EMAIL>)

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

class WidgetBoundsWrapper extends SingleChildRenderObjectWidget {
  const WidgetBoundsWrapper({
    super.key,
    required this.onBoundsChanged,
    required super.child,
  });

  final ValueChanged<Rect> onBoundsChanged;

  @override
  RenderObject createRenderObject(BuildContext context) {
    return _WidgetSizeRenderBox(onBoundsChanged);
  }
}

class _WidgetSizeRenderBox extends RenderProxyBox {
  final ValueChanged<Rect> onBoundsChanged;

  _WidgetSizeRenderBox(this.onBoundsChanged);

  Rect? _bounds;

  @override
  void performLayout() {
    super.performLayout();

    final childBounds = child?.paintBounds;
    if (childBounds != null && childBounds != _bounds) {
      _bounds = childBounds;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        onBoundsChanged.call(childBounds);
      });
    }
  }
}
