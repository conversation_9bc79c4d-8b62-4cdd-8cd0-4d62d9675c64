name: core
description: A new Flutter package project.
version: 0.0.1
homepage:

environment:
  sdk: ^3.5.2

dependencies:
  flutter:
    sdk: flutter
  
  flutter_timezone: 3.0.1
  flutter_local_notifications: 19.0.0
  shared_preferences: 2.3.2
  cached_network_image: 3.4.1
  intl: 0.19.0
  uuid: 4.5.1
  lottie: 3.1.3
  rxdart: 0.28.0
  package_info_plus: 8.1.0
  device_info_plus: 11.1.0
  sqflite: 2.4.0
  path_provider: 2.1.5
  encrypt: 5.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  assets:
    - packages/core/assets/json/loading_state.json
    - packages/core/assets/json/empty_state.json

  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
