// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:async';
import 'dart:collection';

import 'package:flutter/widgets.dart';
import 'package:it/src/objects.dart';
import 'package:nested/nested.dart';

class It extends InheritedWidget {
  const It({super.key, required super.child});

  @override
  InheritedElement createElement() => _ItInheritedElement(this);

  @override
  bool updateShouldNotify(covariant InheritedWidget oldWidget) => false;
}

class _ItInheritedElement extends InheritedElement {
  _ItInheritedElement(super.widget);

  final StreamController<dynamic> _controller = StreamController.broadcast();

  final HashMap<Object, Stream> _streams = HashMap();
  final HashMap<BuildContext, HashMap<Object, ItSubcription>> _dependencies = HashMap(), _listens = HashMap();

  @override
  void updateDependencies(Element dependent, Object? aspect) {
    super.updateDependencies(dependent, aspect);

    assert(aspect is ItAspect, 'aspect must be ItAspect');
    aspect as ItAspect;

    Stream? stream = _streams[aspect.type];
    if (stream == null) {
      _streams[aspect.type] = stream = aspect.castStream(_controller);
    }

    final map = mapOf(aspect);
    final old = map[dependent]?[aspect.type];
    if (old != null) {
      old.close();
    }

    final value = old?.value ?? (stream as ValueStream).value;
    stream = aspect.transformStream(stream);

    final hash = map[dependent] ??= HashMap<Object, ItSubcription>();
    hash[aspect.type] = ItSubcription(
      stream.listen(null),
      value: value,
      onChanged: (old, value) {
        aspect.onChanged.call(old, value, dependent);
      },
    );
  }

  T? getOn<T>(BuildContext context) {
    final hash = _dependencies[context];
    if (hash == null || hash[T] == null) {
      throw StateError('Can not find ItSubcription');
    }

    return hash[T]!.value as T?;
  }

  T? get<T>() {
    for (final entry in _streams.entries) {
      if (entry.key == T) {
        return (entry.value as ValueStream<T>).value;
      }
    }
    return null;
  }

  @override
  void removeDependent(Element dependent) {
    final depend = _dependencies.remove(dependent);
    if (depend != null) {
      for (final sub in depend.values) {
        sub.close();
      }
      depend.clear();
    }

    final listen = _listens.remove(dependent);
    if (listen != null) {
      for (final sub in listen.values) {
        sub.close();
      }
      listen.clear();
    }
    super.removeDependent(dependent);
  }

  @override
  void unmount() {
    _controller.close();
    for (final depend in _dependencies.values) {
      for (final sub in depend.values) {
        sub.close();
      }
      depend.clear();
    }
    _dependencies.clear();
    for (final listen in _listens.values) {
      for (final sub in listen.values) {
        sub.close();
      }
      listen.clear();
    }
    _listens.clear();
    super.unmount();
  }

  HashMap<BuildContext, HashMap<Object, ItSubcription>> mapOf(ItAspect aspect) {
    return aspect is ItAspectListener ? _listens : _dependencies;
  }

  void add<T>(T value) {
    _controller.sink.add(value);
  }

  ValueStream<T> stream<T>() {
    ValueStream<T>? stream = _streams[T] as ValueStream<T>?;
    if (stream == null) {
      _streams[T] = stream = ValueStream<T>(_controller.stream);
    }
    return stream;
  }

  void putStreamIfAbsent<T>() {
    if (_streams.containsKey(T)) {
      return;
    }
    _streams[T] = ValueStream<T>(_controller.stream);
  }
}

extension ItExt on BuildContext {
  T? on<T>({ItTransformer<T>? transform}) {
    final ele = getElementForInheritedWidgetOfExactType<It>() as _ItInheritedElement?;
    if (ele == null) {
      throw StateError('Can not find It in scope of $this');
    }

    dependOnInheritedElement(ele, aspect: ItAspectBuilder<T>(transform: transform));
    return ele.getOn<T>(this);
  }

  T? get<T>({bool putTypeIfAbsent = true}) {
    final ele = getElementForInheritedWidgetOfExactType<It>() as _ItInheritedElement?;
    if (ele == null) {
      throw StateError('Can not find It in scope of $this');
    }

    if (putTypeIfAbsent) {
      ele.putStreamIfAbsent<T>();
    }

    return ele.get<T>();
  }

  void observe<T>(
    void Function(T? old, T value) onChanged, {
    ItTransformer<T>? transform,
  }) {
    final it = dependOnInheritedWidgetOfExactType<It>(
        aspect: ItAspectListener<T>(
      onChanged: onChanged,
      transform: transform,
    ));

    if (it == null) {
      throw StateError('Can not find It in scope of $this');
    }
  }

  void fire<T>(T value, {bool putTypeIfAbsent = false}) {
    final ele = getElementForInheritedWidgetOfExactType<It>() as _ItInheritedElement?;
    if (ele == null) {
      throw StateError('Can not find It in scope of $this');
    }

    if (putTypeIfAbsent) {
      ele.putStreamIfAbsent<T>();
    }
    ele.add(value);
  }

  ValueStream<T> stream<T>() {
    final ele = getElementForInheritedWidgetOfExactType<It>() as _ItInheritedElement?;
    if (ele == null) {
      throw StateError('Can not find It in scope of $this');
    }

    return ele.stream<T>();
  }
}

abstract class ViewModel {
  static Provider<T> create<T extends ViewModel>({
    Key? key,
    required Widget child,
    required T Function() create,
  }) {
    return Provider<T>._(
      key: key,
      delegate: ItCreate<T>(create: create),
      child: child,
    );
  }

  static Provider<T> value<T extends ViewModel>({
    Key? key,
    required Widget child,
    required T value,
  }) {
    return Provider<T>._(
      key: key,
      delegate: ItValue(value: value),
      child: child,
    );
  }

  static Nested multi({
    Key? key,
    required Widget child,
    required List<Provider> providers,
  }) {
    return Nested(
      key: key,
      children: providers,
      child: child,
    );
  }

  static T of<T extends ViewModel>(BuildContext context) {
    final it = context.findAncestorWidgetOfExactType<Provider<T>>();
    if (it == null) {
      throw StateError('Can not find ItViewProvider in scope of $context');
    }

    if (it._element == null) {
      throw StateError('$it not mounted');
    }

    return it._element!.value;
  }

  BuildContext? _owner;

  bool _isDisposed = false;

  bool get _mounted => _owner != null;

  final List<VoidCallback> _pendingTasks = [];

  void _attach(BuildContext context) {
    assert(!_isDisposed, 'Can`t attach a $runtimeType is disposed');

    _owner = context;

    // handle pending tasks
    for (int i = _pendingTasks.length - 1; i >= 0; i--) {
      _pendingTasks.removeAt(i).call();
    }
  }

  void _dettach(BuildContext context) {
    if (_owner == context) {
      _owner = null;
    }
  }

  T? get<T>({bool putTypeIfAbsent = true}) {
    assert(_mounted, '$runtimeType not mounted');
    return _owner!.get<T>(putTypeIfAbsent: putTypeIfAbsent);
  }

  void fire(dynamic value, {bool putTypeIfAbsent = false}) {
    _microtask(() {
      assert(_mounted, '$runtimeType not mounted');
      _owner!.fire(value, putTypeIfAbsent: putTypeIfAbsent);
    });
  }

  void listen<T>(
    void Function(T? old, T value) onChanged, {
    ItTransformer<T>? transform,
  }) {
    _microtask(() {
      assert(_mounted, '$runtimeType not mounted');
      _owner!.observe<T>(onChanged, transform: transform);
    });
  }

  @mustCallSuper
  void dispose() {
    _isDisposed = true;
    _owner = null;
  }

  void _microtask(void Function() task) {
    if (_mounted) {
      task();
    } else {
      _pendingTasks.insert(0, task);
    }
  }
}

// ignore: must_be_immutable
class Provider<T extends ViewModel> extends SingleChildStatelessWidget {
  final ItDelegate<T> delegate;

  _ProviderElement<T>? _element;

  Provider._({
    super.key,
    required super.child,
    required this.delegate,
  });

  @override
  SingleChildStatelessElement createElement() {
    return _ProviderElement<T>(this);
  }

  @override
  Widget buildWithChild(BuildContext context, Widget? child) {
    return child!;
  }
}

class _ProviderElement<T extends ViewModel> extends SingleChildStatelessElement {
  _ProviderElement(Provider<T> super.widget);

  late ItDelegate<T> delegate;

  @override
  Provider<T> get widget => super.widget as Provider<T>;

  @override
  void mount(Element? parent, Object? newSlot) {
    super.mount(parent, newSlot);
    widget._element = this;
    delegate = widget.delegate;
  }

  @override
  void update(covariant Provider<T> newWidget) {
    if (newWidget.delegate.runtimeType != widget.delegate.runtimeType) {
      throw StateError('Can not update provider, because rebuilt $widget using a different constructor.');
    }

    widget._element = null;
    if (newWidget.delegate is ItValue<T>) {
      // dettach old value
      delegate.instance._dettach(this);
      // set current delegate to new value
      delegate = newWidget.delegate;
    }

    super.update(newWidget);
    newWidget._element = this;
  }

  @override
  void unmount() {
    widget._element = null;
    delegate.close();
    super.unmount();
  }

  T get value {
    final it = delegate.instance;
    if (!it._mounted) {
      it._attach(this);
    }
    return it;
  }
}
