// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:it/src/component.dart';

typedef ItTransformer<T> = Stream<T> Function(Stream<T> stream);

sealed class ItAspect<T> {
  final Object type;
  final void Function(dynamic old, dynamic value, Element element) onChanged;
  final ItTransformer<T>? transform;

  ItAspect({
    required this.onChanged,
    required this.transform,
  }) : type = T;

  Stream<T> castStream(StreamController controller) {
    return ValueStream<T>(controller.stream);
  }

  Stream<T> transformStream(Stream<T> stream) {
    if (transform != null) {
      return transform!(stream);
    }
    return stream;
  }
}

class ItAspectBuilder<T> extends ItAspect<T> {
  ItAspectBuilder({
    required super.transform,
  }) : super(onChanged: (_, __, element) => element.markNeedsBuild());
}

class ItAspectListener<T> extends ItAspect<T> {
  ItAspectListener({
    required void Function(T? old, T value) onChanged,
    required super.transform,
  }) : super(onChanged: (old, value, _) => onChanged.call(old, value));
}

class ItSubcription<T> {
  final StreamSubscription<T> subscription;

  T? _value;
  T? get value => _value;

  ItSubcription._({required this.subscription, T? value}) : _value = value;

  factory ItSubcription(
    StreamSubscription<T> subscription, {
    T? value,
    void Function(T? old, T value)? onChanged,
  }) {
    final instance = ItSubcription._(subscription: subscription, value: value);

    subscription.onData((event) {
      if (instance._value == event) {
        return;
      }

      final old = instance._value;
      instance._value = event;
      onChanged?.call(old, event);
    });

    return instance;
  }

  void close() {
    subscription.cancel();
  }
}

class ValueStream<T> extends Stream<T> {
  final Stream<T> _stream;

  T? _value;
  T? get value => _value;

  ValueStream(Stream source) : _stream = source.where((v) => v is T).cast<T>() {
    _stream.listen((event) => _value = event);
  }

  @override
  StreamSubscription<T> listen(
    void Function(T event)? onData, {
    Function? onError,
    void Function()? onDone,
    bool? cancelOnError,
  }) {
    return _stream.listen(
      onData,
      onError: onError,
      onDone: onDone,
      cancelOnError: cancelOnError,
    );
  }
}

sealed class ItDelegate<T> {
  T get instance;

  Type get type => T;

  bool get isCreated;

  void close();
}

class ItCreate<T extends ViewModel> extends ItDelegate<T> {
  final T Function() create;

  ItCreate({required this.create});

  T? _instance;

  @override
  T get instance {
    _instance ??= create();
    return _instance!;
  }

  @override
  bool get isCreated => _instance != null;

  @override
  void close() {
    if (_instance != null) {
      _instance!.dispose();
      _instance = null;
    }
  }
}

class ItValue<T extends ViewModel> extends ItDelegate<T> {
  final T value;

  ItValue({required this.value});

  @override
  T get instance => value;

  @override
  bool get isCreated => true;

  @override
  void close() {
    // not implemented
  }
}
