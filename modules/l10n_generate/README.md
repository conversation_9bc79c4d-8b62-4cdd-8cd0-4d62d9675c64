<!--
This README describes the package. If you publish this package to pub.dev,
this README's contents appear on the landing page for your package.

For information about how to write a good package README, see the guide for
[writing package pages](https://dart.dev/tools/pub/writing-package-pages).

For general information about developing packages, see the Dart guide for
[creating packages](https://dart.dev/guides/libraries/create-packages)
and the Flutter guide for
[developing packages and plugins](https://flutter.dev/to/develop-packages).
-->
## Features

- Xử lý dịch và generate ra các file json chứa ngôn ngữ tương ứng
- Xuất ra 1 file dart chứa các key localization, tương ứng với các giá trị key trong file json

## Getting started

- <PERSON><PERSON> thể cấu hình các config bằng cách tạo file `l10n_config.yaml` tạo thư mục root (ngang hàng với file pubspec.yaml) với format như sau:

```yaml
# Liệt kê các ngôn ngữ muốn dịch 
target_locales:
  - ja
  - fr
  - zh-cn
  - vi

# Đường dẫn tới thư mục chứa các file json được dịch
output_dir: assets

# Tên class dart chứa các key localization
dart_lkey_name: LKey

# Đường dẫn tới file dart key localization bên trên
output_file_dart_lkey: lib/resources/l10n/lkey.dart

# Danh sách các nguồn dịch gốc
sources:
  - 
    source: assets/lang_en.json # Đường dẫn tới file json chứa nội dung nguồn cần dịch
    source_locale: en # Ngôn ngữ tương ứng của file json nguồn bên trên
    prefix_json_name: "lang" # giá trị String được dùng để đặt ở đầu của tên file json 
    suffix_json_name: "" # giá trị String được đặt ở cuối của tên của file json (không bắt buộc)
  - 
    source: assets/error_lang_en.json
    source_locale: en
    prefix_json_name: "error_lang"
    suffix_json_name: ""
```

## Usage

- Import package l10n_generate vào project
- Đảm bảo đã tạo sẵn file `l10n_config.yaml` với các config chuẩn trong thư mục root
- Chạy lệnh `dart run l10n_generate` để dịch và gen các file json, file dart localization
- Mặc định lệnh `dart run l10n_generate` sẽ chỉ dịch các key mới được thêm vào, còn các key cũ nếu có thay đổi nội dung và muốn dịch lại thì cần thêm lệnh `-refresh <args>` vào sau để xử lý dịch, với `<args>` có thể là:
 
`all` (dịch lại toàn bộ các file ngôn ngữ)
```
dart run l10n_generate -refresh all
```

hoặc `lang_a,lang_b,...,lang_n` chỉ định rõ các ngôn ngữ muốn dịch lại
```
dart run l10n_generate -refresh vi,fr,ja
```
