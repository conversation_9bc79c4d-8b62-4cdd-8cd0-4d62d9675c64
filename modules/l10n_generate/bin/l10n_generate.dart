// Flutter project by quanghuuxx (<EMAIL>)

import 'dart:convert';
import 'dart:io';

import 'package:translator/translator.dart';
import 'package:yaml/yaml.dart';

final translator = GoogleTranslator();
const String spliter = '\n    ';

void main(List<String> args) async {
  final yaml = loadYaml(File('./l10n_config.yaml').readAsStringSync());

  final locales = List.of(yaml['target_locales']);
  final sources = List.of(yaml['sources']);

  const encoder = JsonEncoder.withIndent('  ');
  final refresh = _arguments(args, '-refresh')?.split(',');

  final Set<String> keys = {};

  for (final map in sources) {
    final file = File(map['source'] as String);
    if (!file.existsSync()) {
      continue;
    }

    final source = jsonDecode(file.readAsStringSync()) as Map<String, dynamic>;
    keys.addAll(source.keys);

    for (final language in locales) {
      Map<String, String> translate = Map<String, String>.from(source);
      Map<String, String> result = <String, String>{
        'last_modified': DateTime.now().toIso8601String(),
      }..addAll(source.cast());

      String prefix = map['prefix_json_name'] ?? '';
      if (prefix.isNotEmpty) {
        prefix = '${prefix}_';
      }

      String suffix = map['suffix_json_name'] ?? '';
      if (suffix.isNotEmpty) {
        suffix = '_$suffix';
      }

      final file = File('${yaml['output_dir']}/$prefix$language$suffix.json');
      if (file.existsSync()) {
        final exist = jsonDecode(file.readAsStringSync()) as Map<String, dynamic>;
        if (refresh == null || (!refresh.contains('all') && !refresh.contains(language))) {
          final keys = exist.keys;
          for (final key in keys) {
            translate.remove(key);
          }
          if (translate.isEmpty) {
            stdout.writeln('[skip] `${file.path}` already exist, and no need to update!');
            continue;
          }
          result = Map<String, String>.from(exist)..['last_modified'] = DateTime.now().toIso8601String();
        }
        file.deleteSync();
      }
      file.createSync();

      final values = await _translating(translate.values, from: map['source_locale']!, to: language);

      for (int i = 0; i < values.length; i++) {
        final key = translate.keys.elementAt(i);
        result[key] = values[i].trim();
      }

      file.writeAsStringSync(encoder.convert(result));
      stdout.writeln('[success] export to ${file.path}');
    }
  }

  final lkey = File(yaml['output_file_dart_lkey'] as String);

  if (keys.isNotEmpty) {
    String str = '''
// This file is generated by l10n_generate(created by quanghuu.xx).
//
// Last modified: ${DateTime.now().toIso8601String()}

// ignore_for_file: constant_identifier_names
abstract class ${yaml['dart_lkey_name']} {''';
    for (final key in keys) {
      str += '\n';
      str += '  static const String $key = \'$key\';';
    }

    str += '\n}';

    if (lkey.existsSync()) {
      lkey.deleteSync();
    }

    lkey.createSync(recursive: true);
    lkey.writeAsStringSync(str);
    stdout.writeln('[success] export dart file lkey to ${lkey.path}');
  }
}

String? _arguments(List<String> args, String key) {
  for (int i = 0; i < args.length; i++) {
    if (args[i] == key) {
      return args[i + 1];
    }
  }
  return null;
}

Future<List<String>> _translating(
  Iterable<String> values, {
  required String from,
  required String to,
  int maxLength = 3000,
}) async {
  String source = '', distination = '';
  for (final element in values) {
    final length = element.length;
    if (length > maxLength) {
      throw Exception('Contains a value string longer than $maxLength characters');
    }

    if (source.length + length > maxLength) {
      distination += spliter;
      final response = await translator.translate(source, from: from, to: to);
      distination += response.toString();
      source = '';
    }
    if (source.isNotEmpty) {
      source += spliter;
    }
    source += element;
  }

  if (source.isNotEmpty) {
    final response = await translator.translate(source, from: from, to: to);
    distination += response.toString();
  }

  return distination.split(spliter);
}
