name: pic_sketch
description: "A new Flutter project."
publish_to: 'none'
version: 0.1.0

environment:
  sdk: ^3.5.2

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations: 
    sdk: flutter

  permission_handler: 11.3.1
  get_it: 8.0.2
  photo_manager: 3.6.3
  equatable: 2.0.7
  pull_to_refresh_flutter3: 2.0.2
  json_annotation: 4.9.0
  image: 4.3.0
  flutter_image_compress: 2.4.0
  svg_path_parser: ^1.1.2
  purchases_flutter: 8.7.1
  flutter_image_gallery_saver: 0.0.2
  flutter_windowmanager_plus: 1.0.1

  # Firebase
  firebase_core: 3.13.0
  firebase_remote_config: 5.4.3
  firebase_analytics: 11.4.5
  firebase_crashlytics: 4.3.5

  # riverpod
  flutter_riverpod: 2.6.1
  riverpod_annotation: 2.6.1

  # objectbox
  objectbox: 4.1.0
  objectbox_flutter_libs: 4.1.0
  
  # dart run core:generate_assets -i assets/icons -o lib/resource/icon_constants.dart
  core:
    path: modules/core
  it:
    path: modules/it
  # dart run l10n_generate -refresh all
  l10n_generate:
    path: modules/l10n_generate

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0

  riverpod_generator: 2.6.3
  objectbox_generator: 4.1.0

  # dart run build_runner build --delete-conflicting-outputs
  json_serializable: 6.9.0
  build_runner: 2.4.13

flutter:
  uses-material-design: true
  shaders:
    - shaders/image_filter_fragment.frag
  assets:
    - assets/l10n/
    - assets/icons/
    - assets/shapes/
    - assets/images/
    - assets/jsons/
    - assets/fonts/Caveat/
    - assets/fonts/Cookie/
    - assets/fonts/DynaPuff/
    - assets/fonts/Gaegu/
    - assets/fonts/Inter/
    - assets/fonts/Jua/
    - assets/fonts/Moirai_One/
    - assets/fonts/Ole/
    - assets/fonts/Outfit/
    - assets/fonts/Puppies_Play/
    - assets/fonts/Rampart_One/
    - assets/fonts/Rouge_Script/
    - assets/fonts/Silkscreen/
    - assets/fonts/Mustachio_CF/
    - assets/fonts/Rainbow_Ribbon_CF/
    - assets/fonts/Vintage_Grovee/
    - assets/fonts/Blackout_2am/
    - assets/fonts/Baloo2/
    - assets/fonts/Ice_Age/
    # Environment (only enc files)
    - env/production.enc
    - env/development.enc
