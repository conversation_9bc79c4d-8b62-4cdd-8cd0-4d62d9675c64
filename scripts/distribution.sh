#!/bin/bash

cd android || {
  echo "Không thể chuyển đến thư mục android"
  exit 1
}

if [ -z "$1" ]; then
  echo "Tham số flavor không được cung cấp, mặc định build flavor dev"
fi

flavor=$1

if [ "$flavor" = "onepait" ] || [ "$flavor" = "one" ]; then
  fastlane distribution_onepait
elif [ "$flavor" = "production" ] || [ "$flavor" = "prod" ]; then
  fastlane distribution_prod
else
  fastlane distribution_dev
fi