#include <flutter/runtime_effect.glsl>
precision mediump float;

out vec4 fragColor;

uniform sampler2D inputImageTexture;

layout(location = 0) uniform lowp float inputBrightness;
layout(location = 1) uniform lowp float inputExposure;
layout(location = 2) uniform float inputTemperature;
layout(location = 3) uniform lowp float inputShadows;
layout(location = 4) uniform highp float inputContrast;
layout(location = 5) uniform float inputSaturation;
layout(location = 6) uniform float inputHighlights;
layout(location = 7) uniform lowp float inputVibrance;
layout(location = 8) uniform lowp float inputTint;
layout(location = 9) uniform vec2 screenSize;


// Điều chỉnh độ sáng
vec4 processBrightness(vec4 sourceColor){
    if(inputBrightness == 0.00) {
        return sourceColor;
    }
    sourceColor.rgb += vec3(inputBrightness * sourceColor.a);
    return sourceColor;
}

// Điều chỉnh tương phản
const lowp float avgLuminance = 0.5; // Gi<PERSON> trị trung bình độ sáng
vec4 processContrast(vec4 sourceColor){
    if(inputContrast == 1.00) {
        return sourceColor;
    }
    sourceColor.rgb = inputContrast * (sourceColor.rgb - avgLuminance) + avgLuminance;
    return sourceColor;
}

// Điều chỉnh độ bão hòa
const mediump vec3 luminanceWeightingHS = vec3(0.299, 0.587, 0.114); // Công thức chuẩn cho độ sáng
vec4 processSaturation(vec4 sourceColor){
    if(inputSaturation == 1.00) {
        return sourceColor;
    }
   lowp float luminance = dot(sourceColor.rgb, luminanceWeightingHS);
   lowp vec3 greyScaleColor = vec3(luminance);
   sourceColor.rgb = mix(greyScaleColor, sourceColor.rgb, inputSaturation);
   return sourceColor;
}

vec4 processVibrance(vec4 sourceColor){
   if(inputVibrance == 0.0) {
        return sourceColor;
   }
   lowp float luminance = dot(sourceColor.rgb, luminanceWeightingHS);
   lowp vec3 greyScaleColor = vec3(luminance);

    // Tính độ bão hòa hiện tại dựa trên khoảng cách màu
    lowp float maxComponent = max(sourceColor.r, max(sourceColor.g, sourceColor.b));
    lowp float minComponent = min(sourceColor.r, min(sourceColor.g, sourceColor.b));
    lowp float saturation = maxComponent - minComponent;

    // Áp dụng hiệu ứng Vibrance với điều chỉnh phi tuyến
    lowp float vibranceFactor = 1.0 + (inputVibrance * pow(1.0 - saturation, 2.0));
    sourceColor.rgb = mix(greyScaleColor, sourceColor.rgb, vibranceFactor);

    // Đảm bảo giá trị màu nằm trong khoảng [0, 1]
    sourceColor.rgb = clamp(sourceColor.rgb, 0.0, 1.0);
    return sourceColor;
}

vec4 processExposure(vec4 sourceColor){
    if(inputExposure == 1.00) {
        return sourceColor;
    }
    sourceColor.rgb *= inputExposure;
    return sourceColor;
}
vec4 processWhiteBalance(vec4 sourceColor){
    if(inputTemperature == 0.0 && inputTint == 0.00) {
        return sourceColor;
    }

    // Chuyển đổi nhiệt độ màu với đường cong phi tuyến
    float tempFactor = sign(inputTemperature) * pow(abs(inputTemperature) * 0.1, 1.2);
    vec3 warmFilter = vec3(0.93, 0.54, 0.0);
    vec3 coolFilter = vec3(0.0, 0.54, 0.93);
    vec3 tintColor = inputTemperature > 0.0 ? warmFilter : coolFilter;
    
    // Áp dụng nhiệt độ màu
    sourceColor.rgb = mix(sourceColor.rgb, sourceColor.rgb * tintColor, abs(tempFactor));

    // Chuyển đổi tint với điều chỉnh màu xanh lá
    float tintFactor = inputTint * 0.1;
    vec3 tintAdjust = vec3(1.0 + tintFactor, 1.0 - tintFactor, 1.0);
    sourceColor.rgb *= tintAdjust;

    // Đảm bảo giá trị màu nằm trong khoảng [0, 1]
    sourceColor.rgb = clamp(sourceColor.rgb, 0.0, 1.0);
    return sourceColor;
}


// https://stackoverflow.com/questions/26511037/how-can-i-modify-this-webgl-fragment-shader-to-increase-brightness-of-highlights
const mediump vec3 luminanceWeighting = vec3(0.299, 0.587, 0.114);
vec4 processHighlightAndShadows(vec4 sourceColor){
    if(inputShadows == 1.00 && inputHighlights == 1.00) {
        return sourceColor;
    }

    mediump float luminance = dot(sourceColor.rgb, luminanceWeighting);
    
    // Cải thiện xử lý highlight với đường cong mượt hơn
    mediump float highlightAdjustment = 2.0 - inputHighlights;
    mediump float highlight = max(0.0, luminance - 0.5) * highlightAdjustment;
    
    // Cải thiện xử lý shadow với đường cong mượt hơn
    mediump float shadowAdjustment = mix(1.0, 0.3, 1.0 - inputShadows);
    mediump float shadow = min(luminance, 0.5) * shadowAdjustment;
    
    // Kết hợp hiệu ứng
    mediump float adjustment = (shadow + highlight) * 2.0;
    vec3 result = mix(sourceColor.rgb, sourceColor.rgb * (1.0 + adjustment), 0.7);
    
    // Áp dụng tone mapping đơn giản để tránh clipping
    result = result / (1.0 + result);
    
    // Điều chỉnh độ tương phản cuối cùng
    result = mix(sourceColor.rgb, result, max(abs(inputHighlights - 1.0), abs(inputShadows - 1.0)));
    
    sourceColor.rgb = clamp(result, 0.0, 1.0);
    return sourceColor;
}

void main(){
	vec2 textureCoordinate = FlutterFragCoord().xy / screenSize;
	vec4 textureColor = texture(inputImageTexture, textureCoordinate);
	
    textureColor = processBrightness(textureColor);
    textureColor = processContrast(textureColor);
    textureColor = processSaturation(textureColor);
    textureColor = processVibrance(textureColor);
    textureColor = processExposure(textureColor);
    textureColor = processWhiteBalance(textureColor);
    textureColor = processHighlightAndShadows(textureColor);

	fragColor = textureColor;
}